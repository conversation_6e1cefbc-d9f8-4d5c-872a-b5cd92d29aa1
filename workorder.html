<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="/styles/common.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/moment/latest/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏容器 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">首页</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="text-blue-500 hover:text-blue-600">工单配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">工单管理</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="flex items-center space-x-1 text-gray-500 hover:text-blue-600">
                            <i class="ri-refresh-line text-xl"></i>
                            <span>刷新</span>
                        </button>
                        <div class="h-5 w-px bg-gray-300"></div>
                        <div class="flex items-center space-x-3">
                            <img src="https://via.placeholder.com/36" class="w-8 h-8 rounded-full border-2 border-blue-100" alt="用户头像">
                            <span class="text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 搜索区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="grid grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm text-gray-600 mb-2">所属项目</label>
                            <select class="w-full border rounded-lg p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                <option>全部</option>
                                <option>项目A</option>
                                <option>项目B</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-600 mb-2">故障地址</label>
                            <select class="w-full border rounded-lg p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                <option>全部</option>
                                <option>位置A</option>
                                <option>位置B</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-600 mb-2">报修人</label>
                            <input type="text" class="w-full border rounded-lg p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" placeholder="请输入报修人">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-600 mb-2">处理人</label>
                            <input type="text" class="w-full border rounded-lg p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" placeholder="请输入处理人">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-600 mb-2">工单创建时间</label>
                            <input type="text" id="daterange" class="w-full border rounded-lg p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" placeholder="选择时间范围">
                        </div>
                        <div class="flex items-end space-x-4">
                            <button class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-search-line"></i>
                                <span>搜索</span>
                            </button>
                            <button class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                                <i class="ri-refresh-line"></i>
                                <span>重置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="grid grid-cols-4 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow hover-scale">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-gray-500">待处理工单</h3>
                            <div class="w-12 h-12 bg-yellow-50 rounded-lg flex items-center justify-center">
                                <i class="ri-time-line text-2xl text-yellow-500"></i>
                            </div>
                        </div>
                        <div class="text-3xl font-bold text-yellow-500">12</div>
                        <div class="text-sm text-gray-500 mt-2">较昨日 +2</div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow hover-scale">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-gray-500">处理中工单</h3>
                            <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                                <i class="ri-loader-4-line text-2xl text-blue-500"></i>
                            </div>
                        </div>
                        <div class="text-3xl font-bold text-blue-500">8</div>
                        <div class="text-sm text-gray-500 mt-2">较昨日 -1</div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow hover-scale">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-gray-500">已完成工单</h3>
                            <div class="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                                <i class="ri-checkbox-circle-line text-2xl text-green-500"></i>
                            </div>
                        </div>
                        <div class="text-3xl font-bold text-green-500">45</div>
                        <div class="text-sm text-gray-500 mt-2">较昨日 +5</div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow hover-scale">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-gray-500">已取消工单</h3>
                            <div class="w-12 h-12 bg-gray-50 rounded-lg flex items-center justify-center">
                                <i class="ri-close-circle-line text-2xl text-gray-500"></i>
                            </div>
                        </div>
                        <div class="text-3xl font-bold text-gray-500">3</div>
                        <div class="text-sm text-gray-500 mt-2">较昨日 +0</div>
                    </div>
                </div>

                <!-- 修改操作按钮区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6 flex justify-between items-center">
                    <div class="flex space-x-4">
                        <button onclick="toggleAddWorkOrderDrawer()" 
                            class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                            <i class="ri-add-line text-lg"></i>
                            <span>新增工单</span>
                        </button>
                        <button class="border border-red-500 text-red-500 h-10 px-4 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2">
                            <i class="ri-delete-bin-line text-lg"></i>
                            <span>批量删除</span>
                        </button>
                    </div>

                    <!-- 右侧视图切换按钮组 -->
                    <div class="flex rounded-lg border overflow-hidden">
                        <button onclick="toggleView('card')" 
                            class="h-10 px-4 flex items-center space-x-1 bg-white hover:bg-gray-50 transition-colors border-r" 
                            id="cardViewBtn">
                            <i class="ri-layout-grid-line text-lg"></i>
                            <span class="text-sm">卡片视图</span>
                        </button>
                        <button onclick="toggleView('list')" 
                            class="h-10 px-4 flex items-center space-x-1 bg-white hover:bg-gray-50 transition-colors" 
                            id="listViewBtn">
                            <i class="ri-list-check-2 text-lg"></i>
                            <span class="text-sm">列表视图</span>
                        </button>
                    </div>
                </div>

                <!-- 新增工单抽屉 -->
                <div id="addWorkOrderDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
                    <!-- 背景遮罩 -->
                    <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddWorkOrderDrawer()"></div>
                    
                    <!-- 抽屉内容 -->
                    <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
                        <div class="h-full flex flex-col">
                            <!-- 抽屉头部 -->
                            <div class="px-6 py-4 border-b flex justify-between items-center">
                                <h3 class="text-xl font-medium">新增工单</h3>
                                <button onclick="toggleAddWorkOrderDrawer()" class="text-gray-400 hover:text-gray-500">
                                    <i class="ri-close-line text-2xl"></i>
                                </button>
                            </div>

                            <!-- 抽屉内容 -->
                            <div class="flex-1 overflow-y-auto p-6">
                                <form id="addWorkOrderForm" class="space-y-6">
                                    <!-- 所属空间 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">所属空间 <span class="text-red-500">*</span></label>
                                        <select name="space" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                            <option value="">请选择空间</option>
                                            <option value="1">A栋1层</option>
                                            <option value="2">A栋2层</option>
                                            <option value="3">B栋1层</option>
                                        </select>
                                    </div>

                                    <!-- 故障设备 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">故障设备 <span class="text-red-500">*</span></label>
                                        <select name="device" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                            <option value="">请选择设备</option>
                                            <option value="1">温度传感器-01</option>
                                            <option value="2">湿度传感器-02</option>
                                            <option value="3">光照传感器-03</option>
                                        </select>
                                    </div>

                                    <!-- 故障点位 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">故障点位</label>
                                        <select name="faultPoint" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择点位</option>
                                            <option value="1">温度</option>
                                            <option value="2">湿度</option>
                                            <option value="3">光照</option>
                                        </select>
                                    </div>

                                    <!-- 故障类型 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">故障类型</label>
                                        <select name="faultType" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择故障类型</option>
                                            <option value="1">设备离线</option>
                                            <option value="2">数据异常</option>
                                            <option value="3">硬件损坏</option>
                                        </select>
                                    </div>

                                    <!-- 故障描述 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">故障描述</label>
                                        <textarea name="description" rows="4" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    </div>

                                    <!-- 报修人 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">报修人</label>
                                        <input type="text" name="reporter" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <!-- 报修人电话 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">报修人电话</label>
                                        <input type="tel" name="reporterPhone" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <!-- 故障图片 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">故障图片（最多5张）</label>
                                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg">
                                            <div class="space-y-1 text-center">
                                                <i class="ri-upload-cloud-2-line text-3xl text-gray-400"></i>
                                                <div class="flex text-sm text-gray-600">
                                                    <label for="fault-images" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                                        <span>上传图片</span>
                                                        <input id="fault-images" name="fault-images" type="file" class="sr-only" accept="image/*" multiple onchange="previewImages(this)">
                                                    </label>
                                                    <p class="pl-1">或拖拽图片到此处</p>
                                                </div>
                                                <p class="text-xs text-gray-500">PNG, JPG, GIF 最大 10MB</p>
                                            </div>
                                        </div>
                                        <div id="image-preview" class="mt-3 grid grid-cols-3 gap-2"></div>
                                        <p id="image-count" class="text-xs text-gray-500 mt-1">已选择 0 张图片</p>
                                    </div>
                                </form>
                            </div>

                            <!-- 抽屉底部 -->
                            <div class="px-6 py-4 border-t bg-gray-50">
                                <div class="flex justify-end space-x-4">
                                    <button onclick="toggleAddWorkOrderDrawer()" class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                                        取消
                                    </button>
                                    <button onclick="submitAddWorkOrder()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                        提交
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工单列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工单编号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报修人</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报修人电话</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属空间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">处理人</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">处理人电话</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">WO2024031501</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13800138000</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">A栋1层</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">13900139000</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 10:30</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-3 py-1 text-yellow-600 bg-yellow-50 rounded-full text-sm font-medium">
                                        待处理
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800">处理</button>
                                    <button class="text-green-600 hover:text-green-800">分配</button>
                                    <button class="text-gray-600 hover:text-gray-800">详情</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script src="./js/common.js"></script>
    <script>
        $(function() {
            $('#daterange').daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });
        });

        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            setActiveMenuItem(window.location.pathname);
        });

        // 工单抽屉控制
        function toggleAddWorkOrderDrawer() {
            const drawer = document.getElementById('addWorkOrderDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 图片预览功能
        function previewImages(input) {
            const previewContainer = document.getElementById('image-preview');
            const imageCountElement = document.getElementById('image-count');
            const maxImages = 5;
            
            // 检查已有图片数量
            const existingImages = previewContainer.querySelectorAll('.image-preview-item').length;
            const newImagesCount = input.files.length;
            const totalImages = existingImages + newImagesCount;
            
            // 如果超过最大数量，显示警告并返回
            if (totalImages > maxImages) {
                alert(`最多只能上传${maxImages}张图片`);
                input.value = ''; // 清空选择
                return;
            }
            
            // 预览新选择的图片
            for (let i = 0; i < input.files.length; i++) {
                const file = input.files[i];
                
                // 检查文件类型
                if (!file.type.match('image.*')) {
                    continue;
                }
                
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'image-preview-item relative';
                    
                    imageDiv.innerHTML = `
                        <img src="${e.target.result}" class="h-24 w-full object-cover rounded-lg">
                        <button type="button" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center" onclick="removeImage(this)">
                            <i class="ri-close-line"></i>
                        </button>
                    `;
                    
                    previewContainer.appendChild(imageDiv);
                    
                    // 更新图片计数
                    updateImageCount();
                };
                
                reader.readAsDataURL(file);
            }
        }

        // 移除图片
        function removeImage(button) {
            button.closest('.image-preview-item').remove();
            updateImageCount();
        }

        // 更新图片计数
        function updateImageCount() {
            const previewContainer = document.getElementById('image-preview');
            const imageCountElement = document.getElementById('image-count');
            const count = previewContainer.querySelectorAll('.image-preview-item').length;
            
            imageCountElement.textContent = `已选择 ${count} 张图片`;
        }

        // 提交添加工单
        function submitAddWorkOrder() {
            const form = document.getElementById('addWorkOrderForm');
            const formData = new FormData(form);
            
            // 获取空间和设备的显示文本
            const spaceSelect = form.querySelector('select[name="space"]');
            const deviceSelect = form.querySelector('select[name="device"]');
            const faultPointSelect = form.querySelector('select[name="faultPoint"]');
            
            const spaceText = spaceSelect.options[spaceSelect.selectedIndex].text;
            const deviceText = deviceSelect.options[deviceSelect.selectedIndex].text;
            const faultPointText = faultPointSelect.options[faultPointSelect.selectedIndex].text;
            
            // 创建新的工单行
            const newRow = `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">WO-${new Date().getTime().toString().slice(-6)}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${spaceText}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${deviceText}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${faultPointText}</td>
                    <td class="px-6 py-4">${formData.get('description').substring(0, 50)}${formData.get('description').length > 50 ? '...' : ''}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${new Date().toLocaleString()}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 bg-yellow-50 text-yellow-600 rounded-full text-sm">待处理</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                        <button class="text-blue-600 hover:text-blue-800">查看</button>
                        <button class="text-green-600 hover:text-green-800">处理</button>
                        <button class="text-red-600 hover:text-red-800">删除</button>
                    </td>
                </tr>
            `;
            
            // 添加到表格
            document.querySelector('table tbody').insertAdjacentHTML('afterbegin', newRow);
            
            // 关闭抽屉并重置表单
            toggleAddWorkOrderDrawer();
            form.reset();
            document.getElementById('image-preview').innerHTML = '';
        }

        // 添加拖放功能
        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.querySelector('.border-dashed');
            const fileInput = document.getElementById('fault-images');
            
            if (dropZone && fileInput) {
                // 阻止默认拖放行为
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    dropZone.addEventListener(eventName, preventDefaults, false);
                });
                
                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                // 高亮显示拖放区域
                ['dragenter', 'dragover'].forEach(eventName => {
                    dropZone.addEventListener(eventName, highlight, false);
                });
                
                ['dragleave', 'drop'].forEach(eventName => {
                    dropZone.addEventListener(eventName, unhighlight, false);
                });
                
                function highlight() {
                    dropZone.classList.add('border-blue-500', 'bg-blue-50');
                    dropZone.classList.remove('border-gray-300');
                }
                
                function unhighlight() {
                    dropZone.classList.remove('border-blue-500', 'bg-blue-50');
                    dropZone.classList.add('border-gray-300');
                }
                
                // 处理拖放的文件
                dropZone.addEventListener('drop', handleDrop, false);
                
                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;
                    
                    fileInput.files = files;
                    previewImages(fileInput);
                }
            }
        });
    </script>
</body>
</html> 