<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备详情 - AE86</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <!-- 页面布局 -->
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>

                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-group-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
+                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
+                        <a href="personal.html" class="flex items-center w-full">
+                            <i class="ri-user-settings-line mr-2"></i> 个人配置
+                        </a>
+                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 p-6 overflow-y-auto">
            <!-- 设备详情头部 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold">温度传感器-01</h1>
                    <p class="text-gray-500">设备编码: DEVICE_001</p>
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center">
                        <i class="ri-edit-line mr-2"></i>
                        编辑设备
                    </button>
                    <div class="relative" id="deviceMenu">
                        <button onclick="toggleDeviceMenu()" class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center">
                            <span>更多操作</span>
                            <i class="ri-arrow-down-s-line ml-2 device-arrow transition-transform"></i>
                        </button>
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10" id="deviceMenu">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="ri-restart-line mr-2"></i>重启设备
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="ri-delete-bin-line mr-2"></i>删除设备
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备信息卡片 -->
            <div class="bg-white p-6 rounded-lg shadow mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">设备类型</h3>
                        <p class="mt-1 text-lg">温度传感器</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">所属网关</h3>
                        <p class="mt-1 text-lg">网关-001</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">所属空间</h3>
                        <p class="mt-1 text-lg">A栋1层</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">设备状态</h3>
                        <p class="mt-1 text-lg"><span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">在线</span></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">最后活跃时间</h3>
                        <p class="mt-1 text-lg">2024-03-15 14:30:00</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">报警状态</h3>
                        <p class="mt-1 text-lg"><span class="px-2 py-1 bg-red-50 text-red-600 rounded-full text-sm">有报警</span></p>
                    </div>
                </div>
            </div>

            <!-- 标签页导航 -->
            <div class="mb-6">
                <div class="border-b">
                    <div class="flex">
                        <button class="tab-button px-6 py-3 bg-blue-500 text-white" onclick="showTab('points')">点位</button>
                        <button class="tab-button px-6 py-3 text-gray-600" onclick="showTab('alarm')">报警</button>
                        <button class="tab-button px-6 py-3 text-gray-600" onclick="showTab('task')">任务</button>
                        <button class="tab-button px-6 py-3 text-gray-600" onclick="showTab('rule')">规则</button>
                        <button class="tab-button px-6 py-3 text-gray-600" onclick="showTab('workorder')">工单</button>
                    </div>
                </div>
            </div>

            <!-- 点位标签页内容 -->
            <div id="points" class="tab-content">
                <!-- 点位搜索区域 -->
                <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
                    <div class="flex items-center">
                        <div class="relative flex-1">
                            <input id="pointSearchInput" type="text" placeholder="搜索点位名称" class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
                        </div>
                        <button onclick="showPointsCompare()" class="ml-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center">
                            <i class="ri-line-chart-line mr-2"></i>
                            点位曲线对比
                        </button>
                    </div>
                </div>

                <!-- 点位列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位标识</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前值</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">temperature</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-sm">上报</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">数值</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">26.5°C</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <a href="point-detail.html" class="hover:text-blue-800">查看</a>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">湿度</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">humidity</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-sm">上报</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">数值</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45%</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <a href="point-detail.html" class="hover:text-blue-800">查看</a>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">开关状态</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">switch</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">下发</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">开关</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">开</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button onclick="showPointHistory('switch', '开关状态')" class="hover:text-blue-800">查看</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 报警标签页内容 -->
            <div id="alarm" class="tab-content hidden">
                <!-- 报警子选项卡 -->
                <div class="border-b mb-4">
                    <div class="flex">
                        <button class="alarm-tab-button px-4 py-2 bg-blue-500 text-white" onclick="showAlarmTab('history')">报警历史</button>
                        <button class="alarm-tab-button px-4 py-2 text-gray-600" onclick="showAlarmTab('rules')">报警规则</button>
                    </div>
                </div>

                <!-- 报警历史 -->
                <div id="alarm-history" class="alarm-tab-content">
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警点位</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警等级</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警规则</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警内容</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">温度传感器</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 py-1 bg-red-50 text-red-600 rounded-full text-xs">紧急</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">温度过高报警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">温度超过阈值（35°C）</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-14 09:15:00</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">湿度传感器</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 py-1 bg-yellow-50 text-yellow-600 rounded-full text-xs">警告</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">湿度过低报警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">湿度低于阈值（20%）</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 报警规则 -->
                <div id="alarm-rules" class="alarm-tab-content hidden">
                    <!-- 报警规则操作栏 -->
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">设备报警规则</h3>
                        <button onclick="toggleAddAlarmRuleDrawer()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center">
                            <i class="ri-add-line mr-2"></i>
                            新增报警规则
                        </button>
                    </div>

                    <!-- 报警规则列表 -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <table class="min-w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警等级</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度过高报警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 py-1 bg-red-50 text-red-600 rounded-full text-xs">紧急</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                        <button class="text-blue-600 hover:text-blue-800">查看</button>
                                        <button class="text-green-600 hover:text-green-800">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">湿度过低报警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 py-1 bg-yellow-50 text-yellow-600 rounded-full text-xs">警告</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-14 09:15:00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                        <button class="text-blue-600 hover:text-blue-800">查看</button>
                                        <button class="text-green-600 hover:text-green-800">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 任务标签页内容 -->
            <div id="task" class="tab-content hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium">设备相关任务</h3>
                    <button onclick="toggleAddTaskDrawer()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center">
                        <i class="ri-add-line mr-2"></i>
                        新建任务
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <table class="min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务编号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TASK-2024-0001</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">每日温度检测任务</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">定时任务</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-xs">运行中</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800">查看</button>
                                    <button class="text-green-600 hover:text-green-800">编辑</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TASK-2024-0002</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度过高自动调节</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">条件任务</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-16 09:15:00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-xs">运行中</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800">查看</button>
                                    <button class="text-green-600 hover:text-green-800">编辑</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 规则标签页内容 -->
            <div id="rule" class="tab-content hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium">设备相关规则</h3>
                    <button onclick="toggleAddRuleDrawer()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center">
                        <i class="ri-add-line mr-2"></i>
                        新增规则
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <table class="min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则编号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RULE-2024-0001</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度数据格式转换</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">数据上报预处理</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                    <button class="text-blue-600 hover:text-blue-800">查看</button>
                                    <button class="text-green-600 hover:text-green-800">编辑</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RULE-2024-0002</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">湿度数据过滤</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">数据上报预处理</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-16 09:15:00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                    <button class="text-blue-600 hover:text-blue-800">查看</button>
                                    <button class="text-green-600 hover:text-green-800">编辑</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 工单标签页内容 -->
            <div id="workorder" class="tab-content hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium">设备相关工单</h3>
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center">
                        <i class="ri-add-line mr-2"></i>
                        新建工单
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <table class="min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工单编号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工单标题</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">WO-2024-0001</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器异常检修</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 py-1 bg-yellow-50 text-yellow-600 rounded-full text-xs">处理中</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-800">查看</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增报警规则抽屉 -->
    <div id="addAlarmRuleDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddAlarmRuleDrawer()"></div>

        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 drawer">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">新增报警规则</h3>
                    <button onclick="toggleAddAlarmRuleDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addRuleForm" class="space-y-6">
                        <!-- 规则名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                            <input type="text" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备选择 - 默认选中当前设备 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" disabled>
                                <option selected>温度传感器-01 (DEVICE_001)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">已默认选择当前设备</p>
                        </div>

                        <!-- 点位阈值设置 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">点位阈值设置</label>
                            <div class="space-y-4" id="thresholdList">
                                <div class="flex items-center space-x-2">
                                    <select class="border rounded-lg px-3 py-2 w-24">
                                        <option>AND</option>
                                        <option>OR</option>
                                    </select>
                                    <select class="border rounded-lg px-3 py-2 w-32">
                                        <option>温度</option>
                                        <option>湿度</option>
                                    </select>
                                    <select class="border rounded-lg px-3 py-2 w-40">
                                        <option>大于</option>
                                        <option>小于</option>
                                        <option>大于等于</option>
                                        <option>小于等于</option>
                                        <option>在...之间</option>
                                        <option>不在...之间</option>
                                    </select>
                                    <input type="number" class="border rounded-lg px-3 py-2 w-24" placeholder="数值">
                                    <button type="button" onclick="removeThreshold(this)"
                                        class="text-red-500 hover:text-red-700">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" onclick="addThreshold()"
                                class="mt-2 text-blue-500 hover:text-blue-700 flex items-center">
                                <i class="ri-add-line mr-1"></i> 添加条件
                            </button>
                        </div>

                        <!-- 重复次数 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">重复次数</label>
                            <select class="w-full border rounded-lg px-3 py-2">
                                <option>1次</option>
                                <option>2次</option>
                                <option>3次</option>
                            </select>
                        </div>

                        <!-- 报警频次 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">报警频次</label>
                            <div class="flex items-center space-x-2">
                                <input type="number" class="border rounded-lg px-3 py-2 w-24" min="1">
                                <span>分钟</span>
                            </div>
                        </div>

                        <!-- 通知组 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">通知组</label>
                            <select class="w-full border rounded-lg px-3 py-2">
                                <option>请选择通知组</option>
                            </select>
                        </div>

                        <!-- 报警时段 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">报警时段</label>
                            <div class="border rounded-lg p-4 max-h-48 overflow-y-auto">
                                <div class="grid grid-cols-4 gap-2">
                                    <label class="flex items-center">
                                        <span class="w-16">00:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">01:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <!-- 更多时间选项... -->
                                    <label class="flex items-center">
                                        <span class="w-16">23:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 报警上限次数 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">报警上限次数</label>
                            <input type="number" class="w-full border rounded-lg px-3 py-2" min="1">
                        </div>

                        <!-- 报警等级 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">报警等级</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="alarmLevel" class="form-radio text-blue-500">
                                    <span class="ml-2">普通报警</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="alarmLevel" class="form-radio text-blue-500">
                                    <span class="ml-2">重要报警</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="alarmLevel" class="form-radio text-blue-500">
                                    <span class="ml-2">紧急报警</span>
                                </label>
                            </div>
                        </div>

                        <!-- 规则描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则描述</label>
                            <textarea class="w-full border rounded-lg px-3 py-2 h-32 resize-none"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <button onclick="submitAddAlarmRule()"
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                        <button onclick="toggleAddAlarmRuleDrawer()"
                            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增设备任务抽屉 -->
    <div id="addTaskDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddTaskDrawer()"></div>

        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 drawer">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">新建设备任务</h3>
                    <button onclick="toggleAddTaskDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addTaskForm" class="space-y-6">
                        <!-- 任务名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">任务名称</label>
                            <input type="text" name="taskName" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备选择 - 默认选中当前设备 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备</label>
                            <select name="deviceId" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" disabled>
                                <option selected>温度传感器-01 (DEVICE_001)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">已默认选择当前设备</p>
                        </div>

                        <!-- 任务类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">任务类型</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="taskType" value="timer" class="form-radio text-blue-500" checked onchange="toggleTaskTypeSettings()">
                                    <span class="ml-2">定时任务</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="taskType" value="condition" class="form-radio text-blue-500" onchange="toggleTaskTypeSettings()">
                                    <span class="ml-2">条件任务</span>
                                </label>
                            </div>
                        </div>

                        <!-- 定时任务设置 -->
                        <div id="timerTaskSettings">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">执行计划</label>
                                <select name="scheduleType" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleScheduleType()">
                                    <option value="once">单次执行</option>
                                    <option value="daily">每天执行</option>
                                    <option value="weekly">每周执行</option>
                                    <option value="monthly">每月执行</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">执行时间</label>
                                <input type="datetime-local" name="executeTime" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div id="customSchedule" class="mt-4 hidden">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Cron 表达式</label>
                                <input type="text" name="cronExpression" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="* * * * *">
                                <p class="text-xs text-gray-500 mt-1">遵循标准 Cron 表达式格式</p>
                            </div>
                        </div>

                        <!-- 条件任务设置 -->
                        <div id="conditionTaskSettings" class="hidden">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">触发条件</label>
                                <div class="space-y-4" id="taskConditionList">
                                    <div class="flex items-center space-x-2">
                                        <select class="border rounded-lg px-3 py-2 w-24">
                                            <option>AND</option>
                                            <option>OR</option>
                                        </select>
                                        <select class="border rounded-lg px-3 py-2 w-32">
                                            <option>温度</option>
                                            <option>湿度</option>
                                        </select>
                                        <select class="border rounded-lg px-3 py-2 w-40">
                                            <option>大于</option>
                                            <option>小于</option>
                                            <option>等于</option>
                                            <option>不等于</option>
                                            <option>大于等于</option>
                                            <option>小于等于</option>
                                        </select>
                                        <input type="number" class="border rounded-lg px-3 py-2 w-24" placeholder="数值">
                                        <button type="button" onclick="removeTaskCondition(this)"
                                            class="text-red-500 hover:text-red-700">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" onclick="addTaskCondition()"
                                    class="mt-2 text-blue-500 hover:text-blue-700 flex items-center">
                                    <i class="ri-add-line mr-1"></i> 添加条件
                                </button>
                            </div>
                        </div>

                        <!-- 执行动作 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">执行动作</label>
                            <select name="actionType" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleActionType()">
                                <option value="setProperty">设置点位值</option>
                                <option value="callService">调用服务</option>
                                <option value="sendNotification">发送通知</option>
                            </select>
                        </div>

                        <!-- 点位设置 -->
                        <div id="setPropertyAction">
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">选择点位</label>
                                <select name="propertyId" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="switch">开关状态</option>
                                    <option value="mode">工作模式</option>
                                </select>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">设置值</label>
                                <input type="text" name="propertyValue" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 服务调用 -->
                        <div id="callServiceAction" class="hidden">
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">选择服务</label>
                                <select name="serviceId" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="restart">重启设备</option>
                                    <option value="selfCheck">设备自检</option>
                                    <option value="calibrate">设备校准</option>
                                </select>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">服务参数</label>
                                <textarea name="serviceParams" class="w-full border rounded-lg px-3 py-2 h-20 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder='{"param1": "value1"}'></textarea>
                            </div>
                        </div>

                        <!-- 通知发送 -->
                        <div id="sendNotificationAction" class="hidden">
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">通知组</label>
                                <select name="notificationGroup" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="ops">运维组</option>
                                    <option value="admin">管理员组</option>
                                </select>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">通知内容</label>
                                <textarea name="notificationContent" class="w-full border rounded-lg px-3 py-2 h-20 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>

                        <!-- 任务描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                            <textarea name="taskDescription" class="w-full border rounded-lg px-3 py-2 h-32 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <button onclick="submitAddTask()"
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                        <button onclick="toggleAddTaskDrawer()"
                            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增设备规则抽屉 -->
    <div id="addRuleDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddRuleDrawer()"></div>

        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 drawer">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">新增设备规则</h3>
                    <button onclick="toggleAddRuleDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addDeviceRuleForm" class="space-y-6">
                        <!-- 规则名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                            <input type="text" name="ruleName" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备选择 - 默认选中当前设备 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备</label>
                            <select name="deviceId" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" disabled>
                                <option selected>温度传感器-01 (DEVICE_001)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">已默认选择当前设备</p>
                        </div>

                        <!-- 规则类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则类型</label>
                            <select name="ruleType" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="property_report">属性上报预处理</option>
                                <option value="property_set">属性下发预处理</option>
                                <option value="event_report">事件上报预处理</option>
                            </select>
                        </div>

                        <!-- 规则条件 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则条件</label>
                            <div class="space-y-4" id="ruleConditionList">
                                <div class="flex items-center space-x-2">
                                    <select class="border rounded-lg px-3 py-2 w-24">
                                        <option>AND</option>
                                        <option>OR</option>
                                    </select>
                                    <select class="border rounded-lg px-3 py-2 w-32">
                                        <option>温度</option>
                                        <option>湿度</option>
                                    </select>
                                    <select class="border rounded-lg px-3 py-2 w-40">
                                        <option>大于</option>
                                        <option>小于</option>
                                        <option>等于</option>
                                        <option>不等于</option>
                                        <option>大于等于</option>
                                        <option>小于等于</option>
                                    </select>
                                    <input type="text" class="border rounded-lg px-3 py-2 w-24" placeholder="数值">
                                    <button type="button" onclick="removeRuleCondition(this)"
                                        class="text-red-500 hover:text-red-700">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" onclick="addRuleCondition()"
                                class="mt-2 text-blue-500 hover:text-blue-700 flex items-center">
                                <i class="ri-add-line mr-1"></i> 添加条件
                            </button>
                        </div>

                        <!-- 规则动作 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则动作</label>
                            <select name="actionType" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="transform">数据转换</option>
                                <option value="filter">数据过滤</option>
                                <option value="enrich">数据增强</option>
                                <option value="forward">数据转发</option>
                            </select>
                        </div>

                        <!-- 脚本配置 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">脚本配置</label>
                            <div class="border rounded-lg overflow-hidden">
                                <div class="bg-gray-100 px-4 py-2 flex justify-between items-center">
                                    <span class="text-sm font-medium">JavaScript</span>
                                    <div class="flex space-x-2">
                                        <button type="button" class="text-gray-500 hover:text-gray-700">
                                            <i class="ri-fullscreen-line"></i>
                                        </button>
                                        <button type="button" class="text-gray-500 hover:text-gray-700">
                                            <i class="ri-file-copy-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <textarea name="scriptContent" rows="8" class="w-full px-4 py-3 font-mono text-sm focus:outline-none" placeholder="// 输入处理脚本
function process(input) {
  // 在此处理数据
  return input;
}"></textarea>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">通过 JavaScript 函数处理数据流</p>
                        </div>

                        <!-- 规则描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则描述</label>
                            <textarea name="ruleDescription" class="w-full border rounded-lg px-3 py-2 h-32 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <button onclick="submitAddRule()"
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                        <button onclick="toggleAddRuleDrawer()"
                            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 点位曲线对比抽屉 -->
    <div id="pointsCompareDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="togglePointsCompareDrawer()"></div>

        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-6xl w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">点位曲线对比</h3>
                    <button onclick="togglePointsCompareDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容区域 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <!-- 点位选择区域 -->
                    <div id="pointSelectionArea" class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-medium">选择对比点位</h4>
                            <div class="text-sm text-gray-500">
                                已选择 <span id="selectedPointsCount">0</span> 个点位 (最多选择5个)
                            </div>
                        </div>

                        <!-- 搜索和筛选 -->
                        <div class="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="relative">
                                <input id="comparePointSearchInput" type="text" placeholder="搜索点位名称"
                                    class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
                            </div>
                            <div class="relative">
                                <select id="deviceFilterSelect" class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">所有设备</option>
                                    <!-- 设备选项将通过JavaScript动态生成 -->
                                </select>
                                <i class="ri-device-line absolute left-3 top-2.5 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- 点位列表 -->
                        <div class="bg-gray-50 rounded-lg p-4 max-h-80 overflow-y-auto">
                            <div id="comparePointsList" class="space-y-3">
                                <!-- 点位选择项将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 已选择的点位 -->
                        <div id="selectedPointsArea" class="mt-4 hidden">
                            <h5 class="text-md font-medium mb-2">已选择的点位</h5>
                            <div id="selectedPointsList" class="flex flex-wrap gap-2">
                                <!-- 已选择的点位标签将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 时间范围选择 -->
                        <div class="mt-6 grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                                <input type="datetime-local" id="compareStartTime" class="w-full border rounded-lg px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                                <input type="datetime-local" id="compareEndTime" class="w-full border rounded-lg px-3 py-2">
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <button onclick="clearSelectedPoints()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                清空选择
                            </button>
                            <button onclick="startPointsCompare()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600" disabled id="startCompareBtn">
                                开始对比
                            </button>
                        </div>
                    </div>

                    <!-- 对比结果区域 -->
                    <div id="compareResultArea" class="hidden">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-medium">曲线对比结果</h4>
                            <div class="flex space-x-2">
                                <button onclick="exportCompareChart()" class="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center">
                                    <i class="ri-download-line mr-1"></i>
                                    导出图表
                                </button>
                                <button onclick="cancelPointsCompare()" class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 flex items-center">
                                    <i class="ri-close-line mr-1"></i>
                                    取消对比
                                </button>
                            </div>
                        </div>

                        <!-- 图例区域 -->
                        <div id="chartLegend" class="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex flex-wrap gap-4" id="legendItems">
                                <!-- 图例项将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 图表区域 -->
                        <div class="bg-white rounded-lg shadow p-4">
                            <div id="compareChart" class="w-full h-96"></div>
                        </div>

                        <!-- 数据统计 -->
                        <div id="compareStats" class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- 统计信息将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 点位历史记录抽屉 -->
    <div id="pointHistoryDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="togglePointHistoryDrawer()"></div>

        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-4xl w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium"><span id="pointHistoryTitle">点位</span> 历史记录</h3>
                    <button onclick="togglePointHistoryDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <!-- 时间范围选择 -->
                    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                        <div class="flex flex-wrap items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium text-gray-700">开始时间:</label>
                                <input type="datetime-local" id="historyStartTime" class="border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium text-gray-700">结束时间:</label>
                                <input type="datetime-local" id="historyEndTime" class="border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <button onclick="queryPointHistory()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                查询
                            </button>
                            <div class="flex space-x-2 ml-auto">
                                <button onclick="exportPointHistory('csv')" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                                    导出CSV
                                </button>
                                <button onclick="exportPointHistory('excel')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                                    导出Excel
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 视图切换 -->
                    <div class="mb-6 flex justify-between items-center">
                        <h4 class="text-lg font-medium">历史数据</h4>
                        <div class="flex bg-gray-100 rounded-lg overflow-hidden">
                            <button id="chartViewBtn" onclick="switchHistoryView('chart')" class="px-4 py-2 bg-blue-500 text-white">
                                曲线图
                            </button>
                            <button id="tableViewBtn" onclick="switchHistoryView('table')" class="px-4 py-2 bg-white text-gray-700">
                                列表
                            </button>
                        </div>
                    </div>

                    <!-- 曲线图视图 -->
                    <div id="chartView" class="bg-white rounded-lg shadow p-4 mb-6">
                        <div id="historyChart" class="w-full h-80"></div>
                    </div>

                    <!-- 列表视图 -->
                    <div id="tableView" class="bg-white rounded-lg shadow hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上报值</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上报时间</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 历史数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                        <!-- 分页 -->
                        <div class="px-6 py-3 flex items-center justify-between border-t">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    上一页
                                </button>
                                <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    下一页
                                </button>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium" id="totalRecords">50</span> 条记录
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="ri-arrow-left-s-line"></i>
                                        </button>
                                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            1
                                        </button>
                                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            2
                                        </button>
                                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                            3
                                        </button>
                                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            4
                                        </button>
                                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            5
                                        </button>
                                        <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="ri-arrow-right-s-line"></i>
                                        </button>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 控制选项卡 -->
    <script>
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.add('hidden'));
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('bg-blue-500', 'text-white');
                button.classList.add('text-gray-600');
            });
            document.getElementById(tabName).classList.remove('hidden');
            event.target.classList.remove('text-gray-600');
            event.target.classList.add('bg-blue-500', 'text-white');
        }

        function showAlarmTab(tabName) {
            document.querySelectorAll('.alarm-tab-content').forEach(tab => tab.classList.add('hidden'));
            document.querySelectorAll('.alarm-tab-button').forEach(button => {
                button.classList.remove('bg-blue-500', 'text-white');
                button.classList.add('text-gray-600');
            });
            document.getElementById('alarm-' + tabName).classList.remove('hidden');
            event.target.classList.remove('text-gray-600');
            event.target.classList.add('bg-blue-500', 'text-white');
        }

        function toggleDeviceMenu() {
            const menu = document.getElementById('deviceMenu');
            const arrow = document.querySelector('.device-arrow');
            menu.classList.toggle('hidden');
            arrow.classList.toggle('rotate-180');
        }

        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置设备配置菜单为展开状态
            const deviceConfigItem = document.querySelector('.nav-item:nth-child(3)');
            deviceConfigItem.classList.add('active');
        });

        // 显示点位历史记录
        function showPointHistory(pointId, pointName) {
            // 设置标题
            document.getElementById('pointHistoryTitle').textContent = pointName;

            // 设置默认时间范围（过去24小时）
            const endTime = new Date();
            const startTime = new Date(endTime);
            startTime.setHours(startTime.getHours() - 24);

            document.getElementById('historyStartTime').value = formatDatetimeLocal(startTime);
            document.getElementById('historyEndTime').value = formatDatetimeLocal(endTime);

            // 存储当前点位ID
            currentPointId = pointId;

            // 打开抽屉
            togglePointHistoryDrawer();

            // 查询历史数据
            queryPointHistory();
        }

        // 切换历史记录抽屉
        function togglePointHistoryDrawer() {
            const drawer = document.getElementById('pointHistoryDrawer');
            const drawerContent = drawer.querySelector('.max-w-4xl');

            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 切换历史记录视图（曲线图/列表）
        function switchHistoryView(viewType) {
            const chartView = document.getElementById('chartView');
            const tableView = document.getElementById('tableView');
            const chartViewBtn = document.getElementById('chartViewBtn');
            const tableViewBtn = document.getElementById('tableViewBtn');

            if (viewType === 'chart') {
                chartView.classList.remove('hidden');
                tableView.classList.add('hidden');
                chartViewBtn.classList.add('bg-blue-500', 'text-white');
                chartViewBtn.classList.remove('bg-white', 'text-gray-700');
                tableViewBtn.classList.remove('bg-blue-500', 'text-white');
                tableViewBtn.classList.add('bg-white', 'text-gray-700');

                // 重新渲染图表
                renderHistoryChart();
            } else {
                chartView.classList.add('hidden');
                tableView.classList.remove('hidden');
                tableViewBtn.classList.add('bg-blue-500', 'text-white');
                tableViewBtn.classList.remove('bg-white', 'text-gray-700');
                chartViewBtn.classList.remove('bg-blue-500', 'text-white');
                chartViewBtn.classList.add('bg-white', 'text-gray-700');
            }
        }

        // 查询点位历史数据
        function queryPointHistory() {
            const startTime = document.getElementById('historyStartTime').value;
            const endTime = document.getElementById('historyEndTime').value;

            // 这里应该是实际的API调用，这里用模拟数据
            const mockData = generateMockHistoryData(startTime, endTime);

            // 更新历史数据
            historyData = mockData;

            // 更新表格视图
            updateHistoryTable(mockData);

            // 更新图表视图
            renderHistoryChart();
        }

        // 更新历史数据表格
        function updateHistoryTable(data) {
            const tableBody = document.getElementById('historyTableBody');
            tableBody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${document.getElementById('pointHistoryTitle').textContent}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.value}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.timestamp}</td>
                `;
                tableBody.appendChild(row);
            });

            // 更新总记录数
            document.getElementById('totalRecords').textContent = data.length;
        }

        // 渲染历史数据图表
        function renderHistoryChart() {
            // 这里应该使用实际的图表库，如ECharts或Chart.js
            // 这里只是一个示例
            const chartContainer = document.getElementById('historyChart');
            chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">图表加载中...</div>';

            // 模拟图表加载
            setTimeout(() => {
                chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-green-500">历史数据曲线图已加载</div>';
            }, 500);
        }

        // 导出历史数据
        function exportPointHistory(format) {
            const pointName = document.getElementById('pointHistoryTitle').textContent;
            const startTime = document.getElementById('historyStartTime').value;
            const endTime = document.getElementById('historyEndTime').value;

            alert(`正在导出 ${pointName} 的历史数据（${format.toUpperCase()}格式）\n时间范围: ${startTime} 至 ${endTime}`);

            // 实际导出逻辑应该在这里实现
        }

        // 生成模拟历史数据
        function generateMockHistoryData(startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);
            const data = [];

            // 每小时一条数据
            for (let time = new Date(start); time <= end; time.setHours(time.getHours() + 1)) {
                data.push({
                    timestamp: formatDateTime(time),
                    value: (Math.random() * 10 + 20).toFixed(1) + (currentPointId === 'temperature' ? '°C' : '%')
                });
            }

            return data;
        }

        // 格式化日期时间为本地字符串
        function formatDateTime(date) {
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).replace(/\//g, '-');
        }

        // 格式化日期时间为datetime-local输入框格式
        function formatDatetimeLocal(date) {
            return date.toISOString().slice(0, 16);
        }

        // 存储当前查看的点位ID和历史数据
        let currentPointId = '';
        let historyData = [];

        // 显示点位曲线对比
        function showPointsCompare() {
            // 初始化对比界面
            initPointsCompare();
            // 打开抽屉
            togglePointsCompareDrawer();
        }

        // ==================== 点位曲线对比功能 ====================

        // 存储对比相关数据
        let selectedPoints = [];
        let compareData = {};
        let chartColors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'];

        // 模拟设备和点位数据
        const mockDevicesWithPoints = [
            {
                id: 'DEVICE_001',
                name: '温度传感器-01',
                type: '温度传感器',
                group: '温控设备组',
                status: '在线',
                points: [
                    { id: 'temp_001', name: '环境温度', unit: '°C', type: 'number' },
                    { id: 'humidity_001', name: '环境湿度', unit: '%', type: 'number' },
                    { id: 'alarm_001', name: '温度报警', unit: '', type: 'boolean' }
                ]
            },
            {
                id: 'DEVICE_002',
                name: '湿度传感器-01',
                type: '湿度传感器',
                group: '温控设备组',
                status: '在线',
                points: [
                    { id: 'humidity_002', name: '土壤湿度', unit: '%', type: 'number' },
                    { id: 'temp_002', name: '土壤温度', unit: '°C', type: 'number' },
                    { id: 'ph_002', name: 'PH值', unit: '', type: 'number' }
                ]
            },
            {
                id: 'DEVICE_003',
                name: '光照传感器-01',
                type: '光照传感器',
                group: '监控设备组',
                status: '在线',
                points: [
                    { id: 'light_003', name: '光照强度', unit: 'lux', type: 'number' },
                    { id: 'uv_003', name: '紫外线强度', unit: 'mW/cm²', type: 'number' },
                    { id: 'switch_003', name: '补光开关', unit: '', type: 'boolean' }
                ]
            },
            {
                id: 'DEVICE_004',
                name: '电力监测器-01',
                type: '电力监测器',
                group: '监控设备组',
                status: '在线',
                points: [
                    { id: 'voltage_004', name: '电压', unit: 'V', type: 'number' },
                    { id: 'current_004', name: '电流', unit: 'A', type: 'number' },
                    { id: 'power_004', name: '功率', unit: 'W', type: 'number' },
                    { id: 'energy_004', name: '电能', unit: 'kWh', type: 'number' }
                ]
            },
            {
                id: 'DEVICE_005',
                name: '压力传感器-01',
                type: '压力传感器',
                group: '监控设备组',
                status: '离线',
                points: [
                    { id: 'pressure_005', name: '管道压力', unit: 'Pa', type: 'number' },
                    { id: 'flow_005', name: '流量', unit: 'L/min', type: 'number' },
                    { id: 'leak_005', name: '泄漏报警', unit: '', type: 'boolean' }
                ]
            }
        ];

        // 获取所有点位的扁平化列表
        function getAllPoints() {
            const allPoints = [];
            mockDevicesWithPoints.forEach(device => {
                device.points.forEach(point => {
                    allPoints.push({
                        ...point,
                        deviceId: device.id,
                        deviceName: device.name,
                        deviceType: device.type,
                        deviceStatus: device.status,
                        fullId: `${device.id}_${point.id}` // 唯一标识符
                    });
                });
            });
            return allPoints;
        }

        // 初始化点位对比界面
        function initPointsCompare() {
            // 重置状态
            selectedPoints = [];
            compareData = {};

            // 设置默认时间范围（过去24小时）
            const endTime = new Date();
            const startTime = new Date(endTime);
            startTime.setHours(startTime.getHours() - 24);

            document.getElementById('compareStartTime').value = formatDatetimeLocal(startTime);
            document.getElementById('compareEndTime').value = formatDatetimeLocal(endTime);

            // 显示点位选择区域，隐藏对比结果区域
            document.getElementById('pointSelectionArea').classList.remove('hidden');
            document.getElementById('compareResultArea').classList.add('hidden');

            // 初始化设备筛选下拉框
            initDeviceFilter();

            // 渲染点位列表
            renderComparePointsList();

            // 更新选择计数
            updateSelectedPointsCount();

            // 绑定事件
            bindCompareEvents();
        }

        // 初始化设备筛选下拉框
        function initDeviceFilter() {
            const deviceSelect = document.getElementById('deviceFilterSelect');
            deviceSelect.innerHTML = '<option value="">所有设备</option>';

            mockDevicesWithPoints.forEach(device => {
                const option = document.createElement('option');
                option.value = device.id;
                option.textContent = `${device.name} (${device.type})`;
                if (device.status === '离线') {
                    option.textContent += ' [离线]';
                    option.style.color = '#9CA3AF';
                }
                deviceSelect.appendChild(option);
            });
        }

        // 绑定对比相关事件
        function bindCompareEvents() {
            // 搜索事件
            const searchInput = document.getElementById('comparePointSearchInput');
            searchInput.removeEventListener('input', filterComparePoints);
            searchInput.addEventListener('input', filterComparePoints);

            // 设备筛选事件
            const deviceSelect = document.getElementById('deviceFilterSelect');
            deviceSelect.removeEventListener('change', filterComparePoints);
            deviceSelect.addEventListener('change', filterComparePoints);
        }

        // 渲染点位列表
        function renderComparePointsList() {
            const container = document.getElementById('comparePointsList');
            container.innerHTML = '';

            const allPoints = getAllPoints();
            const filteredPoints = getFilteredPoints(allPoints);

            if (filteredPoints.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="ri-search-line text-3xl mb-2"></i>
                        <div>没有找到匹配的点位</div>
                    </div>
                `;
                return;
            }

            // 按设备分组显示
            const groupedPoints = groupPointsByDevice(filteredPoints);

            Object.keys(groupedPoints).forEach(deviceId => {
                const device = mockDevicesWithPoints.find(d => d.id === deviceId);
                const points = groupedPoints[deviceId];

                // 设备分组标题
                const deviceHeader = document.createElement('div');
                deviceHeader.className = 'mb-2 pb-2 border-b border-gray-200';
                deviceHeader.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="ri-device-line text-blue-500 mr-2"></i>
                            <span class="font-medium text-gray-700">${device.name}</span>
                            <span class="text-sm text-gray-500 ml-2">(${device.type})</span>
                        </div>
                        <div class="flex items-center">
                            <span class="px-2 py-1 text-xs rounded-full ${device.status === '在线' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}">
                                ${device.status}
                            </span>
                        </div>
                    </div>
                `;
                container.appendChild(deviceHeader);

                // 设备下的点位
                points.forEach(point => {
                    const isSelected = selectedPoints.some(p => p.fullId === point.fullId);
                    const isDisabled = device.status === '离线';

                    const pointItem = document.createElement('div');
                    pointItem.className = `flex items-center justify-between p-3 ml-4 bg-white rounded-lg border ${
                        isSelected ? 'border-blue-500 bg-blue-50' :
                        isDisabled ? 'border-gray-200 bg-gray-50' : 'border-gray-200'
                    } ${isDisabled ? 'cursor-not-allowed opacity-60' : 'hover:border-blue-300 cursor-pointer'}`;

                    if (!isDisabled) {
                        pointItem.onclick = () => togglePointSelection(point);
                    }

                    pointItem.innerHTML = `
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded border-2 ${
                                isSelected ? 'bg-blue-500 border-blue-500' :
                                isDisabled ? 'border-gray-300 bg-gray-200' : 'border-gray-300'
                            } mr-3 flex items-center justify-center">
                                ${isSelected ? '<i class="ri-check-line text-white text-xs"></i>' : ''}
                            </div>
                            <div>
                                <div class="font-medium ${isDisabled ? 'text-gray-400' : 'text-gray-900'}">${point.name}</div>
                                <div class="text-sm text-gray-500">${point.unit || '无单位'}</div>
                            </div>
                        </div>
                        <div class="text-sm ${isDisabled ? 'text-gray-400' : 'text-gray-500'}">
                            ${point.type === 'boolean' ? '开关量' : '模拟量'}
                            ${isDisabled ? ' (离线)' : ''}
                        </div>
                    `;

                    container.appendChild(pointItem);
                });

                // 添加设备间的间距
                if (Object.keys(groupedPoints).indexOf(deviceId) < Object.keys(groupedPoints).length - 1) {
                    const spacer = document.createElement('div');
                    spacer.className = 'h-4';
                    container.appendChild(spacer);
                }
            });
        }

        // 获取过滤后的点位
        function getFilteredPoints(allPoints) {
            const searchTerm = document.getElementById('comparePointSearchInput').value.toLowerCase();
            const selectedDevice = document.getElementById('deviceFilterSelect').value;

            return allPoints.filter(point => {
                const matchesSearch = !searchTerm ||
                    point.name.toLowerCase().includes(searchTerm) ||
                    point.deviceName.toLowerCase().includes(searchTerm);

                const matchesDevice = !selectedDevice || point.deviceId === selectedDevice;

                return matchesSearch && matchesDevice;
            });
        }

        // 按设备分组点位
        function groupPointsByDevice(points) {
            const grouped = {};
            points.forEach(point => {
                if (!grouped[point.deviceId]) {
                    grouped[point.deviceId] = [];
                }
                grouped[point.deviceId].push(point);
            });
            return grouped;
        }

        // 切换点位选择状态
        function togglePointSelection(point) {
            // 检查设备是否离线
            if (point.deviceStatus === '离线') {
                alert('离线设备的点位无法进行对比');
                return;
            }

            const index = selectedPoints.findIndex(p => p.fullId === point.fullId);

            if (index > -1) {
                // 取消选择
                selectedPoints.splice(index, 1);
            } else {
                // 添加选择（最多5个）
                if (selectedPoints.length < 5) {
                    selectedPoints.push(point);
                } else {
                    alert('最多只能选择5个点位进行对比');
                    return;
                }
            }

            // 重新渲染列表
            renderComparePointsList();

            // 更新已选择的点位显示
            updateSelectedPointsDisplay();

            // 更新按钮状态
            updateStartCompareButton();
        }

        // 更新已选择点位的显示
        function updateSelectedPointsDisplay() {
            const container = document.getElementById('selectedPointsList');
            const area = document.getElementById('selectedPointsArea');

            if (selectedPoints.length === 0) {
                area.classList.add('hidden');
                return;
            }

            area.classList.remove('hidden');
            container.innerHTML = '';

            selectedPoints.forEach((point, index) => {
                const tag = document.createElement('div');
                tag.className = 'flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm';
                tag.innerHTML = `
                    <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${chartColors[index]}"></div>
                    <div class="flex flex-col">
                        <span class="font-medium">${point.name}</span>
                        <span class="text-xs text-blue-600">${point.deviceName}</span>
                    </div>
                    <button onclick="removeSelectedPoint('${point.fullId}')" class="ml-2 text-blue-600 hover:text-blue-800">
                        <i class="ri-close-line text-xs"></i>
                    </button>
                `;
                container.appendChild(tag);
            });

            updateSelectedPointsCount();
        }

        // 移除已选择的点位
        function removeSelectedPoint(pointFullId) {
            selectedPoints = selectedPoints.filter(p => p.fullId !== pointFullId);
            renderComparePointsList();
            updateSelectedPointsDisplay();
            updateStartCompareButton();
        }

        // 更新选择计数
        function updateSelectedPointsCount() {
            document.getElementById('selectedPointsCount').textContent = selectedPoints.length;
        }

        // 更新开始对比按钮状态
        function updateStartCompareButton() {
            const button = document.getElementById('startCompareBtn');
            button.disabled = selectedPoints.length === 0;
        }

        // 清空选择
        function clearSelectedPoints() {
            selectedPoints = [];
            renderComparePointsList();
            updateSelectedPointsDisplay();
            updateStartCompareButton();
        }

        // 过滤点位列表
        function filterComparePoints() {
            // 重新渲染列表以应用过滤条件
            renderComparePointsList();
        }

        // 开始点位对比
        function startPointsCompare() {
            if (selectedPoints.length === 0) {
                alert('请至少选择一个点位');
                return;
            }

            const startTime = document.getElementById('compareStartTime').value;
            const endTime = document.getElementById('compareEndTime').value;

            if (!startTime || !endTime) {
                alert('请选择时间范围');
                return;
            }

            if (new Date(startTime) >= new Date(endTime)) {
                alert('开始时间必须早于结束时间');
                return;
            }

            // 显示加载状态
            showCompareLoading();

            // 模拟数据加载
            setTimeout(() => {
                loadCompareData(startTime, endTime);
                showCompareResult();
            }, 1000);
        }

        // 显示加载状态
        function showCompareLoading() {
            document.getElementById('pointSelectionArea').classList.add('hidden');
            document.getElementById('compareResultArea').classList.remove('hidden');

            document.getElementById('compareChart').innerHTML = `
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <i class="ri-loader-4-line text-4xl text-blue-500 animate-spin"></i>
                        <div class="mt-2 text-gray-500">正在加载对比数据...</div>
                    </div>
                </div>
            `;
        }

        // 加载对比数据
        function loadCompareData(startTime, endTime) {
            compareData = {};

            selectedPoints.forEach(point => {
                compareData[point.fullId] = generateMockCompareData(point, startTime, endTime);
            });
        }

        // 生成模拟对比数据
        function generateMockCompareData(point, startTime, endTime) {
            const data = [];
            const start = new Date(startTime);
            const end = new Date(endTime);
            const interval = (end - start) / 100; // 生成100个数据点

            for (let i = 0; i <= 100; i++) {
                const time = new Date(start.getTime() + i * interval);
                let value;

                if (point.type === 'boolean') {
                    value = Math.random() > 0.5 ? 1 : 0;
                } else {
                    // 生成模拟的数值数据
                    const baseValue = getBaseValue(point.id);
                    const variation = baseValue * 0.1;
                    value = baseValue + (Math.random() - 0.5) * variation;
                }

                data.push({
                    time: time.toISOString(),
                    value: value
                });
            }

            return data;
        }

        // 获取点位基础值
        function getBaseValue(pointId) {
            const baseValues = {
                'temp_001': 25, 'temp_002': 22,
                'humidity_001': 60, 'humidity_002': 45,
                'pressure_005': 101325,
                'voltage_004': 220,
                'current_004': 5,
                'power_004': 1100,
                'energy_004': 50,
                'light_003': 1000,
                'uv_003': 0.5,
                'flow_005': 15,
                'ph_002': 7.2
            };
            return baseValues[pointId] || 50;
        }

        // 显示对比结果
        function showCompareResult() {
            // 渲染图例
            renderChartLegend();

            // 渲染图表
            renderCompareChart();

            // 渲染统计信息
            renderCompareStats();
        }

        // 渲染图例
        function renderChartLegend() {
            const container = document.getElementById('legendItems');
            container.innerHTML = '';

            selectedPoints.forEach((point, index) => {
                const legendItem = document.createElement('div');
                legendItem.className = 'flex items-center';
                legendItem.innerHTML = `
                    <div class="w-4 h-4 rounded mr-2" style="background-color: ${chartColors[index]}"></div>
                    <span class="text-sm font-medium">${point.name}</span>
                    <span class="text-sm text-gray-500 ml-1">(${point.unit || '无单位'})</span>
                `;
                container.appendChild(legendItem);
            });
        }

        // 渲染对比图表
        function renderCompareChart() {
            const container = document.getElementById('compareChart');
            container.innerHTML = `
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <i class="ri-line-chart-line text-6xl text-blue-500 mb-4"></i>
                        <div class="text-lg font-medium text-gray-700">点位曲线对比图表</div>
                        <div class="text-sm text-gray-500 mt-2">
                            正在显示 ${selectedPoints.length} 个点位的对比数据
                        </div>
                        <div class="text-xs text-gray-400 mt-1">
                            (实际项目中这里会集成 ECharts 或其他图表库)
                        </div>
                    </div>
                </div>
            `;
        }

        // 渲染统计信息
        function renderCompareStats() {
            const container = document.getElementById('compareStats');
            container.innerHTML = '';

            selectedPoints.forEach((point, index) => {
                const data = compareData[point.fullId];
                const values = data.map(d => d.value);
                const avg = values.reduce((a, b) => a + b, 0) / values.length;
                const max = Math.max(...values);
                const min = Math.min(...values);

                const statCard = document.createElement('div');
                statCard.className = 'bg-white rounded-lg border p-4';
                statCard.innerHTML = `
                    <div class="flex items-center mb-2">
                        <div class="w-3 h-3 rounded mr-2" style="background-color: ${chartColors[index]}"></div>
                        <div>
                            <h6 class="font-medium">${point.name}</h6>
                            <div class="text-xs text-gray-500">${point.deviceName}</div>
                        </div>
                    </div>
                    <div class="space-y-1 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">平均值:</span>
                            <span>${avg.toFixed(2)} ${point.unit || ''}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">最大值:</span>
                            <span>${max.toFixed(2)} ${point.unit || ''}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">最小值:</span>
                            <span>${min.toFixed(2)} ${point.unit || ''}</span>
                        </div>
                    </div>
                `;
                container.appendChild(statCard);
            });
        }

        // 取消对比
        function cancelPointsCompare() {
            if (confirm('确定要取消当前的对比吗？')) {
                // 重置到选择界面
                document.getElementById('compareResultArea').classList.add('hidden');
                document.getElementById('pointSelectionArea').classList.remove('hidden');

                // 清空数据
                compareData = {};
            }
        }

        // 导出对比图表
        function exportCompareChart() {
            alert('导出功能开发中...\n实际项目中可以导出为 PNG、PDF 或 Excel 格式');
        }

        // 切换点位对比抽屉
        function togglePointsCompareDrawer() {
            const drawer = document.getElementById('pointsCompareDrawer');
            const drawerContent = drawer.querySelector('.max-w-6xl');

            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 任务和规则标签切换函数
        function showTaskTab() {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.add('hidden'));
            document.getElementById('task').classList.remove('hidden');
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('bg-blue-500', 'text-white'));
            document.getElementById('taskTab').classList.add('bg-blue-500', 'text-white');
        }

        function showRuleTab() {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.add('hidden'));
            document.getElementById('rule').classList.remove('hidden');
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('bg-blue-500', 'text-white'));
            document.getElementById('ruleTab').classList.add('bg-blue-500', 'text-white');
        }

        // 任务抽屉相关函数
        function toggleAddTaskDrawer() {
            const drawer = document.getElementById('addTaskDrawer');
            if (drawer.classList.contains('translate-x-full')) {
                drawer.classList.remove('translate-x-full');
                document.body.classList.add('overflow-hidden');
            } else {
                drawer.classList.add('translate-x-full');
                document.body.classList.remove('overflow-hidden');
            }
        }

        function submitTaskForm() {
            const taskName = document.getElementById('taskName').value;
            const deviceId = document.getElementById('deviceIdTask').value;
            const taskType = document.querySelector('input[name="taskType"]:checked').value;

            // 简单的表单验证
            if (taskName.trim() === '') {
                alert('请输入任务名称');
                return;
            }

            // 这里添加提交任务表单的逻辑
            console.log('提交任务表单', {
                taskName,
                deviceId,
                taskType,
                // 可以获取更多表单字段数据
            });

            // 模拟成功提交
            alert('任务已创建成功！');

            // 清空表单
            document.getElementById('taskForm').reset();

            // 关闭抽屉
            toggleAddTaskDrawer();

            // 刷新任务列表（实际项目中可能通过API获取最新数据）
            // refreshTaskList();
        }

        // 规则抽屉相关函数
        function toggleAddRuleDrawer() {
            const drawer = document.getElementById('addRuleDrawer');
            if (drawer.classList.contains('translate-x-full')) {
                drawer.classList.remove('translate-x-full');
                document.body.classList.add('overflow-hidden');
            } else {
                drawer.classList.add('translate-x-full');
                document.body.classList.remove('overflow-hidden');
            }
        }

        function submitRuleForm() {
            const ruleName = document.getElementById('ruleName').value;
            const deviceId = document.getElementById('deviceIdRule').value;

            // 简单的表单验证
            if (ruleName.trim() === '') {
                alert('请输入规则名称');
                return;
            }

            // 这里添加提交规则表单的逻辑
            console.log('提交规则表单', {
                ruleName,
                deviceId,
                // 可以获取更多表单字段数据
            });

            // 模拟成功提交
            alert('规则已创建成功！');

            // 清空表单
            document.getElementById('ruleForm').reset();

            // 关闭抽屉
            toggleAddRuleDrawer();

            // 刷新规则列表（实际项目中可能通过API获取最新数据）
            // refreshRuleList();
        }

        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化标签页按钮监听
            document.getElementById('taskTab').addEventListener('click', showTaskTab);
            document.getElementById('ruleTab').addEventListener('click', showRuleTab);

            // 初始化任务表单相关
            document.getElementById('taskSubmitBtn')?.addEventListener('click', submitTaskForm);
            document.getElementById('taskCancelBtn')?.addEventListener('click', toggleAddTaskDrawer);

            // 初始化规则表单相关
            document.getElementById('ruleSubmitBtn')?.addEventListener('click', submitRuleForm);
            document.getElementById('ruleCancelBtn')?.addEventListener('click', toggleAddRuleDrawer);

            // 任务类型选择逻辑
            const taskTypeInputs = document.querySelectorAll('input[name="taskType"]');
            taskTypeInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const timerSettings = document.getElementById('timerSettings');
                    const conditionSettings = document.getElementById('conditionSettings');

                    if (this.value === 'timer') {
                        timerSettings.classList.remove('hidden');
                        conditionSettings.classList.add('hidden');
                    } else {
                        timerSettings.classList.add('hidden');
                        conditionSettings.classList.remove('hidden');
                    }
                });
            });
        });
    </script>

    <!-- 添加必要的样式 -->
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
    </style>
</body>
</html>