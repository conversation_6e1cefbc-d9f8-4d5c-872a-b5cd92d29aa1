// 仪表板页面JavaScript

let trendChart = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initDashboard();
    loadSidebarComponent();
});

// 初始化仪表板
function initDashboard() {
    loadStatistics();
    initTrendChart();
    loadDeviceStatus();
    loadRecentRecords();
    loadRecentAlerts();

    // 设置定时刷新
    setInterval(refreshData, CONFIG.REFRESH_INTERVAL);
}

// 加载侧边栏组件
function loadSidebarComponent() {
    fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('sidebar-container').innerHTML = html;
            // 执行侧边栏中的脚本
            const scripts = document.getElementById('sidebar-container').querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.body.appendChild(newScript);
            });
        })
        .catch(error => {
            console.error('Failed to load sidebar:', error);
        });
}

// 加载统计数据
function loadStatistics() {
    // 模拟API调用
    const stats = {
        todayTotal: Math.floor(Math.random() * 2000) + 1000,
        employeeCount: Math.floor(Math.random() * 1000) + 500,
        visitorCount: Math.floor(Math.random() * 500) + 200,
        alertCount: Math.floor(Math.random() * 10)
    };

    // 更新统计数据
    document.getElementById('todayTotal').textContent = stats.todayTotal.toLocaleString();
    document.getElementById('employeeCount').textContent = stats.employeeCount.toLocaleString();
    document.getElementById('visitorCount').textContent = stats.visitorCount.toLocaleString();
    document.getElementById('alertCount').textContent = stats.alertCount;
}

// 初始化趋势图表
function initTrendChart() {
    const ctx = document.getElementById('trendChart').getContext('2d');

    // 生成24小时的模拟数据
    const hours = [];
    const employeeData = [];
    const visitorData = [];

    for (let i = 0; i < 24; i++) {
        hours.push(`${i.toString().padStart(2, '0')}:00`);
        employeeData.push(Math.floor(Math.random() * 100) + 20);
        visitorData.push(Math.floor(Math.random() * 50) + 10);
    }

    trendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: hours,
            datasets: [{
                label: '员工通行',
                data: employeeData,
                borderColor: CONFIG.CHART_COLORS.primary,
                backgroundColor: CONFIG.CHART_COLORS.primary + '20',
                tension: 0.4,
                fill: true
            }, {
                label: '访客通行',
                data: visitorData,
                borderColor: CONFIG.CHART_COLORS.warning,
                backgroundColor: CONFIG.CHART_COLORS.warning + '20',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#e2e8f0'
                    }
                },
                x: {
                    grid: {
                        color: '#e2e8f0'
                    }
                }
            }
        }
    });
}

// 加载设备状态
function loadDeviceStatus() {
    const deviceGrid = document.getElementById('deviceGrid');
    const devices = [];

    // 生成42台设备的模拟数据
    const deviceTypes = ['人脸机', '刷卡机', '二维码设备'];
    const locations = ['主入口', '侧门A', '侧门B', '地下车库', '后门', '紧急出口', '电梯口', '楼梯间'];

    for (let i = 1; i <= 42; i++) {
        devices.push({
            id: `DEV${i.toString().padStart(3, '0')}`,
            name: `${deviceTypes[Math.floor(Math.random() * deviceTypes.length)]} ${i}`,
            location: locations[Math.floor(Math.random() * locations.length)],
            status: MockData.generateDeviceStatus(),
            lastHeartbeat: new Date(Date.now() - Math.random() * 60000)
        });
    }

    // 渲染设备网格
    deviceGrid.innerHTML = devices.map(device => `
        <div class="device-item">
            <div class="device-header">
                <span class="device-name">${device.name}</span>
                <span class="status-indicator status-${device.status}">
                    <i class="fas fa-circle"></i>
                    ${getStatusText(device.status)}
                </span>
            </div>
            <div class="device-info">
                <div class="device-location">
                    <i class="fas fa-map-marker-alt"></i>
                    ${device.location}
                </div>
                <div class="device-heartbeat">
                    <i class="fas fa-heartbeat"></i>
                    ${Utils.formatTime(device.lastHeartbeat)}
                </div>
            </div>
        </div>
    `).join('');
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        online: '在线',
        offline: '离线',
        warning: '异常'
    };
    return statusMap[status] || '未知';
}

// 加载最近通行记录
function loadRecentRecords() {
    const tbody = document.getElementById('recentRecords');
    const records = [];

    // 生成10条最近记录
    for (let i = 0; i < 10; i++) {
        records.push(MockData.generateAccessRecord());
    }

    // 按时间排序
    records.sort((a, b) => b.time - a.time);

    tbody.innerHTML = records.map(record => `
        <tr>
            <td>${record.name}</td>
            <td>
                <span class="badge badge-${getTypeClass(record.type)}">
                    ${record.type}
                </span>
            </td>
            <td>${record.channel}</td>
            <td>
                <span class="direction-indicator ${record.direction === '进入' ? 'direction-in' : 'direction-out'}">
                    <i class="fas fa-arrow-${record.direction === '进入' ? 'right' : 'left'}"></i>
                    ${record.direction}
                </span>
            </td>
            <td>${Utils.formatTime(record.time)}</td>
        </tr>
    `).join('');
}

// 获取类型样式类
function getTypeClass(type) {
    const typeMap = {
        '员工': 'primary',
        '访客': 'warning',
        '临时工': 'info'
    };
    return typeMap[type] || 'secondary';
}

// 加载最近报警事件
function loadRecentAlerts() {
    const alertList = document.getElementById('recentAlerts');
    const alerts = [];

    // 生成5条最近报警
    for (let i = 0; i < 5; i++) {
        alerts.push(MockData.generateAlert());
    }

    // 按时间排序
    alerts.sort((a, b) => b.time - a.time);

    alertList.innerHTML = alerts.map(alert => `
        <div class="alert-item">
            <div class="alert-header">
                <div class="alert-type">
                    <i class="fas fa-${getAlertIcon(alert.type)}"></i>
                    ${alert.type}
                </div>
                <div class="alert-level level-${getLevelClass(alert.level)}">
                    ${alert.level}
                </div>
            </div>
            <div class="alert-content">
                <div class="alert-location">
                    <i class="fas fa-map-marker-alt"></i>
                    ${alert.channel}
                </div>
                <div class="alert-time">
                    <i class="fas fa-clock"></i>
                    ${Utils.formatDateTime(alert.time)}
                </div>
            </div>
            <div class="alert-status">
                <span class="status-badge status-${alert.status === '已处理' ? 'resolved' : 'pending'}">
                    ${alert.status}
                </span>
            </div>
        </div>
    `).join('');
}

// 获取报警图标
function getAlertIcon(type) {
    const iconMap = {
        '尾随检测': 'user-friends',
        '非法入侵': 'exclamation-triangle',
        '设备故障': 'tools',
        '长时间滞留': 'clock'
    };
    return iconMap[type] || 'exclamation-circle';
}

// 获取级别样式类
function getLevelClass(level) {
    const levelMap = {
        '高': 'high',
        '中': 'medium',
        '低': 'low'
    };
    return levelMap[level] || 'medium';
}

// 刷新数据
function refreshData() {
    Utils.showNotification('正在刷新数据...', 'info', 1000);

    setTimeout(() => {
        loadStatistics();
        loadDeviceStatus();
        loadRecentRecords();
        loadRecentAlerts();

        // 更新图表数据
        updateTrendChart();

        Utils.showNotification('数据刷新完成', 'success');
    }, 1000);
}

// 更新趋势图表
function updateTrendChart() {
    if (trendChart) {
        // 生成新的数据
        const newEmployeeData = [];
        const newVisitorData = [];

        for (let i = 0; i < 24; i++) {
            newEmployeeData.push(Math.floor(Math.random() * 100) + 20);
            newVisitorData.push(Math.floor(Math.random() * 50) + 10);
        }

        trendChart.data.datasets[0].data = newEmployeeData;
        trendChart.data.datasets[1].data = newVisitorData;
        trendChart.update();
    }
}

// 趋势筛选器变化事件
document.addEventListener('change', function(e) {
    if (e.target.id === 'trendFilter') {
        const period = e.target.value;
        updateTrendChartByPeriod(period);
    }
});

// 根据时间段更新图表
function updateTrendChartByPeriod(period) {
    if (!trendChart) return;

    let labels = [];
    let employeeData = [];
    let visitorData = [];

    switch (period) {
        case 'today':
            // 24小时数据
            for (let i = 0; i < 24; i++) {
                labels.push(`${i.toString().padStart(2, '0')}:00`);
                employeeData.push(Math.floor(Math.random() * 100) + 20);
                visitorData.push(Math.floor(Math.random() * 50) + 10);
            }
            break;
        case 'week':
            // 7天数据
            const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            labels = days;
            for (let i = 0; i < 7; i++) {
                employeeData.push(Math.floor(Math.random() * 1000) + 500);
                visitorData.push(Math.floor(Math.random() * 500) + 200);
            }
            break;
        case 'month':
            // 30天数据
            for (let i = 1; i <= 30; i++) {
                labels.push(`${i}日`);
                employeeData.push(Math.floor(Math.random() * 1000) + 500);
                visitorData.push(Math.floor(Math.random() * 500) + 200);
            }
            break;
    }

    trendChart.data.labels = labels;
    trendChart.data.datasets[0].data = employeeData;
    trendChart.data.datasets[1].data = visitorData;
    trendChart.update();
}