<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通行记录 - 门禁系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-list-alt"></i>
                    通行记录
                </h1>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="exportRecords()">
                        <i class="fas fa-download"></i>
                        导出记录
                    </button>
                    <button class="btn btn-primary" onclick="refreshRecords()">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day" style="color: var(--primary-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="todayRecords">1,234</div>
                        <div class="stat-label">今日记录</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8.5%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week" style="color: var(--success-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="weekRecords">8,567</div>
                        <div class="stat-label">本周记录</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12.3%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-database" style="color: var(--info-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalRecords">156,789</div>
                        <div class="stat-label">总记录数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            持续增长
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users" style="color: var(--warning-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="uniqueUsers">2,345</div>
                        <div class="stat-label">活跃用户</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +5.2%
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">搜索筛选</h3>
                    <div class="card-actions">
                        <button class="btn btn-outline" onclick="resetFilters()">
                            <i class="fas fa-undo"></i>
                            重置筛选
                        </button>
                    </div>
                </div>
                <div class="search-filters">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索姓名、卡号..." onkeyup="handleSearch()">

                    <select class="filter-select" id="typeFilter" onchange="applyFilters()">
                        <option value="">全部类型</option>
                        <option value="员工">员工</option>
                        <option value="访客">访客</option>
                        <option value="临时工">临时工</option>
                    </select>

                    <select class="filter-select" id="methodFilter" onchange="applyFilters()">
                        <option value="">全部方式</option>
                        <option value="刷卡">刷卡</option>
                        <option value="人脸识别">人脸识别</option>
                        <option value="二维码">二维码</option>
                    </select>

                    <select class="filter-select" id="channelFilter" onchange="applyFilters()">
                        <option value="">全部通道</option>
                        <option value="主入口">主入口</option>
                        <option value="侧门A">侧门A</option>
                        <option value="侧门B">侧门B</option>
                        <option value="地下车库">地下车库</option>
                        <option value="后门">后门</option>
                        <option value="紧急出口">紧急出口</option>
                    </select>

                    <select class="filter-select" id="directionFilter" onchange="applyFilters()">
                        <option value="">全部方向</option>
                        <option value="进入">进入</option>
                        <option value="离开">离开</option>
                    </select>

                    <input type="date" class="filter-select" id="dateFilter" onchange="applyFilters()">

                    <button class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
            </div>

            <!-- 记录列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">通行记录列表</h3>
                    <div class="card-actions">
                        <span class="record-count">共 <span id="recordCount">0</span> 条记录</span>
                        <select class="filter-select" id="pageSizeSelect" onchange="changePageSize()">
                            <option value="20">每页20条</option>
                            <option value="50">每页50条</option>
                            <option value="100">每页100条</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th onclick="sortTable('time')">
                                    时间
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th onclick="sortTable('name')">
                                    姓名
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th onclick="sortTable('type')">
                                    类型
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th onclick="sortTable('method')">
                                    验证方式
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th onclick="sortTable('channel')">
                                    通道
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th onclick="sortTable('direction')">
                                    方向
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th>卡号</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTableBody">
                            <!-- 记录将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 记录详情模态框 -->
    <div class="modal" id="recordDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">通行记录详情</h3>
                <button class="modal-close" onclick="closeRecordDetail()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="recordDetailContent">
                <!-- 详情内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script src="records.js"></script>
</body>
</html>