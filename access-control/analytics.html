<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计分析 - 门禁系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-chart-bar"></i>
                    统计分析
                </h1>
                <div class="page-actions">
                    <select class="filter-select" id="periodFilter" onchange="changePeriod()">
                        <option value="today">今日</option>
                        <option value="week">本周</option>
                        <option value="month" selected>本月</option>
                        <option value="quarter">本季度</option>
                        <option value="year">本年</option>
                    </select>
                    <button class="btn btn-success" onclick="exportAnalytics()">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="btn btn-primary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync-alt"></i>
                        刷新数据
                    </button>
                </div>
            </div>

            <!-- 关键指标概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users" style="color: var(--primary-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalTraffic">45,678</div>
                        <div class="stat-label">总通行量</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15.2%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock" style="color: var(--warning-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="peakHour">09:00</div>
                        <div class="stat-label">高峰时段</div>
                        <div class="stat-change">
                            <i class="fas fa-info-circle"></i>
                            1,234人次
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage" style="color: var(--success-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="successRate">99.8%</div>
                        <div class="stat-label">通行成功率</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +0.3%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-stopwatch" style="color: var(--info-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="avgTime">2.3s</div>
                        <div class="stat-label">平均通行时间</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -0.2s
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="analytics-grid">
                <!-- 通行趋势图 -->
                <div class="card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">通行趋势分析</h3>
                        <div class="card-actions">
                            <select class="filter-select" id="trendType" onchange="updateTrendChart()">
                                <option value="hourly">按小时</option>
                                <option value="daily" selected>按天</option>
                                <option value="weekly">按周</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>

                <!-- 通道分布图 -->
                <div class="card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">通道使用分布</h3>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="toggleChartType('channel')">
                                <i class="fas fa-exchange-alt"></i>
                                切换图表
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="channelChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="analytics-grid">
                <!-- 人员类型分析 -->
                <div class="card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">人员类型分析</h3>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="toggleChartType('type')">
                                <i class="fas fa-pie-chart"></i>
                                饼图/柱图
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="typeChart"></canvas>
                    </div>
                </div>

                <!-- 验证方式统计 -->
                <div class="card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">验证方式统计</h3>
                        <div class="card-actions">
                            <span class="chart-legend">
                                <span class="legend-item">
                                    <span class="legend-color" style="background: var(--primary-color);"></span>
                                    刷卡
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: var(--success-color);"></span>
                                    人脸识别
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: var(--warning-color);"></span>
                                    二维码
                                </span>
                            </span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="methodChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 高峰期分析 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">高峰期分析</h3>
                    <div class="card-actions">
                        <button class="btn btn-outline" onclick="generatePeakReport()">
                            <i class="fas fa-file-alt"></i>
                            生成报告
                        </button>
                    </div>
                </div>
                <div class="peak-analysis">
                    <div class="peak-grid">
                        <div class="peak-item">
                            <div class="peak-header">
                                <h4 class="peak-title">
                                    <i class="fas fa-sun"></i>
                                    早高峰 (08:00-10:00)
                                </h4>
                                <span class="peak-status status-high">高峰</span>
                            </div>
                            <div class="peak-stats">
                                <div class="peak-stat">
                                    <span class="stat-label">通行人数:</span>
                                    <span class="stat-value">2,456人</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">平均等待:</span>
                                    <span class="stat-value">3.2秒</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">拥堵指数:</span>
                                    <span class="stat-value congestion-high">85%</span>
                                </div>
                            </div>
                        </div>

                        <div class="peak-item">
                            <div class="peak-header">
                                <h4 class="peak-title">
                                    <i class="fas fa-moon"></i>
                                    晚高峰 (17:00-19:00)
                                </h4>
                                <span class="peak-status status-high">高峰</span>
                            </div>
                            <div class="peak-stats">
                                <div class="peak-stat">
                                    <span class="stat-label">通行人数:</span>
                                    <span class="stat-value">2,234人</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">平均等待:</span>
                                    <span class="stat-value">2.8秒</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">拥堵指数:</span>
                                    <span class="stat-value congestion-high">78%</span>
                                </div>
                            </div>
                        </div>

                        <div class="peak-item">
                            <div class="peak-header">
                                <h4 class="peak-title">
                                    <i class="fas fa-utensils"></i>
                                    午餐时段 (11:30-13:30)
                                </h4>
                                <span class="peak-status status-medium">中等</span>
                            </div>
                            <div class="peak-stats">
                                <div class="peak-stat">
                                    <span class="stat-label">通行人数:</span>
                                    <span class="stat-value">1,567人</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">平均等待:</span>
                                    <span class="stat-value">1.8秒</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">拥堵指数:</span>
                                    <span class="stat-value congestion-medium">45%</span>
                                </div>
                            </div>
                        </div>

                        <div class="peak-item">
                            <div class="peak-header">
                                <h4 class="peak-title">
                                    <i class="fas fa-bed"></i>
                                    夜间时段 (22:00-06:00)
                                </h4>
                                <span class="peak-status status-low">低峰</span>
                            </div>
                            <div class="peak-stats">
                                <div class="peak-stat">
                                    <span class="stat-label">通行人数:</span>
                                    <span class="stat-value">234人</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">平均等待:</span>
                                    <span class="stat-value">0.8秒</span>
                                </div>
                                <div class="peak-stat">
                                    <span class="stat-label">拥堵指数:</span>
                                    <span class="stat-value congestion-low">12%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script src="analytics.js"></script>
</body>
</html>