// 门禁系统公共JavaScript文件

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    REFRESH_INTERVAL: 30000, // 30秒刷新间隔
    CHART_COLORS: {
        primary: '#2563eb',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        info: '#06b6d4'
    }
};

// 工具函数
const Utils = {
    // 格式化日期时间
    formatDateTime: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },

    // 格式化日期
    formatDate: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleDateString('zh-CN');
    },

    // 格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN');
    },

    // 生成随机ID
    generateId: function() {
        return Math.random().toString(36).substr(2, 9);
    },

    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    // 显示通知
    showNotification: function(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    },

    // 获取通知图标
    getNotificationIcon: function(type) {
        const icons = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            danger: 'times-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    // 确认对话框
    confirm: function(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    }
};

// API请求封装
const API = {
    // GET请求
    get: async function(url) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`);
            return await response.json();
        } catch (error) {
            console.error('API GET Error:', error);
            Utils.showNotification('请求失败，请稍后重试', 'danger');
            throw error;
        }
    },

    // POST请求
    post: async function(url, data) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            return await response.json();
        } catch (error) {
            console.error('API POST Error:', error);
            Utils.showNotification('提交失败，请稍后重试', 'danger');
            throw error;
        }
    },

    // PUT请求
    put: async function(url, data) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            return await response.json();
        } catch (error) {
            console.error('API PUT Error:', error);
            Utils.showNotification('更新失败，请稍后重试', 'danger');
            throw error;
        }
    },

    // DELETE请求
    delete: async function(url) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${url}`, {
                method: 'DELETE'
            });
            return await response.json();
        } catch (error) {
            console.error('API DELETE Error:', error);
            Utils.showNotification('删除失败，请稍后重试', 'danger');
            throw error;
        }
    }
};

// 模拟数据生成器
const MockData = {
    // 生成随机设备状态
    generateDeviceStatus: function() {
        const statuses = ['online', 'offline', 'warning'];
        return statuses[Math.floor(Math.random() * statuses.length)];
    },

    // 生成随机通行记录
    generateAccessRecord: function() {
        const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
        const types = ['员工', '访客', '临时工'];
        const methods = ['刷卡', '人脸识别', '二维码'];
        const channels = ['主入口', '侧门A', '侧门B', '地下车库', '后门', '紧急出口'];

        return {
            id: Utils.generateId(),
            name: names[Math.floor(Math.random() * names.length)],
            type: types[Math.floor(Math.random() * types.length)],
            method: methods[Math.floor(Math.random() * methods.length)],
            channel: channels[Math.floor(Math.random() * channels.length)],
            direction: Math.random() > 0.5 ? '进入' : '离开',
            time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
            cardId: 'C' + Math.floor(Math.random() * 10000).toString().padStart(4, '0')
        };
    },

    // 生成随机报警事件
    generateAlert: function() {
        const types = ['尾随检测', '非法入侵', '设备故障', '长时间滞留'];
        const levels = ['高', '中', '低'];
        const channels = ['主入口', '侧门A', '侧门B', '地下车库', '后门', '紧急出口'];

        return {
            id: Utils.generateId(),
            type: types[Math.floor(Math.random() * types.length)],
            level: levels[Math.floor(Math.random() * levels.length)],
            channel: channels[Math.floor(Math.random() * channels.length)],
            time: new Date(Date.now() - Math.random() * 2 * 60 * 60 * 1000),
            status: Math.random() > 0.3 ? '已处理' : '待处理',
            description: '检测到异常行为，请及时处理'
        };
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载侧边栏
    loadSidebar();

    // 初始化通知样式
    initNotificationStyles();
});

// 加载侧边栏组件
function loadSidebar() {
    fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
            const sidebarContainer = document.getElementById('sidebar-container');
            if (sidebarContainer) {
                sidebarContainer.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Failed to load sidebar:', error);
        });
}

// 初始化通知样式
function initNotificationStyles() {
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                padding: 16px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-width: 300px;
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
            }

            .notification-success { border-left: 4px solid var(--success-color); }
            .notification-warning { border-left: 4px solid var(--warning-color); }
            .notification-danger { border-left: 4px solid var(--danger-color); }
            .notification-info { border-left: 4px solid var(--info-color); }

            .notification-content {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .notification-close {
                background: none;
                border: none;
                cursor: pointer;
                color: var(--text-secondary);
                padding: 4px;
            }

            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
}