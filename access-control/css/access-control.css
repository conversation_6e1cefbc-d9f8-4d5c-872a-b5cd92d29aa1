/* 门禁系统专用样式文件 */

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-bg: #f8fafc;
    --dark-bg: #1e293b;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 基础重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-bg);
    color: var(--text-primary);
    line-height: 1.6;
}

/* 主布局 */
.main-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background: white;
    box-shadow: var(--shadow);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
}

/* 容器布局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    gap: 4px;
}

.status-online {
    background-color: #dcfce7;
    color: #166534;
}

.status-offline {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-warning {
    background-color: #fef3c7;
    color: #92400e;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary-color);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 14px;
}

.stat-change {
    font-size: 12px;
    margin-top: 8px;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--light-bg);
    font-weight: 600;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* 搜索和筛选 */
.search-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.pagination button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 导航栏样式 */
.nav-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.nav-logo {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s;
    gap: 12px;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--light-bg);
    color: var(--primary-color);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
        z-index: 999;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .search-filters {
        flex-direction: column;
    }

    .search-input {
        min-width: auto;
    }

    .table-container {
        overflow-x: auto;
    }
}

/* 仪表板专用样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    min-height: 400px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin-top: 20px;
}

.device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.device-item {
    background: var(--light-bg);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid var(--border-color);
}

.device-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.device-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.device-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.device-location,
.device-heartbeat {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.alert-list {
    max-height: 400px;
    overflow-y: auto;
}

.alert-item {
    background: var(--light-bg);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--warning-color);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.alert-type {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-level {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.level-high {
    background-color: #fee2e2;
    color: #991b1b;
}

.level-medium {
    background-color: #fef3c7;
    color: #92400e;
}

.level-low {
    background-color: #dcfce7;
    color: #166534;
}

.alert-content {
    display: flex;
    gap: 20px;
    margin-bottom: 8px;
}

.alert-location,
.alert-time {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.alert-status {
    display: flex;
    justify-content: flex-end;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-resolved {
    background-color: #dcfce7;
    color: #166534;
}

.status-pending {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge-primary {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-info {
    background-color: #cffafe;
    color: #0e7490;
}

.direction-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.direction-in {
    color: var(--success-color);
}

.direction-out {
    color: var(--info-color);
}

.status-summary {
    display: flex;
    gap: 15px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-bg);
    border-radius: 12px;
}

.stat-content {
    flex: 1;
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .device-grid {
        grid-template-columns: 1fr;
    }

    .alert-content {
        flex-direction: column;
        gap: 5px;
    }
}

/* 监控页面专用样式 */
.monitor-status-bar {
    display: flex;
    gap: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-weight: 600;
    color: var(--text-secondary);
}

.status-value {
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 6px;
}

.monitor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.channel-monitor {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.channel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
}

.channel-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.channel-status {
    display: flex;
    align-items: center;
}

.channel-video {
    position: relative;
    height: 200px;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-placeholder {
    color: #666;
    text-align: center;
}

.video-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.video-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    max-width: 200px;
}

.passing-event {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 5px;
    font-size: 12px;
    animation: fadeInOut 3s ease-in-out;
}

.event-info {
    margin-bottom: 4px;
}

.event-person {
    font-weight: 600;
}

.event-method {
    color: #ccc;
}

.event-time {
    text-align: right;
    color: #999;
    font-size: 10px;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateX(20px); }
    10% { opacity: 1; transform: translateX(0); }
    90% { opacity: 1; transform: translateX(0); }
    100% { opacity: 0; transform: translateX(-20px); }
}

.channel-info {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    background: white;
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.traffic-log {
    max-height: 400px;
    overflow-y: auto;
}

.log-placeholder {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.log-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.log-entry {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
    gap: 15px;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 60px;
    font-family: monospace;
}

.log-content {
    flex: 1;
}

.log-person {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.person-name {
    font-weight: 600;
    color: var(--text-primary);
}

.log-details {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.log-separator {
    color: var(--border-color);
}

.log-direction {
    display: flex;
    align-items: center;
    gap: 4px;
}

.log-status {
    display: flex;
    align-items: center;
}

@media (max-width: 768px) {
    .monitor-status-bar {
        flex-direction: column;
        gap: 15px;
    }

    .monitor-grid {
        grid-template-columns: 1fr;
    }

    .channel-info {
        flex-direction: column;
        gap: 10px;
    }

    .log-entry {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .log-time {
        min-width: auto;
    }
}

/* 通行记录页面专用样式 */
.record-count {
    color: var(--text-secondary);
    font-size: 14px;
}

.sort-icon {
    margin-left: 5px;
    color: var(--text-secondary);
    font-size: 12px;
}

.table th {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
}

.table th:hover {
    background-color: #e2e8f0;
}

.person-info {
    display: flex;
    flex-direction: column;
}

.person-name {
    font-weight: 600;
    color: var(--text-primary);
}

.method-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.method-card {
    background-color: #dbeafe;
    color: #1e40af;
}

.method-face {
    background-color: #dcfce7;
    color: #166534;
}

.method-qr {
    background-color: #fef3c7;
    color: #92400e;
}

.method-default {
    background-color: #f1f5f9;
    color: #475569;
}

.card-id {
    font-family: 'Courier New', monospace;
    background-color: var(--light-bg);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-primary);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.pagination-ellipsis {
    padding: 8px 12px;
    color: var(--text-secondary);
}

/* 记录详情模态框样式 */
.record-detail {
    max-width: 600px;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-item span,
.detail-item code {
    font-size: 14px;
    color: var(--text-primary);
}

/* 搜索筛选增强样式 */
.search-filters {
    display: grid;
    grid-template-columns: 2fr repeat(5, 1fr) auto;
    gap: 15px;
    align-items: center;
}

@media (max-width: 1200px) {
    .search-filters {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .search-filters {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .table-container {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 8px 4px;
    }

    .btn-sm {
        padding: 2px 6px;
        font-size: 10px;
    }
}

/* 统计分析页面专用样式 */
.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-legend {
    display: flex;
    gap: 15px;
    align-items: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: var(--text-secondary);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.peak-analysis {
    padding: 20px 0;
}

.peak-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.peak-item {
    background: var(--light-bg);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.peak-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.peak-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.peak-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-high {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-medium {
    background-color: #fef3c7;
    color: #92400e;
}

.status-low {
    background-color: #dcfce7;
    color: #166534;
}

.peak-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.peak-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.peak-stat .stat-label {
    color: var(--text-secondary);
    font-size: 14px;
}

.peak-stat .stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

.congestion-high {
    color: var(--danger-color);
}

.congestion-medium {
    color: var(--warning-color);
}

.congestion-low {
    color: var(--success-color);
}

/* 图表容器增强 */
.chart-card {
    min-height: 450px;
}

.chart-container {
    position: relative;
    height: 350px;
    margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .peak-grid {
        grid-template-columns: 1fr;
    }

    .chart-legend {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .peak-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .chart-card {
        min-height: 400px;
    }

    .chart-container {
        height: 300px;
    }
}

/* 报警管理页面专用样式 */
.alert-count {
    color: var(--text-secondary);
    font-size: 14px;
    margin-right: 15px;
}

.alert-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.alert-card {
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.alert-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.alert-card.unread {
    border-left: 4px solid var(--primary-color);
    background: #f8fafc;
}

.alert-card.unread::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.alert-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.alert-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--primary-color);
}

.alert-info {
    flex: 1;
}

.alert-type {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.alert-time {
    font-size: 12px;
    color: var(--text-secondary);
}

.alert-card-body {
    margin-bottom: 15px;
}

.alert-location {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.alert-description {
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
}

.alert-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-actions {
    display: flex;
    gap: 8px;
}

.no-alerts {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.no-alerts i {
    font-size: 64px;
    margin-bottom: 20px;
    display: block;
}

/* 表格视图样式 */
.unread-row {
    background-color: #f8fafc;
    border-left: 4px solid var(--primary-color);
}

.alert-type-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-desc-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-actions {
    display: flex;
    gap: 5px;
}

/* 状态样式 */
.status-pending {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-processing {
    background-color: #fef3c7;
    color: #92400e;
}

.status-resolved {
    background-color: #dcfce7;
    color: #166534;
}

.status-ignored {
    background-color: #f1f5f9;
    color: #475569;
}

/* 模态框增强 */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    margin-top: 20px;
}

.alert-description-full {
    background: var(--light-bg);
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
    line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .alert-cards {
        grid-template-columns: 1fr;
    }

    .alert-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .alert-card-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .alert-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .modal-footer {
        flex-direction: column;
    }

    .table-actions {
        flex-direction: column;
        gap: 2px;
    }
}

/* 设备管理页面专用样式 */
.device-count {
    color: var(--text-secondary);
    font-size: 14px;
    margin-right: 15px;
}

.device-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.device-card {
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.device-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.device-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.device-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--primary-color);
}

.device-info {
    flex: 1;
}

.device-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.device-id {
    font-size: 12px;
    color: var(--text-secondary);
    font-family: monospace;
}

.device-status {
    display: flex;
    align-items: center;
}

.device-card-body {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.device-detail {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.no-devices {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.no-devices i {
    font-size: 64px;
    margin-bottom: 20px;
    display: block;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .device-cards {
        grid-template-columns: 1fr;
    }

    .device-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}