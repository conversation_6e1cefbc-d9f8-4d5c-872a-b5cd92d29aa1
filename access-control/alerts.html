<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警管理 - 门禁系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    报警管理
                </h1>
                <div class="page-actions">
                    <button class="btn btn-warning" onclick="testAlert()">
                        <i class="fas fa-bell"></i>
                        测试报警
                    </button>
                    <button class="btn btn-success" onclick="markAllRead()">
                        <i class="fas fa-check-double"></i>
                        全部已读
                    </button>
                    <button class="btn btn-primary" onclick="refreshAlerts()">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                </div>
            </div>

            <!-- 报警统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-circle" style="color: var(--danger-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalAlerts">23</div>
                        <div class="stat-label">待处理报警</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-up"></i>
                            +3 新增
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-friends" style="color: var(--warning-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="tailgatingAlerts">8</div>
                        <div class="stat-label">尾随检测</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i>
                            最近5分钟
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shield-alt" style="color: var(--info-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="intrusionAlerts">5</div>
                        <div class="stat-label">非法入侵</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i>
                            最近15分钟
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tools" style="color: var(--secondary-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="deviceAlerts">10</div>
                        <div class="stat-label">设备故障</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i>
                            最近1小时
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报警筛选 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">筛选条件</h3>
                    <div class="card-actions">
                        <button class="btn btn-outline" onclick="resetAlertFilters()">
                            <i class="fas fa-undo"></i>
                            重置筛选
                        </button>
                    </div>
                </div>
                <div class="search-filters">
                    <select class="filter-select" id="alertTypeFilter" onchange="applyAlertFilters()">
                        <option value="">全部类型</option>
                        <option value="尾随检测">尾随检测</option>
                        <option value="非法入侵">非法入侵</option>
                        <option value="设备故障">设备故障</option>
                        <option value="长时间滞留">长时间滞留</option>
                    </select>

                    <select class="filter-select" id="alertLevelFilter" onchange="applyAlertFilters()">
                        <option value="">全部级别</option>
                        <option value="高">高级别</option>
                        <option value="中">中级别</option>
                        <option value="低">低级别</option>
                    </select>

                    <select class="filter-select" id="alertStatusFilter" onchange="applyAlertFilters()">
                        <option value="">全部状态</option>
                        <option value="待处理">待处理</option>
                        <option value="处理中">处理中</option>
                        <option value="已处理">已处理</option>
                        <option value="已忽略">已忽略</option>
                    </select>

                    <select class="filter-select" id="alertChannelFilter" onchange="applyAlertFilters()">
                        <option value="">全部通道</option>
                        <option value="主入口">主入口</option>
                        <option value="侧门A">侧门A</option>
                        <option value="侧门B">侧门B</option>
                        <option value="地下车库">地下车库</option>
                        <option value="后门">后门</option>
                        <option value="紧急出口">紧急出口</option>
                    </select>

                    <input type="date" class="filter-select" id="alertDateFilter" onchange="applyAlertFilters()">

                    <button class="btn btn-primary" onclick="applyAlertFilters()">
                        <i class="fas fa-search"></i>
                        筛选
                    </button>
                </div>
            </div>

            <!-- 报警列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">报警事件列表</h3>
                    <div class="card-actions">
                        <span class="alert-count">共 <span id="alertCount">0</span> 条报警</span>
                        <button class="btn btn-outline btn-sm" onclick="toggleAlertView()">
                            <i class="fas fa-th-list" id="viewToggleIcon"></i>
                            <span id="viewToggleText">列表视图</span>
                        </button>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div class="alert-cards" id="alertCards">
                    <!-- 报警卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 表格视图 -->
                <div class="table-container" id="alertTable" style="display: none;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>类型</th>
                                <th>级别</th>
                                <th>通道</th>
                                <th>描述</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="alertTableBody">
                            <!-- 表格内容将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination" id="alertPagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 报警详情模态框 -->
    <div class="modal" id="alertDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">报警详情</h3>
                <button class="modal-close" onclick="closeAlertDetail()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="alertDetailContent">
                <!-- 详情内容将通过JavaScript动态生成 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="handleAlert('resolve')">
                    <i class="fas fa-check"></i>
                    标记已处理
                </button>
                <button class="btn btn-warning" onclick="handleAlert('ignore')">
                    <i class="fas fa-eye-slash"></i>
                    忽略报警
                </button>
                <button class="btn btn-outline" onclick="closeAlertDetail()">
                    取消
                </button>
            </div>
        </div>
    </div>

    <!-- 处理报警模态框 -->
    <div class="modal" id="handleAlertModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">处理报警</h3>
                <button class="modal-close" onclick="closeHandleAlert()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">处理方式:</label>
                    <select class="form-input" id="handleMethod">
                        <option value="resolve">标记已处理</option>
                        <option value="ignore">忽略报警</option>
                        <option value="escalate">升级处理</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">处理说明:</label>
                    <textarea class="form-input" id="handleNote" rows="4" placeholder="请输入处理说明..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="submitAlertHandle()">
                    <i class="fas fa-save"></i>
                    提交处理
                </button>
                <button class="btn btn-outline" onclick="closeHandleAlert()">
                    取消
                </button>
            </div>
        </div>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script src="alerts.js"></script>
</body>
</html>