<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - 门禁系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </h1>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        刷新数据
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users" style="color: var(--primary-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="todayTotal">1,234</div>
                        <div class="stat-label">今日通行总数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12.5%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-tie" style="color: var(--success-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="employeeCount">856</div>
                        <div class="stat-label">员工通行</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8.3%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-friends" style="color: var(--warning-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="visitorCount">378</div>
                        <div class="stat-label">访客通行</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15.2%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="alertCount">3</div>
                        <div class="stat-label">待处理报警</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -25.0%
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表和设备状态 -->
            <div class="dashboard-grid">
                <!-- 通行趋势图表 -->
                <div class="card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">今日通行趋势</h3>
                        <div class="card-actions">
                            <select class="filter-select" id="trendFilter">
                                <option value="today">今日</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>

                <!-- 设备状态监控 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">设备状态监控</h3>
                        <div class="card-actions">
                            <span class="status-summary">
                                <span class="status-indicator status-online">
                                    <i class="fas fa-circle"></i>
                                    在线: 38
                                </span>
                                <span class="status-indicator status-offline">
                                    <i class="fas fa-circle"></i>
                                    离线: 2
                                </span>
                                <span class="status-indicator status-warning">
                                    <i class="fas fa-circle"></i>
                                    异常: 2
                                </span>
                            </span>
                        </div>
                    </div>
                    <div class="device-grid" id="deviceGrid">
                        <!-- 设备状态将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 最近通行记录和报警事件 -->
            <div class="dashboard-grid">
                <!-- 最近通行记录 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近通行记录</h3>
                        <div class="card-actions">
                            <a href="records.html" class="btn btn-outline">
                                查看全部
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>类型</th>
                                    <th>通道</th>
                                    <th>方向</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody id="recentRecords">
                                <!-- 记录将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 最近报警事件 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近报警事件</h3>
                        <div class="card-actions">
                            <a href="alerts.html" class="btn btn-outline">
                                查看全部
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    <div class="alert-list" id="recentAlerts">
                        <!-- 报警事件将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>