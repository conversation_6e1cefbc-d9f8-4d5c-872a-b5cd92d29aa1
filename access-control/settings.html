<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 门禁系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-sliders-h"></i>
                    系统设置
                </h1>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="saveSettings()">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                    <button class="btn btn-warning" onclick="resetSettings()">
                        <i class="fas fa-undo"></i>
                        重置默认
                    </button>
                </div>
            </div>

            <!-- 设置选项卡 -->
            <div class="settings-tabs">
                <div class="tab-nav">
                    <button class="tab-btn active" onclick="switchTab('general')">
                        <i class="fas fa-cog"></i>
                        基本设置
                    </button>
                    <button class="tab-btn" onclick="switchTab('security')">
                        <i class="fas fa-shield-alt"></i>
                        安全设置
                    </button>
                    <button class="tab-btn" onclick="switchTab('notification')">
                        <i class="fas fa-bell"></i>
                        通知设置
                    </button>
                    <button class="tab-btn" onclick="switchTab('users')">
                        <i class="fas fa-users"></i>
                        用户管理
                    </button>
                </div>

                <!-- 基本设置 -->
                <div class="tab-content active" id="general">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">系统基本配置</h3>
                        </div>
                        <div class="settings-form">
                            <div class="form-group">
                                <label class="form-label">系统名称</label>
                                <input type="text" class="form-input" value="智能门禁管理系统" id="systemName">
                            </div>
                            <div class="form-group">
                                <label class="form-label">系统版本</label>
                                <input type="text" class="form-input" value="v2.1.0" readonly>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据刷新间隔（秒）</label>
                                <input type="number" class="form-input" value="30" id="refreshInterval">
                            </div>
                            <div class="form-group">
                                <label class="form-label">日志保留天数</label>
                                <input type="number" class="form-input" value="90" id="logRetention">
                            </div>
                            <div class="form-group">
                                <label class="form-label">启用调试模式</label>
                                <label class="switch">
                                    <input type="checkbox" id="debugMode">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div class="tab-content" id="security">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">安全策略配置</h3>
                        </div>
                        <div class="settings-form">
                            <div class="form-group">
                                <label class="form-label">会话超时时间（分钟）</label>
                                <input type="number" class="form-input" value="30" id="sessionTimeout">
                            </div>
                            <div class="form-group">
                                <label class="form-label">密码最小长度</label>
                                <input type="number" class="form-input" value="8" id="minPasswordLength">
                            </div>
                            <div class="form-group">
                                <label class="form-label">登录失败锁定次数</label>
                                <input type="number" class="form-input" value="5" id="maxLoginAttempts">
                            </div>
                            <div class="form-group">
                                <label class="form-label">启用双因子认证</label>
                                <label class="switch">
                                    <input type="checkbox" id="twoFactorAuth">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">启用IP白名单</label>
                                <label class="switch">
                                    <input type="checkbox" id="ipWhitelist">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div class="tab-content" id="notification">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">通知配置</h3>
                        </div>
                        <div class="settings-form">
                            <div class="form-group">
                                <label class="form-label">邮件服务器</label>
                                <input type="text" class="form-input" value="smtp.company.com" id="emailServer">
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮件端口</label>
                                <input type="number" class="form-input" value="587" id="emailPort">
                            </div>
                            <div class="form-group">
                                <label class="form-label">发送邮箱</label>
                                <input type="email" class="form-input" value="<EMAIL>" id="senderEmail">
                            </div>
                            <div class="form-group">
                                <label class="form-label">启用邮件通知</label>
                                <label class="switch">
                                    <input type="checkbox" checked id="emailNotification">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">启用短信通知</label>
                                <label class="switch">
                                    <input type="checkbox" id="smsNotification">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理 -->
                <div class="tab-content" id="users">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">用户管理</h3>
                            <div class="card-actions">
                                <button class="btn btn-primary" onclick="addUser()">
                                    <i class="fas fa-plus"></i>
                                    添加用户
                                </button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>用户名</th>
                                        <th>姓名</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>admin</td>
                                        <td>系统管理员</td>
                                        <td><span class="badge badge-primary">管理员</span></td>
                                        <td><span class="status-indicator status-online"><i class="fas fa-circle"></i>活跃</span></td>
                                        <td>2024-01-15 14:30</td>
                                        <td>
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-key"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>operator</td>
                                        <td>操作员</td>
                                        <td><span class="badge badge-warning">操作员</span></td>
                                        <td><span class="status-indicator status-online"><i class="fas fa-circle"></i>活跃</span></td>
                                        <td>2024-01-15 09:15</td>
                                        <td>
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline btn-sm">
                                                <i class="fas fa-key"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadSidebarComponent();
        });

        function loadSidebarComponent() {
            fetch('components/sidebar.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('sidebar-container').innerHTML = html;
                })
                .catch(error => console.error('Failed to load sidebar:', error));
        }

        function switchTab(tabName) {
            // 移除所有活跃状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 激活选中的选项卡
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        function saveSettings() {
            Utils.showNotification('设置保存成功', 'success');
        }

        function resetSettings() {
            Utils.confirm('确定要重置所有设置为默认值吗？', () => {
                Utils.showNotification('设置已重置为默认值', 'info');
            });
        }

        function addUser() {
            Utils.showNotification('添加用户功能开发中...', 'info');
        }
    </script>

    <style>
        /* 设置页面专用样式 */
        .settings-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .tab-nav {
            display: flex;
            background: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            transition: all 0.2s;
        }

        .tab-btn:hover {
            background: white;
            color: var(--text-primary);
        }

        .tab-btn.active {
            background: white;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .settings-form {
            display: grid;
            gap: 20px;
            max-width: 600px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        @media (max-width: 768px) {
            .tab-nav {
                flex-direction: column;
            }

            .tab-content {
                padding: 20px;
            }
        }
    </style>
</body>
</html>