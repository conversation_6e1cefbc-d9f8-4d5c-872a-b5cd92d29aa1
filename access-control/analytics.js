// 统计分析页面JavaScript

let charts = {
    trend: null,
    channel: null,
    type: null,
    method: null
};

let chartTypes = {
    channel: 'doughnut',
    type: 'pie'
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initAnalytics();
    loadSidebarComponent();
});

// 加载侧边栏组件
function loadSidebarComponent() {
    fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('sidebar-container').innerHTML = html;
            // 执行侧边栏中的脚本
            const scripts = document.getElementById('sidebar-container').querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.body.appendChild(newScript);
            });
        })
        .catch(error => {
            console.error('Failed to load sidebar:', error);
        });
}

// 初始化分析页面
function initAnalytics() {
    loadKeyMetrics();
    initTrendChart();
    initChannelChart();
    initTypeChart();
    initMethodChart();
}

// 加载关键指标
function loadKeyMetrics() {
    const metrics = {
        totalTraffic: Math.floor(Math.random() * 50000) + 40000,
        peakHour: '09:00',
        successRate: (99.5 + Math.random() * 0.5).toFixed(1),
        avgTime: (2.0 + Math.random() * 1.0).toFixed(1)
    };

    document.getElementById('totalTraffic').textContent = metrics.totalTraffic.toLocaleString();
    document.getElementById('peakHour').textContent = metrics.peakHour;
    document.getElementById('successRate').textContent = metrics.successRate + '%';
    document.getElementById('avgTime').textContent = metrics.avgTime + 's';
}

// 初始化趋势图表
function initTrendChart() {
    const ctx = document.getElementById('trendChart').getContext('2d');

    const data = generateTrendData('daily');

    charts.trend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: '员工通行',
                data: data.employee,
                borderColor: CONFIG.CHART_COLORS.primary,
                backgroundColor: CONFIG.CHART_COLORS.primary + '20',
                tension: 0.4,
                fill: true
            }, {
                label: '访客通行',
                data: data.visitor,
                borderColor: CONFIG.CHART_COLORS.warning,
                backgroundColor: CONFIG.CHART_COLORS.warning + '20',
                tension: 0.4,
                fill: true
            }, {
                label: '临时工通行',
                data: data.temp,
                borderColor: CONFIG.CHART_COLORS.info,
                backgroundColor: CONFIG.CHART_COLORS.info + '20',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#e2e8f0'
                    }
                },
                x: {
                    grid: {
                        color: '#e2e8f0'
                    }
                }
            }
        }
    });
}

// 初始化通道分布图表
function initChannelChart() {
    const ctx = document.getElementById('channelChart').getContext('2d');

    const data = {
        labels: ['主入口', '侧门A', '侧门B', '地下车库', '后门', '紧急出口'],
        datasets: [{
            data: [35, 20, 15, 18, 8, 4],
            backgroundColor: [
                CONFIG.CHART_COLORS.primary,
                CONFIG.CHART_COLORS.success,
                CONFIG.CHART_COLORS.warning,
                CONFIG.CHART_COLORS.info,
                CONFIG.CHART_COLORS.danger,
                '#6b7280'
            ]
        }]
    };

    charts.channel = new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

// 初始化人员类型图表
function initTypeChart() {
    const ctx = document.getElementById('typeChart').getContext('2d');

    const data = {
        labels: ['员工', '访客', '临时工'],
        datasets: [{
            data: [65, 25, 10],
            backgroundColor: [
                CONFIG.CHART_COLORS.primary,
                CONFIG.CHART_COLORS.warning,
                CONFIG.CHART_COLORS.info
            ]
        }]
    };

    charts.type = new Chart(ctx, {
        type: 'pie',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

// 初始化验证方式图表
function initMethodChart() {
    const ctx = document.getElementById('methodChart').getContext('2d');

    const data = {
        labels: ['刷卡', '人脸识别', '二维码'],
        datasets: [{
            label: '使用次数',
            data: [45, 35, 20],
            backgroundColor: [
                CONFIG.CHART_COLORS.primary,
                CONFIG.CHART_COLORS.success,
                CONFIG.CHART_COLORS.warning
            ]
        }]
    };

    charts.method = new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#e2e8f0'
                    }
                },
                x: {
                    grid: {
                        color: '#e2e8f0'
                    }
                }
            }
        }
    });
}

// 生成趋势数据
function generateTrendData(type) {
    let labels = [];
    let employee = [];
    let visitor = [];
    let temp = [];

    switch (type) {
        case 'hourly':
            for (let i = 0; i < 24; i++) {
                labels.push(`${i.toString().padStart(2, '0')}:00`);
                employee.push(Math.floor(Math.random() * 200) + 50);
                visitor.push(Math.floor(Math.random() * 100) + 20);
                temp.push(Math.floor(Math.random() * 50) + 10);
            }
            break;
        case 'daily':
            for (let i = 1; i <= 30; i++) {
                labels.push(`${i}日`);
                employee.push(Math.floor(Math.random() * 2000) + 1000);
                visitor.push(Math.floor(Math.random() * 800) + 400);
                temp.push(Math.floor(Math.random() * 300) + 100);
            }
            break;
        case 'weekly':
            const weeks = ['第1周', '第2周', '第3周', '第4周'];
            labels = weeks;
            for (let i = 0; i < 4; i++) {
                employee.push(Math.floor(Math.random() * 10000) + 5000);
                visitor.push(Math.floor(Math.random() * 4000) + 2000);
                temp.push(Math.floor(Math.random() * 1500) + 500);
            }
            break;
    }

    return { labels, employee, visitor, temp };
}

// 更新趋势图表
function updateTrendChart() {
    const type = document.getElementById('trendType').value;
    const data = generateTrendData(type);

    charts.trend.data.labels = data.labels;
    charts.trend.data.datasets[0].data = data.employee;
    charts.trend.data.datasets[1].data = data.visitor;
    charts.trend.data.datasets[2].data = data.temp;
    charts.trend.update();
}

// 切换图表类型
function toggleChartType(chartName) {
    if (chartName === 'channel') {
        const newType = chartTypes.channel === 'doughnut' ? 'bar' : 'doughnut';
        chartTypes.channel = newType;

        // 重新创建图表
        charts.channel.destroy();
        initChannelChart();

        if (newType === 'bar') {
            charts.channel.config.type = 'bar';
            charts.channel.config.options.plugins.legend.display = false;
            charts.channel.config.options.scales = {
                y: { beginAtZero: true, grid: { color: '#e2e8f0' } },
                x: { grid: { color: '#e2e8f0' } }
            };
        }
        charts.channel.update();

    } else if (chartName === 'type') {
        const newType = chartTypes.type === 'pie' ? 'bar' : 'pie';
        chartTypes.type = newType;

        // 重新创建图表
        charts.type.destroy();
        initTypeChart();

        if (newType === 'bar') {
            charts.type.config.type = 'bar';
            charts.type.config.options.plugins.legend.display = false;
            charts.type.config.options.scales = {
                y: { beginAtZero: true, grid: { color: '#e2e8f0' } },
                x: { grid: { color: '#e2e8f0' } }
            };
        }
        charts.type.update();
    }
}

// 改变时间周期
function changePeriod() {
    const period = document.getElementById('periodFilter').value;

    // 更新关键指标
    loadKeyMetrics();

    // 更新所有图表
    updateAllCharts();

    Utils.showNotification(`已切换到${getPeriodText(period)}数据`, 'info');
}

// 获取周期文本
function getPeriodText(period) {
    const periodMap = {
        today: '今日',
        week: '本周',
        month: '本月',
        quarter: '本季度',
        year: '本年'
    };
    return periodMap[period] || '本月';
}

// 更新所有图表
function updateAllCharts() {
    // 更新趋势图表
    updateTrendChart();

    // 更新其他图表数据
    updateChannelData();
    updateTypeData();
    updateMethodData();
}

// 更新通道数据
function updateChannelData() {
    const newData = [
        Math.floor(Math.random() * 40) + 30,
        Math.floor(Math.random() * 25) + 15,
        Math.floor(Math.random() * 20) + 10,
        Math.floor(Math.random() * 25) + 15,
        Math.floor(Math.random() * 15) + 5,
        Math.floor(Math.random() * 10) + 2
    ];

    charts.channel.data.datasets[0].data = newData;
    charts.channel.update();
}

// 更新类型数据
function updateTypeData() {
    const newData = [
        Math.floor(Math.random() * 20) + 60,
        Math.floor(Math.random() * 15) + 20,
        Math.floor(Math.random() * 10) + 5
    ];

    charts.type.data.datasets[0].data = newData;
    charts.type.update();
}

// 更新验证方式数据
function updateMethodData() {
    const newData = [
        Math.floor(Math.random() * 20) + 40,
        Math.floor(Math.random() * 20) + 30,
        Math.floor(Math.random() * 15) + 15
    ];

    charts.method.data.datasets[0].data = newData;
    charts.method.update();
}

// 刷新分析数据
function refreshAnalytics() {
    Utils.showNotification('正在刷新分析数据...', 'info', 1000);

    setTimeout(() => {
        loadKeyMetrics();
        updateAllCharts();
        Utils.showNotification('分析数据已刷新', 'success');
    }, 1000);
}

// 导出分析报告
function exportAnalytics() {
    Utils.showNotification('正在生成分析报告...', 'info');

    // 模拟导出过程
    setTimeout(() => {
        const period = getPeriodText(document.getElementById('periodFilter').value);
        Utils.showNotification(`${period}分析报告已导出`, 'success');
    }, 2000);
}

// 生成高峰期报告
function generatePeakReport() {
    Utils.showNotification('正在生成高峰期分析报告...', 'info');

    // 模拟报告生成
    setTimeout(() => {
        Utils.showNotification('高峰期分析报告已生成', 'success');
    }, 1500);
}