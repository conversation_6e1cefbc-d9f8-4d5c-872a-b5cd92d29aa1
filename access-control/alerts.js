// 报警管理页面JavaScript

let allAlerts = [];
let filteredAlerts = [];
let currentPage = 1;
let pageSize = 10;
let isCardView = true;
let currentAlert = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initAlerts();
    loadSidebarComponent();
});

// 加载侧边栏组件
function loadSidebarComponent() {
    fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('sidebar-container').innerHTML = html;
            // 执行侧边栏中的脚本
            const scripts = document.getElementById('sidebar-container').querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.body.appendChild(newScript);
            });
        })
        .catch(error => {
            console.error('Failed to load sidebar:', error);
        });
}

// 初始化报警页面
function initAlerts() {
    loadAlertStatistics();
    generateMockAlerts();
    applyAlertFilters();

    // 设置默认日期为今天
    document.getElementById('alertDateFilter').value = new Date().toISOString().split('T')[0];

    // 定时刷新报警数据
    setInterval(checkNewAlerts, 30000); // 每30秒检查新报警
}

// 加载报警统计
function loadAlertStatistics() {
    const stats = {
        totalAlerts: Math.floor(Math.random() * 30) + 15,
        tailgatingAlerts: Math.floor(Math.random() * 10) + 5,
        intrusionAlerts: Math.floor(Math.random() * 8) + 3,
        deviceAlerts: Math.floor(Math.random() * 15) + 8
    };

    document.getElementById('totalAlerts').textContent = stats.totalAlerts;
    document.getElementById('tailgatingAlerts').textContent = stats.tailgatingAlerts;
    document.getElementById('intrusionAlerts').textContent = stats.intrusionAlerts;
    document.getElementById('deviceAlerts').textContent = stats.deviceAlerts;
}

// 生成模拟报警数据
function generateMockAlerts() {
    allAlerts = [];

    // 生成50条模拟报警
    for (let i = 0; i < 50; i++) {
        const alert = MockData.generateAlert();
        alert.id = i + 1;
        // 设置时间为最近24小时内的随机时间
        alert.time = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);
        alert.isRead = Math.random() > 0.3; // 70%已读
        alert.handler = alert.status === '已处理' ? '张管理员' : null;
        alert.handleTime = alert.status === '已处理' ? new Date(alert.time.getTime() + Math.random() * 60 * 60 * 1000) : null;
        allAlerts.push(alert);
    }

    // 按时间排序
    allAlerts.sort((a, b) => b.time - a.time);
}

// 应用报警筛选
function applyAlertFilters() {
    const typeFilter = document.getElementById('alertTypeFilter').value;
    const levelFilter = document.getElementById('alertLevelFilter').value;
    const statusFilter = document.getElementById('alertStatusFilter').value;
    const channelFilter = document.getElementById('alertChannelFilter').value;
    const dateFilter = document.getElementById('alertDateFilter').value;

    filteredAlerts = allAlerts.filter(alert => {
        const matchesType = !typeFilter || alert.type === typeFilter;
        const matchesLevel = !levelFilter || alert.level === levelFilter;
        const matchesStatus = !statusFilter || alert.status === statusFilter;
        const matchesChannel = !channelFilter || alert.channel === channelFilter;
        const matchesDate = !dateFilter ||
            alert.time.toISOString().split('T')[0] === dateFilter;

        return matchesType && matchesLevel && matchesStatus &&
               matchesChannel && matchesDate;
    });

    // 重置到第一页
    currentPage = 1;

    // 更新显示
    updateAlertsDisplay();
    updateAlertPagination();
}

// 更新报警显示
function updateAlertsDisplay() {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageAlerts = filteredAlerts.slice(startIndex, endIndex);

    // 更新报警数量
    document.getElementById('alertCount').textContent = filteredAlerts.length;

    if (isCardView) {
        updateCardView(pageAlerts);
    } else {
        updateTableView(pageAlerts);
    }
}

// 更新卡片视图
function updateCardView(alerts) {
    const container = document.getElementById('alertCards');

    if (alerts.length === 0) {
        container.innerHTML = `
            <div class="no-alerts">
                <i class="fas fa-shield-alt"></i>
                <p>暂无符合条件的报警</p>
            </div>
        `;
        return;
    }

    container.innerHTML = alerts.map(alert => `
        <div class="alert-card ${!alert.isRead ? 'unread' : ''}" onclick="showAlertDetail(${alert.id})">
            <div class="alert-card-header">
                <div class="alert-type-icon">
                    <i class="fas fa-${getAlertIcon(alert.type)}"></i>
                </div>
                <div class="alert-info">
                    <div class="alert-type">${alert.type}</div>
                    <div class="alert-time">${Utils.formatDateTime(alert.time)}</div>
                </div>
                <div class="alert-level level-${getLevelClass(alert.level)}">
                    ${alert.level}
                </div>
            </div>
            <div class="alert-card-body">
                <div class="alert-location">
                    <i class="fas fa-map-marker-alt"></i>
                    ${alert.channel}
                </div>
                <div class="alert-description">
                    ${alert.description}
                </div>
            </div>
            <div class="alert-card-footer">
                <div class="alert-status">
                    <span class="status-badge status-${getStatusClass(alert.status)}">
                        ${alert.status}
                    </span>
                </div>
                <div class="alert-actions">
                    <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); quickHandle(${alert.id}, 'resolve')">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); quickHandle(${alert.id}, 'ignore')">
                        <i class="fas fa-eye-slash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// 更新表格视图
function updateTableView(alerts) {
    const tbody = document.getElementById('alertTableBody');

    if (alerts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                    <i class="fas fa-shield-alt" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
                    暂无符合条件的报警
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = alerts.map(alert => `
        <tr class="${!alert.isRead ? 'unread-row' : ''}" onclick="showAlertDetail(${alert.id})">
            <td>${Utils.formatDateTime(alert.time)}</td>
            <td>
                <div class="alert-type-cell">
                    <i class="fas fa-${getAlertIcon(alert.type)}"></i>
                    ${alert.type}
                </div>
            </td>
            <td>
                <span class="alert-level level-${getLevelClass(alert.level)}">
                    ${alert.level}
                </span>
            </td>
            <td>${alert.channel}</td>
            <td class="alert-desc-cell">${alert.description}</td>
            <td>
                <span class="status-badge status-${getStatusClass(alert.status)}">
                    ${alert.status}
                </span>
            </td>
            <td>
                <div class="table-actions">
                    <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); quickHandle(${alert.id}, 'resolve')" title="标记已处理">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); quickHandle(${alert.id}, 'ignore')" title="忽略">
                        <i class="fas fa-eye-slash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 获取报警图标
function getAlertIcon(type) {
    const iconMap = {
        '尾随检测': 'user-friends',
        '非法入侵': 'exclamation-triangle',
        '设备故障': 'tools',
        '长时间滞留': 'clock'
    };
    return iconMap[type] || 'exclamation-circle';
}

// 获取级别样式类
function getLevelClass(level) {
    const levelMap = {
        '高': 'high',
        '中': 'medium',
        '低': 'low'
    };
    return levelMap[level] || 'medium';
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        '待处理': 'pending',
        '处理中': 'processing',
        '已处理': 'resolved',
        '已忽略': 'ignored'
    };
    return statusMap[status] || 'pending';
}

// 切换视图模式
function toggleAlertView() {
    isCardView = !isCardView;

    const cardContainer = document.getElementById('alertCards');
    const tableContainer = document.getElementById('alertTable');
    const toggleIcon = document.getElementById('viewToggleIcon');
    const toggleText = document.getElementById('viewToggleText');

    if (isCardView) {
        cardContainer.style.display = 'block';
        tableContainer.style.display = 'none';
        toggleIcon.className = 'fas fa-th-list';
        toggleText.textContent = '列表视图';
    } else {
        cardContainer.style.display = 'none';
        tableContainer.style.display = 'block';
        toggleIcon.className = 'fas fa-th-large';
        toggleText.textContent = '卡片视图';
    }

    updateAlertsDisplay();
}

// 显示报警详情
function showAlertDetail(alertId) {
    const alert = allAlerts.find(a => a.id === alertId);
    if (!alert) return;

    currentAlert = alert;

    // 标记为已读
    if (!alert.isRead) {
        alert.isRead = true;
        updateAlertsDisplay();
    }

    const modal = document.getElementById('alertDetailModal');
    const content = document.getElementById('alertDetailContent');

    content.innerHTML = `
        <div class="alert-detail">
            <div class="detail-section">
                <h4 class="detail-title">基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>报警ID:</label>
                        <span>${alert.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>报警时间:</label>
                        <span>${Utils.formatDateTime(alert.time)}</span>
                    </div>
                    <div class="detail-item">
                        <label>报警类型:</label>
                        <span>
                            <i class="fas fa-${getAlertIcon(alert.type)}"></i>
                            ${alert.type}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>报警级别:</label>
                        <span class="alert-level level-${getLevelClass(alert.level)}">${alert.level}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4 class="detail-title">位置信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>发生通道:</label>
                        <span>${alert.channel}</span>
                    </div>
                    <div class="detail-item">
                        <label>设备编号:</label>
                        <span>DEV${Math.floor(Math.random() * 42) + 1}</span>
                    </div>
                    <div class="detail-item">
                        <label>摄像头:</label>
                        <span>CAM${Math.floor(Math.random() * 20) + 1}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4 class="detail-title">详细描述</h4>
                <p class="alert-description-full">${alert.description}</p>
            </div>

            <div class="detail-section">
                <h4 class="detail-title">处理状态</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>当前状态:</label>
                        <span class="status-badge status-${getStatusClass(alert.status)}">${alert.status}</span>
                    </div>
                    ${alert.handler ? `
                        <div class="detail-item">
                            <label>处理人员:</label>
                            <span>${alert.handler}</span>
                        </div>
                        <div class="detail-item">
                            <label>处理时间:</label>
                            <span>${Utils.formatDateTime(alert.handleTime)}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    modal.classList.add('show');
}

// 关闭报警详情
function closeAlertDetail() {
    document.getElementById('alertDetailModal').classList.remove('show');
    currentAlert = null;
}

// 快速处理报警
function quickHandle(alertId, action) {
    const alert = allAlerts.find(a => a.id === alertId);
    if (!alert) return;

    const actionText = action === 'resolve' ? '已处理' : '已忽略';

    Utils.confirm(`确定要将此报警标记为"${actionText}"吗？`, () => {
        alert.status = actionText;
        alert.handler = '当前用户';
        alert.handleTime = new Date();

        updateAlertsDisplay();
        loadAlertStatistics();

        Utils.showNotification(`报警已标记为${actionText}`, 'success');
    });
}

// 处理报警（从详情页）
function handleAlert(action) {
    if (!currentAlert) return;

    document.getElementById('handleMethod').value = action;
    document.getElementById('alertDetailModal').classList.remove('show');
    document.getElementById('handleAlertModal').classList.add('show');
}

// 关闭处理报警模态框
function closeHandleAlert() {
    document.getElementById('handleAlertModal').classList.remove('show');
}

// 提交报警处理
function submitAlertHandle() {
    const method = document.getElementById('handleMethod').value;
    const note = document.getElementById('handleNote').value;

    if (!currentAlert) return;

    const statusMap = {
        resolve: '已处理',
        ignore: '已忽略',
        escalate: '处理中'
    };

    currentAlert.status = statusMap[method];
    currentAlert.handler = '当前用户';
    currentAlert.handleTime = new Date();
    currentAlert.handleNote = note;

    closeHandleAlert();
    updateAlertsDisplay();
    loadAlertStatistics();

    Utils.showNotification('报警处理完成', 'success');
}

// 重置报警筛选
function resetAlertFilters() {
    document.getElementById('alertTypeFilter').value = '';
    document.getElementById('alertLevelFilter').value = '';
    document.getElementById('alertStatusFilter').value = '';
    document.getElementById('alertChannelFilter').value = '';
    document.getElementById('alertDateFilter').value = '';

    applyAlertFilters();
    Utils.showNotification('筛选条件已重置', 'info');
}

// 全部标记为已读
function markAllRead() {
    const unreadCount = allAlerts.filter(a => !a.isRead).length;

    if (unreadCount === 0) {
        Utils.showNotification('所有报警都已标记为已读', 'info');
        return;
    }

    Utils.confirm(`确定要将 ${unreadCount} 条未读报警标记为已读吗？`, () => {
        allAlerts.forEach(alert => alert.isRead = true);
        updateAlertsDisplay();
        Utils.showNotification(`已标记 ${unreadCount} 条报警为已读`, 'success');
    });
}

// 测试报警
function testAlert() {
    const testAlert = {
        id: Date.now(),
        type: '测试报警',
        level: '低',
        channel: '主入口',
        time: new Date(),
        status: '待处理',
        description: '这是一条测试报警，用于验证系统功能',
        isRead: false,
        handler: null,
        handleTime: null
    };

    allAlerts.unshift(testAlert);
    applyAlertFilters();
    loadAlertStatistics();

    Utils.showNotification('测试报警已生成', 'success');
}

// 刷新报警
function refreshAlerts() {
    Utils.showNotification('正在刷新报警数据...', 'info', 1000);

    setTimeout(() => {
        generateMockAlerts();
        applyAlertFilters();
        loadAlertStatistics();
        Utils.showNotification('报警数据已刷新', 'success');
    }, 1000);
}

// 检查新报警
function checkNewAlerts() {
    // 模拟新报警检查
    if (Math.random() > 0.8) { // 20%概率有新报警
        const newAlert = MockData.generateAlert();
        newAlert.id = Date.now();
        newAlert.time = new Date();
        newAlert.isRead = false;

        allAlerts.unshift(newAlert);
        applyAlertFilters();
        loadAlertStatistics();

        // 显示新报警通知
        Utils.showNotification(`新报警: ${newAlert.type} - ${newAlert.channel}`, 'warning', 5000);
    }
}

// 更新报警分页
function updateAlertPagination() {
    const totalPages = Math.ceil(filteredAlerts.length / pageSize);
    const pagination = document.getElementById('alertPagination');

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // 上一页按钮
    paginationHTML += `
        <button onclick="changeAlertPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
            上一页
        </button>
    `;

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button onclick="changeAlertPage(${i})" ${i === currentPage ? 'class="active"' : ''}>
                ${i}
            </button>
        `;
    }

    // 下一页按钮
    paginationHTML += `
        <button onclick="changeAlertPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            下一页
            <i class="fas fa-chevron-right"></i>
        </button>
    `;

    pagination.innerHTML = paginationHTML;
}

// 切换报警页面
function changeAlertPage(page) {
    const totalPages = Math.ceil(filteredAlerts.length / pageSize);
    if (page < 1 || page > totalPages) return;

    currentPage = page;
    updateAlertsDisplay();
    updateAlertPagination();
}

// 点击模态框外部关闭
document.addEventListener('click', function(e) {
    const alertModal = document.getElementById('alertDetailModal');
    const handleModal = document.getElementById('handleAlertModal');

    if (e.target === alertModal) {
        closeAlertDetail();
    }
    if (e.target === handleModal) {
        closeHandleAlert();
    }
});