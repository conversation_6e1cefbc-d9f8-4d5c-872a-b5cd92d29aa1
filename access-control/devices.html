<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - 门禁系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-cogs"></i>
                    设备管理
                </h1>
                <div class="page-actions">
                    <button class="btn btn-success" onclick="addDevice()">
                        <i class="fas fa-plus"></i>
                        添加设备
                    </button>
                    <button class="btn btn-warning" onclick="batchOperation()">
                        <i class="fas fa-tasks"></i>
                        批量操作
                    </button>
                    <button class="btn btn-primary" onclick="refreshDevices()">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                </div>
            </div>

            <!-- 设备统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-server" style="color: var(--primary-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalDevices">42</div>
                        <div class="stat-label">设备总数</div>
                        <div class="stat-change">
                            <i class="fas fa-info-circle"></i>
                            8个通道
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="onlineDevices">38</div>
                        <div class="stat-label">在线设备</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            90.5%
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-times-circle" style="color: var(--danger-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="offlineDevices">2</div>
                        <div class="stat-label">离线设备</div>
                        <div class="stat-change negative">
                            <i class="fas fa-exclamation-triangle"></i>
                            需处理
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="warningDevices">2</div>
                        <div class="stat-label">异常设备</div>
                        <div class="stat-change">
                            <i class="fas fa-tools"></i>
                            需维护
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备筛选 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">设备筛选</h3>
                    <div class="card-actions">
                        <button class="btn btn-outline" onclick="resetDeviceFilters()">
                            <i class="fas fa-undo"></i>
                            重置筛选
                        </button>
                    </div>
                </div>
                <div class="search-filters">
                    <input type="text" class="search-input" id="deviceSearchInput" placeholder="搜索设备名称、编号..." onkeyup="applyDeviceFilters()">

                    <select class="filter-select" id="deviceTypeFilter" onchange="applyDeviceFilters()">
                        <option value="">全部类型</option>
                        <option value="人脸机">人脸机</option>
                        <option value="刷卡机">刷卡机</option>
                        <option value="二维码设备">二维码设备</option>
                    </select>

                    <select class="filter-select" id="deviceStatusFilter" onchange="applyDeviceFilters()">
                        <option value="">全部状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                        <option value="warning">异常</option>
                    </select>

                    <select class="filter-select" id="deviceLocationFilter" onchange="applyDeviceFilters()">
                        <option value="">全部位置</option>
                        <option value="主入口">主入口</option>
                        <option value="侧门A">侧门A</option>
                        <option value="侧门B">侧门B</option>
                        <option value="地下车库">地下车库</option>
                        <option value="后门">后门</option>
                        <option value="紧急出口">紧急出口</option>
                    </select>
                </div>
            </div>

            <!-- 设备列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">设备列表</h3>
                    <div class="card-actions">
                        <span class="device-count">共 <span id="deviceCount">0</span> 台设备</span>
                        <button class="btn btn-outline btn-sm" onclick="toggleDeviceView()">
                            <i class="fas fa-th-large" id="deviceViewToggleIcon"></i>
                            <span id="deviceViewToggleText">卡片视图</span>
                        </button>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div class="device-cards" id="deviceCards">
                    <!-- 设备卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 表格视图 -->
                <div class="table-container" id="deviceTable" style="display: none;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>设备编号</th>
                                <th>设备名称</th>
                                <th>类型</th>
                                <th>位置</th>
                                <th>状态</th>
                                <th>IP地址</th>
                                <th>最后心跳</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="deviceTableBody">
                            <!-- 表格内容将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 设备详情模态框 -->
    <div class="modal" id="deviceDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">设备详情</h3>
                <button class="modal-close" onclick="closeDeviceDetail()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="deviceDetailContent">
                <!-- 详情内容将通过JavaScript动态生成 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="editDevice()">
                    <i class="fas fa-edit"></i>
                    编辑设备
                </button>
                <button class="btn btn-warning" onclick="restartDevice()">
                    <i class="fas fa-redo"></i>
                    重启设备
                </button>
                <button class="btn btn-outline" onclick="closeDeviceDetail()">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script>
        // 简化的设备管理JavaScript
        let allDevices = [];
        let filteredDevices = [];
        let isDeviceCardView = true;

        document.addEventListener('DOMContentLoaded', function() {
            loadSidebarComponent();
            initDevices();
        });

        function loadSidebarComponent() {
            fetch('components/sidebar.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('sidebar-container').innerHTML = html;
                })
                .catch(error => console.error('Failed to load sidebar:', error));
        }

        function initDevices() {
            generateMockDevices();
            applyDeviceFilters();
        }

        function generateMockDevices() {
            allDevices = [];
            const types = ['人脸机', '刷卡机', '二维码设备'];
            const locations = ['主入口', '侧门A', '侧门B', '地下车库', '后门', '紧急出口'];

            for (let i = 1; i <= 42; i++) {
                allDevices.push({
                    id: `DEV${i.toString().padStart(3, '0')}`,
                    name: `${types[Math.floor(Math.random() * types.length)]} ${i}`,
                    type: types[Math.floor(Math.random() * types.length)],
                    location: locations[Math.floor(Math.random() * locations.length)],
                    status: MockData.generateDeviceStatus(),
                    ip: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
                    lastHeartbeat: new Date(Date.now() - Math.random() * 60000)
                });
            }
        }

        function applyDeviceFilters() {
            const searchTerm = document.getElementById('deviceSearchInput').value.toLowerCase();
            const typeFilter = document.getElementById('deviceTypeFilter').value;
            const statusFilter = document.getElementById('deviceStatusFilter').value;
            const locationFilter = document.getElementById('deviceLocationFilter').value;

            filteredDevices = allDevices.filter(device => {
                const matchesSearch = !searchTerm ||
                    device.name.toLowerCase().includes(searchTerm) ||
                    device.id.toLowerCase().includes(searchTerm);
                const matchesType = !typeFilter || device.type === typeFilter;
                const matchesStatus = !statusFilter || device.status === statusFilter;
                const matchesLocation = !locationFilter || device.location === locationFilter;

                return matchesSearch && matchesType && matchesStatus && matchesLocation;
            });

            updateDevicesDisplay();
        }

        function updateDevicesDisplay() {
            document.getElementById('deviceCount').textContent = filteredDevices.length;

            if (isDeviceCardView) {
                updateDeviceCardView();
            } else {
                updateDeviceTableView();
            }
        }

        function updateDeviceCardView() {
            const container = document.getElementById('deviceCards');

            if (filteredDevices.length === 0) {
                container.innerHTML = '<div class="no-devices"><i class="fas fa-server"></i><p>暂无符合条件的设备</p></div>';
                return;
            }

            container.innerHTML = filteredDevices.map(device => `
                <div class="device-card" onclick="showDeviceDetail('${device.id}')">
                    <div class="device-card-header">
                        <div class="device-icon">
                            <i class="fas fa-${getDeviceIcon(device.type)}"></i>
                        </div>
                        <div class="device-info">
                            <div class="device-name">${device.name}</div>
                            <div class="device-id">${device.id}</div>
                        </div>
                        <div class="device-status">
                            <span class="status-indicator status-${device.status}">
                                <i class="fas fa-circle"></i>
                                ${getStatusText(device.status)}
                            </span>
                        </div>
                    </div>
                    <div class="device-card-body">
                        <div class="device-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            ${device.location}
                        </div>
                        <div class="device-detail">
                            <i class="fas fa-network-wired"></i>
                            ${device.ip}
                        </div>
                        <div class="device-detail">
                            <i class="fas fa-heartbeat"></i>
                            ${Utils.formatTime(device.lastHeartbeat)}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateDeviceTableView() {
            const tbody = document.getElementById('deviceTableBody');

            if (filteredDevices.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 40px;">暂无符合条件的设备</td></tr>';
                return;
            }

            tbody.innerHTML = filteredDevices.map(device => `
                <tr onclick="showDeviceDetail('${device.id}')">
                    <td>${device.id}</td>
                    <td>${device.name}</td>
                    <td>${device.type}</td>
                    <td>${device.location}</td>
                    <td>
                        <span class="status-indicator status-${device.status}">
                            <i class="fas fa-circle"></i>
                            ${getStatusText(device.status)}
                        </span>
                    </td>
                    <td>${device.ip}</td>
                    <td>${Utils.formatTime(device.lastHeartbeat)}</td>
                    <td>
                        <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); editDevice('${device.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function getDeviceIcon(type) {
            const iconMap = {
                '人脸机': 'user',
                '刷卡机': 'credit-card',
                '二维码设备': 'qrcode'
            };
            return iconMap[type] || 'server';
        }

        function getStatusText(status) {
            const statusMap = {
                online: '在线',
                offline: '离线',
                warning: '异常'
            };
            return statusMap[status] || '未知';
        }

        function toggleDeviceView() {
            isDeviceCardView = !isDeviceCardView;

            const cardContainer = document.getElementById('deviceCards');
            const tableContainer = document.getElementById('deviceTable');
            const toggleIcon = document.getElementById('deviceViewToggleIcon');
            const toggleText = document.getElementById('deviceViewToggleText');

            if (isDeviceCardView) {
                cardContainer.style.display = 'block';
                tableContainer.style.display = 'none';
                toggleIcon.className = 'fas fa-th-list';
                toggleText.textContent = '列表视图';
            } else {
                cardContainer.style.display = 'none';
                tableContainer.style.display = 'block';
                toggleIcon.className = 'fas fa-th-large';
                toggleText.textContent = '卡片视图';
            }

            updateDevicesDisplay();
        }

        function showDeviceDetail(deviceId) {
            const device = allDevices.find(d => d.id === deviceId);
            if (!device) return;

            const modal = document.getElementById('deviceDetailModal');
            const content = document.getElementById('deviceDetailContent');

            content.innerHTML = `
                <div class="device-detail-info">
                    <h4>基本信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>设备编号:</label>
                            <span>${device.id}</span>
                        </div>
                        <div class="detail-item">
                            <label>设备名称:</label>
                            <span>${device.name}</span>
                        </div>
                        <div class="detail-item">
                            <label>设备类型:</label>
                            <span>${device.type}</span>
                        </div>
                        <div class="detail-item">
                            <label>安装位置:</label>
                            <span>${device.location}</span>
                        </div>
                        <div class="detail-item">
                            <label>设备状态:</label>
                            <span class="status-indicator status-${device.status}">
                                <i class="fas fa-circle"></i>
                                ${getStatusText(device.status)}
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>IP地址:</label>
                            <span>${device.ip}</span>
                        </div>
                        <div class="detail-item">
                            <label>最后心跳:</label>
                            <span>${Utils.formatDateTime(device.lastHeartbeat)}</span>
                        </div>
                    </div>
                </div>
            `;

            modal.classList.add('show');
        }

        function closeDeviceDetail() {
            document.getElementById('deviceDetailModal').classList.remove('show');
        }

        function resetDeviceFilters() {
            document.getElementById('deviceSearchInput').value = '';
            document.getElementById('deviceTypeFilter').value = '';
            document.getElementById('deviceStatusFilter').value = '';
            document.getElementById('deviceLocationFilter').value = '';
            applyDeviceFilters();
            Utils.showNotification('筛选条件已重置', 'info');
        }

        function refreshDevices() {
            Utils.showNotification('正在刷新设备数据...', 'info', 1000);
            setTimeout(() => {
                generateMockDevices();
                applyDeviceFilters();
                Utils.showNotification('设备数据已刷新', 'success');
            }, 1000);
        }

        function addDevice() {
            Utils.showNotification('添加设备功能开发中...', 'info');
        }

        function batchOperation() {
            Utils.showNotification('批量操作功能开发中...', 'info');
        }

        function editDevice() {
            Utils.showNotification('编辑设备功能开发中...', 'info');
        }

        function restartDevice() {
            Utils.showNotification('设备重启命令已发送', 'success');
        }
    </script>
</body>
</html>