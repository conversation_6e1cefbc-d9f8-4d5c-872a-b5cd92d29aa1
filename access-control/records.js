// 通行记录页面JavaScript

let allRecords = [];
let filteredRecords = [];
let currentPage = 1;
let pageSize = 20;
let sortField = 'time';
let sortDirection = 'desc';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initRecords();
    loadSidebarComponent();
});

// 加载侧边栏组件
function loadSidebarComponent() {
    fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('sidebar-container').innerHTML = html;
            // 执行侧边栏中的脚本
            const scripts = document.getElementById('sidebar-container').querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.body.appendChild(newScript);
            });
        })
        .catch(error => {
            console.error('Failed to load sidebar:', error);
        });
}

// 初始化记录页面
function initRecords() {
    loadStatistics();
    generateMockRecords();
    applyFilters();

    // 设置默认日期为今天
    document.getElementById('dateFilter').value = new Date().toISOString().split('T')[0];
}

// 加载统计数据
function loadStatistics() {
    const stats = {
        todayRecords: Math.floor(Math.random() * 2000) + 1000,
        weekRecords: Math.floor(Math.random() * 10000) + 5000,
        totalRecords: Math.floor(Math.random() * 200000) + 100000,
        uniqueUsers: Math.floor(Math.random() * 3000) + 2000
    };

    document.getElementById('todayRecords').textContent = stats.todayRecords.toLocaleString();
    document.getElementById('weekRecords').textContent = stats.weekRecords.toLocaleString();
    document.getElementById('totalRecords').textContent = stats.totalRecords.toLocaleString();
    document.getElementById('uniqueUsers').textContent = stats.uniqueUsers.toLocaleString();
}

// 生成模拟记录数据
function generateMockRecords() {
    allRecords = [];

    // 生成500条模拟记录
    for (let i = 0; i < 500; i++) {
        const record = MockData.generateAccessRecord();
        record.id = i + 1;
        // 设置时间为最近7天内的随机时间
        record.time = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
        allRecords.push(record);
    }

    // 按时间排序
    allRecords.sort((a, b) => b.time - a.time);
}

// 处理搜索
function handleSearch() {
    // 使用防抖处理搜索
    Utils.debounce(applyFilters, 300)();
}

// 应用筛选
function applyFilters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const methodFilter = document.getElementById('methodFilter').value;
    const channelFilter = document.getElementById('channelFilter').value;
    const directionFilter = document.getElementById('directionFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;

    filteredRecords = allRecords.filter(record => {
        // 搜索条件
        const matchesSearch = !searchTerm ||
            record.name.toLowerCase().includes(searchTerm) ||
            record.cardId.toLowerCase().includes(searchTerm);

        // 类型筛选
        const matchesType = !typeFilter || record.type === typeFilter;

        // 验证方式筛选
        const matchesMethod = !methodFilter || record.method === methodFilter;

        // 通道筛选
        const matchesChannel = !channelFilter || record.channel === channelFilter;

        // 方向筛选
        const matchesDirection = !directionFilter || record.direction === directionFilter;

        // 日期筛选
        const matchesDate = !dateFilter ||
            record.time.toISOString().split('T')[0] === dateFilter;

        return matchesSearch && matchesType && matchesMethod &&
               matchesChannel && matchesDirection && matchesDate;
    });

    // 排序
    sortRecords();

    // 重置到第一页
    currentPage = 1;

    // 更新显示
    updateRecordsDisplay();
    updatePagination();
}

// 排序记录
function sortRecords() {
    filteredRecords.sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // 处理时间字段
        if (sortField === 'time') {
            aValue = aValue.getTime();
            bValue = bValue.getTime();
        }

        // 处理字符串字段
        if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }

        if (sortDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
}

// 表格排序
function sortTable(field) {
    if (sortField === field) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortField = field;
        sortDirection = 'desc';
    }

    // 更新排序图标
    updateSortIcons();

    // 重新排序和显示
    sortRecords();
    updateRecordsDisplay();
}

// 更新排序图标
function updateSortIcons() {
    // 重置所有图标
    document.querySelectorAll('.sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort sort-icon';
    });

    // 设置当前排序字段的图标
    const currentIcon = document.querySelector(`th[onclick="sortTable('${sortField}')"] .sort-icon`);
    if (currentIcon) {
        currentIcon.className = `fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'} sort-icon`;
    }
}

// 更新记录显示
function updateRecordsDisplay() {
    const tbody = document.getElementById('recordsTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageRecords = filteredRecords.slice(startIndex, endIndex);

    // 更新记录数量
    document.getElementById('recordCount').textContent = filteredRecords.length;

    if (pageRecords.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                    <i class="fas fa-search" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
                    暂无符合条件的记录
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = pageRecords.map(record => `
        <tr>
            <td>${Utils.formatDateTime(record.time)}</td>
            <td>
                <div class="person-info">
                    <span class="person-name">${record.name}</span>
                </div>
            </td>
            <td>
                <span class="badge badge-${getTypeClass(record.type)}">
                    ${record.type}
                </span>
            </td>
            <td>
                <span class="method-badge method-${getMethodClass(record.method)}">
                    <i class="fas fa-${getMethodIcon(record.method)}"></i>
                    ${record.method}
                </span>
            </td>
            <td>${record.channel}</td>
            <td>
                <span class="direction-indicator direction-${record.direction === '进入' ? 'in' : 'out'}">
                    <i class="fas fa-arrow-${record.direction === '进入' ? 'right' : 'left'}"></i>
                    ${record.direction}
                </span>
            </td>
            <td>
                <code class="card-id">${record.cardId}</code>
            </td>
            <td>
                <button class="btn btn-outline btn-sm" onclick="showRecordDetail(${record.id})">
                    <i class="fas fa-eye"></i>
                    详情
                </button>
            </td>
        </tr>
    `).join('');
}

// 获取类型样式类
function getTypeClass(type) {
    const typeMap = {
        '员工': 'primary',
        '访客': 'warning',
        '临时工': 'info'
    };
    return typeMap[type] || 'secondary';
}

// 获取验证方式样式类
function getMethodClass(method) {
    const methodMap = {
        '刷卡': 'card',
        '人脸识别': 'face',
        '二维码': 'qr'
    };
    return methodMap[method] || 'default';
}

// 获取验证方式图标
function getMethodIcon(method) {
    const iconMap = {
        '刷卡': 'credit-card',
        '人脸识别': 'user',
        '二维码': 'qrcode'
    };
    return iconMap[method] || 'key';
}

// 更新分页
function updatePagination() {
    const totalPages = Math.ceil(filteredRecords.length / pageSize);
    const pagination = document.getElementById('pagination');

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // 上一页按钮
    paginationHTML += `
        <button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
            上一页
        </button>
    `;

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        paginationHTML += `<button onclick="changePage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="pagination-ellipsis">...</span>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>
                ${i}
            </button>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="pagination-ellipsis">...</span>`;
        }
        paginationHTML += `<button onclick="changePage(${totalPages})">${totalPages}</button>`;
    }

    // 下一页按钮
    paginationHTML += `
        <button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            下一页
            <i class="fas fa-chevron-right"></i>
        </button>
    `;

    pagination.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredRecords.length / pageSize);
    if (page < 1 || page > totalPages) return;

    currentPage = page;
    updateRecordsDisplay();
    updatePagination();
}

// 改变每页显示数量
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSizeSelect').value);
    currentPage = 1;
    updateRecordsDisplay();
    updatePagination();
}

// 重置筛选
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('typeFilter').value = '';
    document.getElementById('methodFilter').value = '';
    document.getElementById('channelFilter').value = '';
    document.getElementById('directionFilter').value = '';
    document.getElementById('dateFilter').value = '';

    applyFilters();
    Utils.showNotification('筛选条件已重置', 'info');
}

// 刷新记录
function refreshRecords() {
    Utils.showNotification('正在刷新记录...', 'info', 1000);

    setTimeout(() => {
        generateMockRecords();
        applyFilters();
        Utils.showNotification('记录已刷新', 'success');
    }, 1000);
}

// 导出记录
function exportRecords() {
    if (filteredRecords.length === 0) {
        Utils.showNotification('暂无数据可导出', 'warning');
        return;
    }

    Utils.showNotification('正在导出记录...', 'info');

    // 模拟导出过程
    setTimeout(() => {
        Utils.showNotification(`已导出 ${filteredRecords.length} 条记录`, 'success');
    }, 1500);
}

// 显示记录详情
function showRecordDetail(recordId) {
    const record = allRecords.find(r => r.id === recordId);
    if (!record) return;

    const modal = document.getElementById('recordDetailModal');
    const content = document.getElementById('recordDetailContent');

    content.innerHTML = `
        <div class="record-detail">
            <div class="detail-section">
                <h4 class="detail-title">基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>记录ID:</label>
                        <span>${record.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>通行时间:</label>
                        <span>${Utils.formatDateTime(record.time)}</span>
                    </div>
                    <div class="detail-item">
                        <label>人员姓名:</label>
                        <span>${record.name}</span>
                    </div>
                    <div class="detail-item">
                        <label>人员类型:</label>
                        <span class="badge badge-${getTypeClass(record.type)}">${record.type}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4 class="detail-title">通行信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>验证方式:</label>
                        <span class="method-badge method-${getMethodClass(record.method)}">
                            <i class="fas fa-${getMethodIcon(record.method)}"></i>
                            ${record.method}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>通行通道:</label>
                        <span>${record.channel}</span>
                    </div>
                    <div class="detail-item">
                        <label>通行方向:</label>
                        <span class="direction-indicator direction-${record.direction === '进入' ? 'in' : 'out'}">
                            <i class="fas fa-arrow-${record.direction === '进入' ? 'right' : 'left'}"></i>
                            ${record.direction}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>卡号:</label>
                        <code class="card-id">${record.cardId}</code>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4 class="detail-title">系统信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>记录状态:</label>
                        <span class="status-indicator status-online">
                            <i class="fas fa-check-circle"></i>
                            正常
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>设备编号:</label>
                        <span>DEV${Math.floor(Math.random() * 42) + 1}</span>
                    </div>
                    <div class="detail-item">
                        <label>响应时间:</label>
                        <span>${Math.floor(Math.random() * 500) + 100}ms</span>
                    </div>
                    <div class="detail-item">
                        <label>IP地址:</label>
                        <span>192.168.1.${Math.floor(Math.random() * 254) + 1}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    modal.classList.add('show');
}

// 关闭记录详情
function closeRecordDetail() {
    const modal = document.getElementById('recordDetailModal');
    modal.classList.remove('show');
}

// 点击模态框外部关闭
document.addEventListener('click', function(e) {
    const modal = document.getElementById('recordDetailModal');
    if (e.target === modal) {
        closeRecordDetail();
    }
});