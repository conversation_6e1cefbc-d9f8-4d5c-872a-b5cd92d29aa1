<!-- 门禁系统侧边导航栏组件 -->
<div class="sidebar" id="sidebar">
    <!-- 导航头部 -->
    <div class="nav-header">
        <div class="nav-logo">
            <i class="fas fa-shield-alt"></i>
            门禁系统
        </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="nav-menu">
        <ul>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="monitor.html" class="nav-link" data-page="monitor">
                    <i class="fas fa-video"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="records.html" class="nav-link" data-page="records">
                    <i class="fas fa-list-alt"></i>
                    <span>通行记录</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="analytics.html" class="nav-link" data-page="analytics">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计分析</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="alerts.html" class="nav-link" data-page="alerts">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>报警管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="devices.html" class="nav-link" data-page="devices">
                    <i class="fas fa-cogs"></i>
                    <span>设备管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link" data-page="settings">
                    <i class="fas fa-sliders-h"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 用户信息 -->
    <div class="nav-footer">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="user-details">
                <div class="user-name">管理员</div>
                <div class="user-role">系统管理员</div>
            </div>
        </div>
        <div class="nav-actions">
            <button class="btn btn-outline" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                退出
            </button>
        </div>
    </div>
</div>

<!-- 移动端菜单按钮 -->
<button class="mobile-menu-btn" id="mobileMenuBtn">
    <i class="fas fa-bars"></i>
</button>

<!-- 遮罩层 -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<style>
/* 导航栏底部样式 */
.nav-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.user-avatar {
    font-size: 32px;
    color: var(--primary-color);
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: var(--text-secondary);
}

.nav-actions {
    display: flex;
    justify-content: center;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    box-shadow: var(--shadow);
}

/* 遮罩层 */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .sidebar-overlay.show {
        display: block;
    }

    .main-content {
        padding-top: 70px;
    }
}
</style>

<script>
// 导航栏交互功能
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前页面的导航项为激活状态
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'dashboard';
    const currentNavLink = document.querySelector(`[data-page="${currentPage}"]`);
    if (currentNavLink) {
        currentNavLink.classList.add('active');
    }

    // 移动端菜单切换
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('open');
            sidebarOverlay.classList.toggle('show');
        });
    }

    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('open');
            sidebarOverlay.classList.remove('show');
        });
    }
});

// 退出登录功能
function logout() {
    if (confirm('确定要退出系统吗？')) {
        // 这里可以添加实际的退出逻辑
        alert('退出成功');
        // window.location.href = 'login.html';
    }
}
</script>