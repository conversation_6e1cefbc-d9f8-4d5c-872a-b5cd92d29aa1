<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门禁系统 - 主页</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/access-control.css">
</head>
<body>
    <div class="main-layout">
        <!-- 侧边栏容器 -->
        <div id="sidebar-container"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 欢迎页面 -->
            <div class="welcome-section">
                <div class="welcome-header">
                    <h1 class="welcome-title">
                        <i class="fas fa-shield-alt"></i>
                        智能门禁管理系统
                    </h1>
                    <p class="welcome-subtitle">多通道通行方式部署，实时出入记录与高峰流量统计，异常行为识别与报警联动</p>
                </div>

                <!-- 系统特性 -->
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">多通道通行</h3>
                        <p class="feature-description">支持刷卡、人脸识别、二维码验证方式，满足员工、访客及临时工多样化通行需求</p>
                        <div class="feature-stats">
                            <span class="stat-item">8个主要通道</span>
                            <span class="stat-item">42台设备</span>
                            <span class="stat-item">稳定运行5个月</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">实时记录统计</h3>
                        <p class="feature-description">所有进出行为实时记录，自动统计每天出入人数，生成通行高峰图表和趋势分析</p>
                        <div class="feature-stats">
                            <span class="stat-item">20,000+条日记录</span>
                            <span class="stat-item">高峰期分析</span>
                            <span class="stat-item">访客管理</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 class="feature-title">异常行为识别</h3>
                        <p class="feature-description">尾随检测、非法入侵识别，系统实时触发预警，联动视频监控和安全工单</p>
                        <div class="feature-stats">
                            <span class="stat-item">尾随检测</span>
                            <span class="stat-item">入侵识别</span>
                            <span class="stat-item">报警联动</span>
                        </div>
                    </div>
                </div>

                <!-- 快速导航 -->
                <div class="quick-nav">
                    <h2 class="nav-title">快速导航</h2>
                    <div class="nav-grid">
                        <a href="dashboard.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="nav-content">
                                <h3>仪表板</h3>
                                <p>查看系统总览和关键指标</p>
                            </div>
                        </a>

                        <a href="monitor.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="nav-content">
                                <h3>实时监控</h3>
                                <p>监控各通道实时状态</p>
                            </div>
                        </a>

                        <a href="records.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <div class="nav-content">
                                <h3>通行记录</h3>
                                <p>查看详细的出入记录</p>
                            </div>
                        </a>

                        <a href="analytics.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="nav-content">
                                <h3>统计分析</h3>
                                <p>流量统计和趋势分析</p>
                            </div>
                        </a>

                        <a href="alerts.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="nav-content">
                                <h3>报警管理</h3>
                                <p>处理异常行为报警</p>
                            </div>
                        </a>

                        <a href="devices.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="nav-content">
                                <h3>设备管理</h3>
                                <p>管理门禁设备配置</p>
                            </div>
                        </a>

                        <a href="settings.html" class="nav-item">
                            <div class="nav-icon">
                                <i class="fas fa-sliders-h"></i>
                            </div>
                            <div class="nav-content">
                                <h3>系统设置</h3>
                                <p>系统参数和用户管理</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共脚本 -->
    <script src="js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadSidebarComponent();
        });

        function loadSidebarComponent() {
            fetch('components/sidebar.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('sidebar-container').innerHTML = html;
                })
                .catch(error => console.error('Failed to load sidebar:', error));
        }
    </script>

    <style>
        /* 主页专用样式 */
        .welcome-section {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .welcome-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .welcome-title {
            font-size: 48px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .welcome-title i {
            color: var(--primary-color);
        }

        .welcome-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 80px;
        }

        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 40px 30px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
        }

        .feature-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-stats {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: var(--light-bg);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .quick-nav {
            margin-top: 60px;
        }

        .nav-title {
            font-size: 32px;
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: 40px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 20px;
            background: white;
            border-radius: 8px;
            padding: 25px;
            text-decoration: none;
            box-shadow: var(--shadow);
            transition: all 0.3s;
        }

        .nav-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-icon {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: var(--light-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: var(--primary-color);
        }

        .nav-content h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .nav-content p {
            color: var(--text-secondary);
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .welcome-title {
                font-size: 32px;
                flex-direction: column;
                gap: 10px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .nav-grid {
                grid-template-columns: 1fr;
            }

            .nav-item {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</body>
</html>