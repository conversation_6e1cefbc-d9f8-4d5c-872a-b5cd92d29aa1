// 实时监控页面JavaScript

let monitoringActive = false;
let monitoringInterval = null;
let trafficLogData = [];

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initMonitor();
    loadSidebarComponent();
});

// 加载侧边栏组件
function loadSidebarComponent() {
    fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('sidebar-container').innerHTML = html;
            // 执行侧边栏中的脚本
            const scripts = document.getElementById('sidebar-container').querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.body.appendChild(newScript);
            });
        })
        .catch(error => {
            console.error('Failed to load sidebar:', error);
        });
}

// 初始化监控
function initMonitor() {
    updateMonitorStatus();
    loadChannelData();
}

// 开始监控
function startMonitoring() {
    monitoringActive = true;

    // 更新按钮状态
    document.getElementById('startBtn').style.display = 'none';
    document.getElementById('pauseBtn').style.display = 'inline-flex';

    // 更新监控状态
    updateMonitorStatus();

    // 开始实时数据更新
    monitoringInterval = setInterval(() => {
        updateRealTimeData();
        generateTrafficEvent();
    }, 2000); // 每2秒更新一次

    Utils.showNotification('监控已开始', 'success');
}

// 暂停监控
function pauseMonitoring() {
    monitoringActive = false;

    // 更新按钮状态
    document.getElementById('startBtn').style.display = 'inline-flex';
    document.getElementById('pauseBtn').style.display = 'none';

    // 停止实时数据更新
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
        monitoringInterval = null;
    }

    // 更新监控状态
    updateMonitorStatus();

    Utils.showNotification('监控已暂停', 'warning');
}

// 刷新监控
function refreshMonitor() {
    loadChannelData();
    updateRealTimeData();
    Utils.showNotification('监控数据已刷新', 'info');
}

// 更新监控状态
function updateMonitorStatus() {
    const statusElement = document.getElementById('monitorStatus');
    const lastUpdateElement = document.getElementById('lastUpdate');

    if (monitoringActive) {
        statusElement.innerHTML = `
            <i class="fas fa-circle status-online"></i>
            监控中
        `;
        lastUpdateElement.textContent = Utils.formatTime(new Date());
    } else {
        statusElement.innerHTML = `
            <i class="fas fa-circle status-offline"></i>
            已停止
        `;
    }
}

// 加载通道数据
function loadChannelData() {
    // 模拟加载各通道的基础数据
    const channels = [
        { id: 'mainEntrance', devices: 6, todayCount: Math.floor(Math.random() * 500) + 400 },
        { id: 'sideA', devices: 4, todayCount: Math.floor(Math.random() * 300) + 200 },
        { id: 'sideB', devices: 4, todayCount: Math.floor(Math.random() * 250) + 150 },
        { id: 'garage', devices: 8, todayCount: Math.floor(Math.random() * 400) + 300 }
    ];

    channels.forEach(channel => {
        const channelElement = document.querySelector(`#${channel.id}`).closest('.channel-monitor');
        const todayCountElement = channelElement.querySelector('.info-item:nth-child(2) .info-value');
        if (todayCountElement) {
            todayCountElement.textContent = `${channel.todayCount}人`;
        }
    });
}

// 更新实时数据
function updateRealTimeData() {
    if (!monitoringActive) return;

    // 更新在线设备数
    const onlineDevices = Math.floor(Math.random() * 5) + 38;
    document.getElementById('onlineDevices').textContent = `${onlineDevices}/42`;

    // 更新当前通行人数
    const currentTraffic = Math.floor(Math.random() * 20) + 5;
    document.getElementById('currentTraffic').textContent = `${currentTraffic}人`;

    // 更新最后更新时间
    document.getElementById('lastUpdate').textContent = Utils.formatTime(new Date());

    // 更新各通道的排队人数
    updateChannelQueues();

    // 更新视频覆盖层
    updateVideoOverlays();
}

// 更新通道排队人数
function updateChannelQueues() {
    const channels = ['mainEntrance', 'sideA', 'sideB', 'garage'];

    channels.forEach(channelId => {
        const channelElement = document.querySelector(`#${channelId}`).closest('.channel-monitor');
        const queueElement = channelElement.querySelector('.info-item:nth-child(3) .info-value');
        if (queueElement) {
            const queueCount = Math.floor(Math.random() * 5);
            queueElement.textContent = `${queueCount}人`;
        }
    });
}

// 更新视频覆盖层
function updateVideoOverlays() {
    const channels = [
        { id: 'mainEntrance', name: '主入口' },
        { id: 'sideA', name: '侧门A' },
        { id: 'sideB', name: '侧门B' },
        { id: 'garage', name: '地下车库' }
    ];

    channels.forEach(channel => {
        const overlay = document.getElementById(channel.id);
        if (Math.random() > 0.7) { // 30%概率显示通行事件
            const person = generateRandomPerson();
            showPassingEvent(overlay, person, channel.name);
        }
    });
}

// 生成随机人员信息
function generateRandomPerson() {
    const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八'];
    const types = ['员工', '访客', '临时工'];
    const methods = ['刷卡', '人脸识别', '二维码'];

    return {
        name: names[Math.floor(Math.random() * names.length)],
        type: types[Math.floor(Math.random() * types.length)],
        method: methods[Math.floor(Math.random() * methods.length)],
        direction: Math.random() > 0.5 ? '进入' : '离开'
    };
}

// 显示通行事件
function showPassingEvent(overlay, person, channel) {
    const eventElement = document.createElement('div');
    eventElement.className = 'passing-event';
    eventElement.innerHTML = `
        <div class="event-info">
            <div class="event-person">${person.name} (${person.type})</div>
            <div class="event-method">${person.method} - ${person.direction}</div>
        </div>
        <div class="event-time">${Utils.formatTime(new Date())}</div>
    `;

    overlay.appendChild(eventElement);

    // 3秒后移除事件显示
    setTimeout(() => {
        if (eventElement.parentNode) {
            eventElement.remove();
        }
    }, 3000);
}

// 生成通行事件记录
function generateTrafficEvent() {
    if (!monitoringActive) return;

    if (Math.random() > 0.6) { // 40%概率生成新的通行记录
        const channels = ['主入口', '侧门A', '侧门B', '地下车库'];
        const person = generateRandomPerson();
        const channel = channels[Math.floor(Math.random() * channels.length)];

        const event = {
            id: Utils.generateId(),
            time: new Date(),
            person: person.name,
            type: person.type,
            method: person.method,
            channel: channel,
            direction: person.direction,
            status: 'success'
        };

        addTrafficLogEntry(event);
    }
}

// 添加通行记录条目
function addTrafficLogEntry(event) {
    trafficLogData.unshift(event); // 添加到数组开头

    // 限制记录数量，只保留最近100条
    if (trafficLogData.length > 100) {
        trafficLogData = trafficLogData.slice(0, 100);
    }

    updateTrafficLogDisplay();
}

// 更新通行记录显示
function updateTrafficLogDisplay() {
    const logContainer = document.getElementById('trafficLog');

    if (trafficLogData.length === 0) {
        logContainer.innerHTML = `
            <div class="log-placeholder">
                <i class="fas fa-info-circle"></i>
                <p>暂无通行记录</p>
            </div>
        `;
        return;
    }

    logContainer.innerHTML = trafficLogData.map(event => `
        <div class="log-entry">
            <div class="log-time">${Utils.formatTime(event.time)}</div>
            <div class="log-content">
                <div class="log-person">
                    <span class="person-name">${event.person}</span>
                    <span class="person-type badge badge-${getTypeClass(event.type)}">${event.type}</span>
                </div>
                <div class="log-details">
                    <span class="log-method">${event.method}</span>
                    <span class="log-separator">•</span>
                    <span class="log-channel">${event.channel}</span>
                    <span class="log-separator">•</span>
                    <span class="log-direction direction-${event.direction === '进入' ? 'in' : 'out'}">
                        <i class="fas fa-arrow-${event.direction === '进入' ? 'right' : 'left'}"></i>
                        ${event.direction}
                    </span>
                </div>
            </div>
            <div class="log-status">
                <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
            </div>
        </div>
    `).join('');
}

// 获取类型样式类
function getTypeClass(type) {
    const typeMap = {
        '员工': 'primary',
        '访客': 'warning',
        '临时工': 'info'
    };
    return typeMap[type] || 'secondary';
}

// 清空通行记录
function clearTrafficLog() {
    Utils.confirm('确定要清空所有通行记录吗？', () => {
        trafficLogData = [];
        updateTrafficLogDisplay();
        Utils.showNotification('通行记录已清空', 'success');
    });
}

// 导出通行记录
function exportTrafficLog() {
    if (trafficLogData.length === 0) {
        Utils.showNotification('暂无数据可导出', 'warning');
        return;
    }

    // 模拟导出功能
    Utils.showNotification('正在导出数据...', 'info');

    setTimeout(() => {
        Utils.showNotification(`已导出 ${trafficLogData.length} 条记录`, 'success');
    }, 1500);
}