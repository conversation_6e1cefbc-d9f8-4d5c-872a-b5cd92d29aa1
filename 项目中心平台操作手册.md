# 项目中心平台操作手册

## 📖 目录

### 第一章 平台概述
- 1.1 平台介绍
- 1.2 功能架构
- 1.3 技术特点
- 1.4 使用说明

### 第二章 网关配置
- 2.1 网关管理概述
- 2.2 网关列表管理
- 2.3 网关新增操作
- 2.4 网关编辑操作
- 2.5 网关删除操作
- 2.6 网关状态监控

### 第三章 设备配置
- 3.1 设备配置概述
- 3.2 设备类型管理
- 3.3 设备分组管理
- 3.4 设备管理
- 3.5 设备监控与控制

### 第四章 空间配置
- 4.1 空间配置概述
- 4.2 空间类型管理
- 4.3 空间管理
- 4.4 空间层级结构

### 第五章 台账配置
- 5.1 台账配置概述
- 5.2 台账记录管理
- 5.3 资产类型管理
- 5.4 台账数据分析

### 第六章 报警配置
- 6.1 报警配置概述
- 6.2 通知组管理
- 6.3 报警规则管理
- 6.4 报警记录管理

### 第七章 工单配置
- 7.1 工单配置概述
- 7.2 工单管理
- 7.3 工单流程
- 7.4 工单统计

### 第八章 规则配置
- 8.1 规则配置概述
- 8.2 规则日志管理
- 8.3 消息规则管理
- 8.4 规则性能优化

### 第九章 任务配置
- 9.1 任务配置概述
- 9.2 定时任务管理
- 9.3 任务日志管理
- 9.4 任务调度优化

### 第十章 可视大屏配置
- 10.1 可视大屏概述
- 10.2 组态管理
- 10.3 大屏设计
- 10.4 数据绑定

### 第十一章 系统配置
- 11.1 系统配置概述
- 11.2 用户管理
- 11.3 部门管理
- 11.4 角色管理
- 11.5 操作日志管理

### 第十二章 个人配置
- 12.1 个人配置概述
- 12.2 个人信息管理
- 12.3 密码设置
- 12.4 个性化配置

---

## 第一章 平台概述

### 1.1 平台介绍

项目中心平台是一个集成化的物联网管理系统，提供设备管理、数据监控、报警处理、工单管理等全方位的物联网解决方案。平台采用现代化的Web技术架构，支持多项目管理，具有良好的扩展性和易用性。

#### 核心价值
- **统一管理**：集中管理多个项目的物联网设备和数据
- **实时监控**：实时监控设备状态和数据变化
- **智能报警**：智能化的报警规则和通知机制
- **流程管理**：完整的工单和任务管理流程
- **数据分析**：强大的数据分析和可视化能力

#### 应用场景
- **智慧园区**：园区设备的集中监控和管理
- **智慧楼宇**：楼宇设施的智能化管理
- **工业物联网**：工业设备的监控和维护
- **智慧城市**：城市基础设施的管理
- **环境监测**：环境数据的采集和分析

### 1.2 功能架构

#### 系统架构图
```
项目中心平台
├── 基础配置层
│   ├── 网关配置
│   ├── 设备配置
│   └── 空间配置
├── 业务管理层
│   ├── 台账配置
│   ├── 报警配置
│   └── 工单配置
├── 规则引擎层
│   ├── 规则配置
│   └── 任务配置
├── 可视化层
│   └── 可视大屏配置
└── 系统管理层
    ├── 系统配置
    └── 个人配置
```

#### 功能模块
- **网关配置**：管理物联网网关设备
- **设备配置**：管理各类物联网设备
- **空间配置**：管理空间层级和区域
- **台账配置**：管理资产台账和类型
- **报警配置**：配置报警规则和通知
- **工单配置**：管理工单流程和处理
- **规则配置**：配置业务规则和逻辑
- **任务配置**：管理定时任务和调度
- **可视大屏配置**：配置数据可视化大屏
- **系统配置**：管理用户、权限和系统设置
- **个人配置**：个人信息和偏好设置

### 1.3 技术特点

#### 前端技术
- **响应式设计**：支持PC、平板、手机等多种设备
- **现代化UI**：基于Tailwind CSS的现代化界面
- **交互友好**：直观的用户界面和操作体验
- **图标丰富**：使用Remix Icon图标库

#### 后端技术
- **微服务架构**：模块化的微服务架构
- **RESTful API**：标准的REST API接口
- **实时通信**：支持WebSocket实时数据推送
- **数据安全**：完善的数据安全和权限控制

#### 数据管理
- **多数据源**：支持多种数据源接入
- **实时处理**：实时数据处理和分析
- **历史存储**：完整的历史数据存储
- **数据可视化**：丰富的数据可视化组件

### 1.4 使用说明

#### 浏览器要求
- **推荐浏览器**：Chrome 90+、Firefox 88+、Safari 14+、Edge 90+
- **分辨率要求**：最低分辨率1366×768
- **网络要求**：稳定的网络连接
- **JavaScript**：必须启用JavaScript

#### 操作说明
- **📋 操作步骤**：详细的操作指导
- **⚠️ 注意事项**：重要提醒和注意事项
- **💡 使用技巧**：实用技巧和最佳实践
- **🔧 技术说明**：技术细节和配置说明

#### 权限说明
- **管理员**：拥有所有功能的访问和配置权限
- **操作员**：拥有日常操作和监控权限
- **查看者**：只拥有查看和浏览权限
- **自定义角色**：根据需要配置的自定义权限

#### 项目切换
平台支持多项目管理，用户可以通过顶部的项目选择器切换不同的项目：
- **项目选择**：在顶部导航栏选择当前操作的项目
- **项目隔离**：不同项目的数据完全隔离
- **权限控制**：用户只能访问有权限的项目
- **数据安全**：确保项目间数据的安全性

---

## 第二章 网关配置

### 2.1 网关管理概述

网关是物联网系统的重要组成部分，负责连接各种物联网设备并将数据传输到平台。网关管理模块提供了完整的网关设备管理功能，包括网关的注册、配置、监控和维护。

#### 网关功能
- **设备接入**：支持多种协议的设备接入
- **数据采集**：实时采集设备数据
- **协议转换**：将设备协议转换为平台标准协议
- **数据传输**：将采集的数据安全传输到平台
- **边缘计算**：支持边缘计算和数据预处理

#### 网关类型
- **有线网关**：通过以太网连接的网关
- **无线网关**：通过WiFi、4G等无线方式连接
- **边缘网关**：具备边缘计算能力的智能网关
- **协议网关**：专门用于协议转换的网关

### 2.2 网关列表管理

#### 访问路径
📋 **操作步骤**：
1. 登录系统后，点击左侧导航栏"网关配置"
2. 点击"网关管理"进入网关管理页面
3. 系统显示网关列表和相关操作

#### 页面布局
网关管理页面提供两种视图模式：
- **卡片视图**：以卡片形式展示网关信息，直观易读
- **列表视图**：以表格形式展示网关信息，信息密度高

#### 卡片视图功能
📋 **显示内容**：
- **网关图标**：网关设备的图标标识
- **网关名称**：网关的中文名称
- **网关编码**：网关的唯一标识码
- **启用状态**：启用/禁用状态标识
- **在线状态**：在线/离线状态标识
- **操作按钮**：编辑、删除等操作按钮

#### 列表视图功能
📋 **显示字段**：
- **网关编码**：网关的唯一标识
- **网关名称**：网关的中文名称
- **网关类型**：网关的类型分类
- **状态**：启用/禁用状态
- **在线状态**：在线/离线状态
- **操作**：编辑、删除等操作

#### 状态说明
- **🟢 启用**：网关已启用，可以正常工作
- **🔴 禁用**：网关已禁用，暂停工作
- **🟢 在线**：网关正常连接，数据传输正常
- **🔴 离线**：网关断开连接，无法通信

#### 筛选和搜索
- **状态筛选**：按启用状态筛选网关
- **类型筛选**：按网关类型筛选
- **在线状态筛选**：按在线状态筛选
- **关键词搜索**：按网关名称或编码搜索

### 2.3 网关新增操作

#### 新增网关入口
📋 **操作步骤**：
1. 在网关管理页面点击"新增网关"按钮
2. 系统打开新增网关的抽屉式表单
3. 填写网关的基本信息和配置参数
4. 点击"确认"按钮保存网关信息

#### 基本信息填写
📋 **必填字段**：
- **网关名称** *：网关的中文名称，要求简洁明确
- **网关编码** *：网关的唯一标识，建议使用规范编码
- **网关品牌** *：选择网关设备的品牌
- **通信协议** *：选择网关支持的通信协议
- **所属空间** *：选择网关所在的物理空间
- **状态** *：设置网关的启用状态

#### 协议类型选择
系统支持多种通信协议：
- **MQTT**：轻量级的消息传输协议
- **HTTP/HTTPS**：标准的Web协议
- **TCP/UDP**：传输层协议
- **Modbus**：工业通信协议
- **LoRaWAN**：低功耗广域网协议

#### 配置参数设置
📋 **配置项**：
- **IP地址**：网关的IP地址
- **端口号**：通信端口号
- **设备密钥**：安全认证密钥
- **心跳间隔**：心跳检测间隔时间
- **数据上报频率**：数据上报的频率设置

#### 描述信息
- **网关描述**：详细描述网关的用途和特点
- **安装位置**：网关的具体安装位置
- **维护负责人**：网关的维护负责人
- **备注信息**：其他相关说明

### 2.4 网关编辑操作

#### 编辑网关入口
📋 **操作步骤**：
1. 在网关列表中找到要编辑的网关
2. 点击网关卡片或列表行的"编辑"按钮
3. 系统打开编辑网关的抽屉式表单
4. 修改网关信息后点击"确认"保存

#### 可编辑字段
- **网关名称**：可以修改网关名称
- **网关品牌**：可以修改网关品牌
- **通信协议**：可以修改通信协议
- **所属空间**：可以调整所属空间
- **状态**：可以启用或禁用网关
- **配置参数**：可以修改各项配置参数
- **描述信息**：可以更新描述和备注

#### 不可编辑字段
- **网关编码**：网关编码创建后不可修改
- **创建时间**：系统自动维护，不可修改
- **最后通信时间**：系统自动更新

#### 编辑验证
- **名称唯一性**：确保网关名称不重复
- **编码格式**：验证编码格式的正确性
- **参数有效性**：验证配置参数的有效性
- **权限检查**：验证用户的编辑权限

### 2.5 网关删除操作

#### 删除网关入口
📋 **操作步骤**：
1. 在网关列表中找到要删除的网关
2. 点击网关的"删除"按钮
3. 系统弹出确认删除对话框
4. 确认删除后网关将被移除

#### 删除前检查
⚠️ **删除条件**：
- 网关必须处于离线状态
- 网关下不能有关联的设备
- 用户必须有删除权限
- 确认删除操作不可恢复

#### 批量删除功能
📋 **操作步骤**：
1. 在网关列表中勾选多个网关
2. 点击"批量删除"按钮
3. 系统检查所选网关的删除条件
4. 确认后执行批量删除操作

#### 删除影响
- **设备关联**：删除网关前需要先处理关联设备
- **数据清理**：网关相关的配置数据将被清理
- **历史记录**：历史通信记录将被保留
- **日志记录**：删除操作将被记录在操作日志中

### 2.6 网关状态监控

#### 状态监控功能
- **实时状态**：实时显示网关的连接状态
- **通信质量**：显示网关的通信质量指标
- **数据流量**：显示网关的数据传输量
- **错误统计**：显示网关的错误和异常次数

#### 状态指标
📋 **监控指标**：
- **连接状态**：在线/离线状态
- **信号强度**：网络信号强度
- **数据传输率**：数据传输速率
- **错误率**：通信错误率
- **最后通信时间**：最近一次通信时间

#### 状态变化通知
- **状态变化提醒**：网关状态变化时自动通知
- **离线报警**：网关离线时发送报警
- **异常检测**：检测网关异常行为
- **性能监控**：监控网关性能指标

💡 **网关管理最佳实践**
- 使用规范的网关编码命名规则
- 定期检查网关的在线状态
- 及时处理离线和异常的网关
- 建立网关维护和巡检计划
- 合理配置网关的通信参数

⚠️ **注意事项**
- 网关编码一旦创建不可修改
- 删除网关前确保没有关联设备
- 重要配置修改建议先备份
- 网关离线时及时排查原因

## 第三章 设备配置

### 3.1 设备配置概述

设备配置是物联网平台的核心功能模块，通过设备类型管理、设备分组管理和设备管理三个层次，实现对所有接入设备的统一管理和监控。设备配置模块提供了完整的设备生命周期管理功能。

#### 设备管理层次
- **设备类型管理**：定义设备的分类和属性模板
- **设备分组管理**：按业务需求对设备进行分组
- **设备管理**：管理具体的设备实例

#### 设备生命周期
1. **设备注册**：将设备信息录入系统
2. **设备配置**：配置设备参数和属性
3. **设备监控**：实时监控设备状态和数据
4. **设备维护**：设备故障处理和维护
5. **设备退役**：设备报废和数据清理

### 3.2 设备类型管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"设备配置"
2. 点击"设备类型管理"进入设备类型列表

#### 设备类型作用
- **分类管理**：对设备进行标准化分类
- **属性模板**：定义设备的标准属性
- **配置模板**：提供设备配置模板
- **数据模型**：定义设备数据模型

#### 新增设备类型
📋 **操作步骤**：
1. 点击"新增设备类型"按钮
2. 填写设备类型信息：
   - **类型编码** *：唯一标识，如"TEMP_SENSOR"
   - **类型名称** *：中文名称，如"温度传感器"
   - **设备厂商**：设备制造商
   - **通信协议**：设备使用的通信协议
   - **数据格式**：设备数据格式
   - **属性定义**：定义设备属性列表
3. 点击"确认"保存

#### 设备类型管理
- **编辑类型**：修改设备类型信息
- **删除类型**：删除未使用的设备类型
- **复制类型**：基于现有类型创建新类型
- **导入导出**：批量导入导出设备类型

### 3.3 设备分组管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"设备配置"
2. 点击"设备分组管理"进入分组管理页面

#### 分组作用
- **业务分类**：按业务功能对设备分组
- **权限管理**：基于分组的权限控制
- **批量操作**：对分组内设备进行批量操作
- **统计分析**：按分组进行数据统计

#### 新增设备分组
📋 **操作步骤**：
1. 点击"新增分组"按钮
2. 填写分组信息：
   - **分组编码** *：唯一标识
   - **分组名称** *：中文名称
   - **上级分组**：选择上级分组（可选）
   - **负责人**：分组负责人
   - **描述**：分组描述
3. 点击"确认"保存

#### 分组层级
- 支持多级分组结构
- 最多支持5级分组
- 子分组继承父分组的权限

#### 分组操作
- **编辑分组**：修改分组信息
- **删除分组**：删除空分组
- **移动设备**：在分组间移动设备
- **权限设置**：设置分组访问权限

### 3.4 设备管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"设备配置"
2. 点击"设备管理"进入设备列表页面

#### 页面布局
设备管理页面提供两种视图模式：
- **卡片视图**：以卡片形式展示设备信息
- **列表视图**：以表格形式展示设备信息

#### 设备列表信息
📋 **显示字段**：
- **设备编码**：设备的唯一标识
- **设备名称**：设备的中文名称
- **设备类型**：所属设备类型
- **所属设备组**：设备所在分组
- **连接状态**：在线/离线状态
- **操作**：查看、编辑、删除等操作

#### 新增设备
📋 **操作步骤**：
1. 点击"新增设备"按钮
2. 填写设备基本信息：
   - **设备编码** *：设备唯一标识
   - **设备名称** *：设备中文名称
   - **设备类型** *：选择设备类型
   - **所属分组** *：选择设备分组
   - **所属网关**：选择接入网关
3. 填写设备详细信息：
   - **设备型号**：设备具体型号
   - **序列号**：设备序列号
   - **安装位置**：设备安装位置
   - **负责人**：设备维护负责人
4. 配置设备参数：
   - **通信参数**：IP地址、端口等
   - **采集参数**：采集频率、数据格式等
   - **报警参数**：报警阈值、报警方式等
5. 点击"确认"保存

#### 设备状态
- **🟢 在线**：设备正常连接，数据正常
- **🔴 离线**：设备断开连接
- **🟡 异常**：设备连接异常或数据异常
- **🔵 维护**：设备处于维护状态

#### 设备操作
- **查看详情**：查看设备详细信息和实时数据
- **编辑设备**：修改设备配置信息
- **删除设备**：删除设备记录
- **批量操作**：批量编辑或删除设备

### 3.5 设备监控与控制

#### 设备详情页面
📋 **访问方式**：
1. 在设备列表中点击设备卡片
2. 或点击设备的"查看"按钮
3. 进入设备详情页面

#### 设备详情功能
设备详情页面提供以下功能标签：
- **基本信息**：设备的基本配置信息
- **实时数据**：设备的实时数据监控
- **历史数据**：设备的历史数据查询
- **报警记录**：设备相关的报警信息
- **操作日志**：设备的操作历史记录

#### 实时监控
- **状态监控**：实时显示设备连接状态
- **数据监控**：实时显示设备上报的数据
- **性能监控**：监控设备性能指标
- **报警监控**：显示设备报警信息

#### 设备控制
- **远程开关**：远程控制设备开关
- **参数设置**：远程设置设备参数
- **指令下发**：向设备下发控制指令
- **批量控制**：对多个设备进行批量控制

#### 数据分析
- **数据查询**：查询设备历史数据
- **数据导出**：导出历史数据到Excel
- **数据分析**：对历史数据进行统计分析
- **趋势分析**：分析数据变化趋势

💡 **设备管理最佳实践**
- 建立标准的设备编码规则
- 合理规划设备分组结构
- 定期检查设备状态确保正常运行
- 合理设置数据采集频率
- 建立设备维护计划定期维护设备

⚠️ **注意事项**
- 设备编码一旦创建不可修改
- 删除设备前确认没有重要数据
- 设备控制操作需要相应权限
- 重要操作建议先在测试环境验证

## 第五章 台账配置

### 5.1 台账配置概述

台账配置是资产管理的核心功能，通过台账记录管理和资产类型管理，实现对所有资产的全生命周期管理。系统支持资产的新增、编辑、查看、删除，以及导入导出、到期提醒等功能。

#### 台账管理价值
- **资产追踪**：完整记录资产从采购到报废的全过程
- **成本控制**：准确掌握资产成本和折旧情况
- **合规管理**：满足财务和审计的合规要求
- **决策支持**：为资产采购和更新提供数据支持

#### 管理功能
- **台账记录管理**：管理具体的资产台账记录
- **资产类型管理**：管理资产分类和类型定义
- **导入导出**：支持Excel格式的批量导入导出
- **到期管理**：智能的到期提醒和续期管理

### 5.2 台账记录管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"台账管理"
2. 点击"台账记录"进入台账列表页面

#### 台账列表功能
📋 **页面功能**：
- **搜索筛选**：支持按名称、编号、状态等条件搜索
- **批量操作**：支持批量导入、导出、删除操作
- **状态管理**：显示资产的当前状态
- **到期提醒**：智能显示资产到期状态

#### 台账列表信息
📋 **显示字段**：
- **序号**：记录序号
- **资产编号**：资产的唯一标识
- **资产名称**：资产的中文名称
- **资产类型**：资产所属类型
- **状态**：正常/维修/报废
- **所属部门**：资产归属部门
- **到期日期**：资产到期时间（重要！）
- **操作**：查看、编辑、删除等操作

#### 到期日期显示说明
系统会根据到期时间智能显示状态：
- **🔴 已过期/今天到期**：红色显示，需要立即处理
- **🟠 7天内到期**：橙色显示，需要尽快处理
- **🟡 30天内到期**：黄色显示，需要关注
- **⚫ 正常状态**：灰色显示，暂无需处理
- **⚪ 未设置**：浅灰色显示，需要补充信息

#### 新增台账记录
📋 **操作步骤**：
1. 点击"新增台账"按钮
2. 填写基本信息：
   - **资产编号** *：唯一标识，如"AS2024001"
   - **资产名称** *：中文名称，如"服务器-Dell R740"
   - **资产类型** *：选择资产类型
   - **状态** *：选择资产状态
   - **所属部门** *：选择归属部门
3. 填写归属信息：
   - **存放位置**：具体存放位置
   - **责任人**：资产负责人
4. 填写采购信息：
   - **采购日期**：采购时间
   - **采购价格**：采购金额
   - **供应商**：供应商名称
5. 填写到期信息：
   - **到期时间** *：资产到期日期（重要！）
   - **到期提前通知** *：提前几天通知（1-30天）
6. 填写备注信息
7. 点击"确认"保存

#### 编辑台账记录
📋 **操作步骤**：
1. 在台账列表中找到要编辑的记录
2. 点击"编辑"按钮
3. 修改相关信息（资产编号不可修改）
4. 点击"确认"保存修改

#### 查看台账详情
📋 **操作步骤**：
1. 在台账列表中点击"查看"按钮
2. 在详情抽屉中查看完整信息：
   - **基本信息**：资产编号、名称、类型、状态
   - **归属信息**：部门、位置、责任人
   - **采购信息**：采购日期、价格、供应商、到期时间
   - **时间信息**：创建时间、更新时间
   - **备注信息**：相关说明

#### 删除台账记录
📋 **操作步骤**：
1. 在台账列表中找到要删除的记录
2. 点击"删除"按钮
3. 在确认对话框中确认删除

⚠️ **注意事项**
- 资产编号一旦保存不可修改
- 到期时间和提前通知天数是必填项
- 删除操作不可恢复，建议先导出备份

#### 批量导入导出
📋 **导入步骤**：
1. 点击"导入Excel"按钮
2. 下载导入模板文件
3. 按模板格式填写数据
4. 上传填写好的数据文件
5. 系统验证数据格式和内容
6. 确认导入操作

📋 **导出步骤**：
1. 点击"导出Excel"按钮
2. 系统生成包含所有字段的Excel文件
3. 文件自动下载到本地

### 5.3 资产类型管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"台账管理"
2. 点击"资产类型管理"进入类型管理页面

#### 资产类型作用
- **分类管理**：对资产进行标准化分类
- **数据规范**：统一资产数据标准
- **报表统计**：按类型进行统计分析
- **权限控制**：基于类型的权限管理

#### 预置资产类型
系统预置了常用的资产类型：
- **服务器设备** (SERVER)：各类服务器、存储设备
- **网络设备** (NETWORK)：交换机、路由器、防火墙
- **办公设备** (OFFICE)：电脑、打印机、投影仪
- **安防设备** (SECURITY)：监控摄像头、门禁系统
- **其他设备** (OTHER)：其他类型设备

#### 新增资产类型
📋 **操作步骤**：
1. 点击"新增类型"按钮
2. 填写类型信息：
   - **类型编码** *：唯一标识，如"FURNITURE"
   - **类型名称** *：中文名称，如"办公家具"
   - **状态** *：启用/禁用
   - **描述**：类型说明
3. 点击"确认"保存

#### 编辑资产类型
- 可修改类型名称、状态、描述
- 不可修改类型编码
- 可启用/禁用类型

#### 删除资产类型
⚠️ **注意事项**
- 只能删除未被使用的类型
- 删除前需确认没有台账使用该类型
- 建议先禁用再删除

### 5.4 台账数据分析

#### 统计分析功能
- **总量统计**：按类型、部门、状态统计资产数量
- **价值统计**：统计资产的总价值和分布
- **到期统计**：统计即将到期和已过期的资产
- **增长统计**：统计资产数量的增长趋势

#### 到期管理机制
- **自动计算**：自动计算距离到期的天数
- **分级提醒**：根据紧急程度使用不同颜色提醒
- **提前通知**：按设置的提前天数进行通知
- **状态跟踪**：跟踪到期处理状态

#### 报表生成功能
- **资产清单报表**：完整的资产清单
- **到期提醒报表**：即将到期的资产列表
- **部门资产报表**：按部门分类的资产报表
- **供应商报表**：按供应商分类的资产报表
- **价值分析报表**：资产价值分析报表

💡 **台账管理最佳实践**
- 建立标准的资产编号规则
- 定期检查和更新台账信息
- 设置合理的到期提醒时间
- 建立完善的到期处理流程
- 定期进行资产盘点和核对

⚠️ **注意事项**
- 台账信息要求准确完整
- 到期时间设置要合理
- 重要资产变更需要审批
- 定期备份台账数据

## 第六章 报警配置

### 6.1 报警配置概述

报警配置是物联网平台的重要安全保障功能，通过通知组管理、报警规则管理和报警记录管理，实现对系统异常情况的及时发现、通知和处理。报警系统能够帮助用户快速响应各种异常情况，确保系统的稳定运行。

#### 报警系统价值
- **及时发现**：快速发现系统和设备异常
- **自动通知**：自动向相关人员发送报警通知
- **分级处理**：根据报警级别进行分级处理
- **历史追溯**：完整记录报警处理历史

#### 报警管理功能
- **通知组管理**：管理报警通知的人员组织
- **报警规则管理**：配置各种报警触发规则
- **报警记录管理**：管理和处理报警记录
- **报警统计分析**：分析报警趋势和处理效率

### 6.2 通知组管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"报警配置"
2. 点击"通知组管理"进入通知组列表

#### 通知组作用
- **人员组织**：将相关人员组织成通知组
- **分级通知**：根据报警级别选择不同通知组
- **权限管理**：基于通知组的权限控制
- **通知方式**：配置多种通知方式

#### 通知组列表
📋 **显示信息**：
- **组名称**：通知组的名称
- **组描述**：通知组的描述说明
- **成员数量**：通知组的成员数量
- **通知方式**：配置的通知方式
- **状态**：启用/禁用状态
- **操作**：编辑、删除、查看成员等

#### 新增通知组
📋 **操作步骤**：
1. 点击"新增通知组"按钮
2. 填写基本信息：
   - **组名称** *：通知组名称
   - **组描述**：通知组描述
   - **状态** *：启用/禁用
3. 添加组成员：
   - **选择成员**：从用户列表中选择成员
   - **设置角色**：设置成员在组中的角色
   - **通知方式**：配置成员的通知方式
4. 配置通知设置：
   - **邮件通知**：是否启用邮件通知
   - **短信通知**：是否启用短信通知
   - **系统通知**：是否启用系统内通知
5. 点击"确认"保存

#### 编辑通知组
- **修改基本信息**：组名称、描述、状态
- **管理成员**：添加、删除、修改成员
- **调整通知方式**：修改通知方式配置
- **测试通知**：测试通知组的通知功能

### 6.3 报警规则管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"报警配置"
2. 点击"报警规则管理"进入规则列表

#### 报警规则类型
- **设备报警**：基于设备状态和数据的报警
- **系统报警**：基于系统运行状态的报警
- **业务报警**：基于业务逻辑的报警
- **自定义报警**：用户自定义的报警规则

#### 报警规则列表
📋 **显示信息**：
- **规则名称**：报警规则的名称
- **规则类型**：报警规则的类型
- **触发条件**：报警的触发条件
- **报警级别**：严重/重要/一般/提示
- **通知组**：关联的通知组
- **状态**：启用/禁用状态
- **操作**：编辑、删除、测试等

#### 新增报警规则
📋 **操作步骤**：
1. 点击"新增报警规则"按钮
2. 填写基本信息：
   - **规则名称** *：报警规则名称
   - **规则类型** *：选择规则类型
   - **报警级别** *：选择报警级别
   - **规则描述**：规则描述说明
3. 配置触发条件：
   - **数据源**：选择监控的数据源
   - **触发条件**：设置触发报警的条件
   - **阈值设置**：设置报警阈值
   - **持续时间**：设置条件持续时间
4. 配置通知设置：
   - **通知组** *：选择接收通知的组
   - **通知方式**：选择通知方式
   - **通知频率**：设置通知频率
   - **静默时间**：设置报警静默时间
5. 点击"确认"保存

#### 报警级别说明
- **🔴 严重**：系统严重故障，需要立即处理
- **🟠 重要**：重要功能异常，需要尽快处理
- **🟡 一般**：一般性问题，需要关注处理
- **🔵 提示**：信息性提醒，供参考

#### 编辑报警规则
- **修改基本信息**：名称、类型、级别、描述
- **调整触发条件**：修改触发条件和阈值
- **更新通知设置**：修改通知组和通知方式
- **测试规则**：测试报警规则的触发和通知

### 6.4 报警记录管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"报警配置"
2. 点击"报警记录管理"进入报警记录列表

#### 报警记录列表
📋 **显示信息**：
- **报警时间**：报警产生的时间
- **报警级别**：报警的级别标识
- **报警规则**：触发的报警规则
- **报警内容**：报警的具体内容
- **处理状态**：未处理/处理中/已处理
- **处理人**：报警的处理人员
- **处理时间**：报警的处理时间
- **操作**：查看、处理、关闭等

#### 报警状态说明
- **🔴 未处理**：新产生的报警，等待处理
- **🟡 处理中**：正在处理的报警
- **🟢 已处理**：已经处理完成的报警
- **⚫ 已关闭**：已经关闭的报警

#### 报警处理操作
📋 **处理步骤**：
1. 选择要处理的报警记录
2. 点击"处理"按钮
3. 填写处理信息：
   - **处理方式**：现场处理/远程处理/转派他人
   - **处理结果**：已解决/临时处理/需要跟进
   - **处理说明**：详细的处理过程描述
   - **附件上传**：上传相关图片或文档
4. 点击"确认处理"完成操作

#### 批量处理功能
📋 **操作步骤**：
1. 勾选多个报警记录
2. 点击"批量处理"按钮
3. 选择批量处理方式：
   - **批量确认**：确认多个报警
   - **批量转派**：转派给其他处理人
   - **批量关闭**：关闭多个已解决的报警
4. 填写批量处理说明
5. 确认批量操作

#### 报警统计分析
- **报警趋势**：显示报警数量的时间变化趋势
- **级别分布**：显示不同级别报警的分布情况
- **处理效率**：分析报警处理的平均时间
- **规则分析**：分析哪些规则触发报警最频繁

#### 报警查询筛选
- **时间筛选**：按时间范围筛选报警
- **级别筛选**：按报警级别筛选
- **状态筛选**：按处理状态筛选
- **规则筛选**：按报警规则筛选
- **关键词搜索**：按报警内容搜索

💡 **报警管理最佳实践**
- 合理设置报警阈值避免误报
- 建立分级报警和处理机制
- 定期检查和优化报警规则
- 及时处理报警确保系统安全
- 分析报警趋势优化系统配置

⚠️ **注意事项**
- 报警规则设置要合理，避免频繁误报
- 重要报警要及时处理
- 定期清理已处理的历史报警
- 确保通知组成员信息准确

## 第十一章 系统配置

### 11.1 系统配置概述

系统配置是平台管理的核心模块，包括用户管理、部门管理、角色管理和操作日志管理。通过系统配置，管理员可以控制用户权限、组织架构、系统安全和操作审计。

#### 管理功能
- **用户管理**：管理系统用户账户和基本信息
- **部门管理**：管理组织架构和部门层级
- **角色管理**：管理用户角色和权限分配
- **操作日志**：记录和查询系统操作日志

#### 权限体系
系统采用基于角色的权限控制（RBAC）：
- **用户**：系统的使用者
- **角色**：权限的集合
- **权限**：具体的操作权限
- **部门**：组织架构单元

### 11.2 用户管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"用户管理"进入用户列表页面

#### 用户列表信息
📋 **显示字段**：
- **用户编号**：用户的唯一标识符
- **用户名**：登录系统使用的用户名
- **姓名**：用户的真实姓名
- **邮箱**：用户的邮箱地址
- **手机号**：用户的手机号码
- **所属部门**：用户归属的部门
- **角色**：用户拥有的角色
- **状态**：启用/禁用/锁定
- **最后登录时间**：最近一次登录时间
- **操作**：编辑、删除、重置密码等

#### 用户状态说明
- **🟢 启用**：用户可以正常登录和使用系统
- **🔴 禁用**：用户被禁用，无法登录系统
- **🟡 锁定**：用户因安全原因被锁定
- **🔵 待激活**：新创建的用户等待激活

#### 新增用户操作
📋 **操作步骤**：
1. 点击"新增用户"按钮
2. 填写用户基本信息：
   - **用户编号** *：唯一标识
   - **用户名** *：登录用户名，3-20位字符
   - **姓名** *：用户真实姓名
   - **密码** *：初始登录密码
   - **确认密码** *：再次输入密码确认
   - **所属部门** *：选择用户归属部门
   - **角色** *：选择用户角色
3. 填写详细信息：
   - **邮箱**：用户邮箱地址
   - **手机号**：用户手机号码
   - **工号**：用户工号
   - **职位**：用户职位
4. 设置账户配置：
   - **账户状态**：启用/禁用
   - **首次登录强制修改密码**：是/否
   - **账户有效期**：设置账户有效期
5. 点击"确认"保存

#### 编辑用户信息
- **基本信息**：姓名、邮箱、手机号等
- **部门角色**：所属部门、用户角色
- **账户状态**：启用、禁用、锁定状态
- **权限设置**：特殊权限的分配

#### 密码管理
📋 **重置密码步骤**：
1. 在用户列表中找到目标用户
2. 点击"重置密码"按钮
3. 选择重置方式：
   - **系统生成密码**：系统自动生成安全密码
   - **管理员设置密码**：管理员手动设置密码
4. 设置密码重置选项
5. 确认重置操作

### 11.3 部门管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"部门管理"进入部门管理页面

#### 部门层级结构
系统支持多级部门结构：
```
总公司
├── 技术部
│   ├── 开发组
│   ├── 测试组
│   └── 运维组
├── 市场部
│   ├── 销售组
│   └── 推广组
└── 财务部
    ├── 会计组
    └── 出纳组
```

#### 新增部门操作
📋 **操作步骤**：
1. 点击"新增部门"按钮
2. 填写部门信息：
   - **部门编码** *：唯一标识
   - **部门名称** *：中文名称
   - **上级部门**：选择上级部门（可选）
   - **部门负责人**：选择负责人
   - **联系电话**：部门电话
   - **部门描述**：部门说明
3. 点击"确认"保存

#### 部门操作
- **编辑部门**：修改部门信息
- **删除部门**：删除空部门
- **移动部门**：调整部门层级关系
- **设置负责人**：指定部门负责人

### 11.4 角色管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"角色管理"进入角色管理页面

#### 预置角色
系统预置了常用角色：
- **系统管理员**：拥有所有权限
- **项目管理员**：拥有项目管理权限
- **设备管理员**：设备相关权限
- **普通用户**：基本查看权限

#### 新增角色操作
📋 **操作步骤**：
1. 点击"新增角色"按钮
2. 填写角色信息：
   - **角色编码** *：唯一标识
   - **角色名称** *：中文名称
   - **角色描述**：角色说明
3. 配置权限：
   - **功能权限**：选择可访问的功能模块
   - **数据权限**：选择可访问的数据范围
   - **操作权限**：选择可执行的操作类型
4. 点击"确认"保存

#### 权限分类
- **查看权限**：查看数据的权限
- **新增权限**：添加数据的权限
- **编辑权限**：修改数据的权限
- **删除权限**：删除数据的权限
- **导入权限**：批量导入数据的权限
- **导出权限**：导出数据的权限

### 11.5 操作日志管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"操作日志"进入日志查询页面

#### 日志记录内容
系统自动记录以下操作：
- **新增操作**：创建新记录的操作
- **修改操作**：修改现有记录的操作
- **删除操作**：删除记录的操作
- **导入操作**：批量导入数据的操作
- **导出操作**：导出数据的操作

#### 日志信息字段
📋 **显示内容**：
- **操作人**：执行操作的用户
- **操作类型**：操作的类型分类
- **操作对象**：操作的目标对象
- **操作时间**：操作执行的时间
- **操作详情**：操作的详细描述

#### 日志查询
📋 **操作步骤**：
1. 设置查询条件：
   - **操作人**：选择操作用户
   - **操作类型**：选择操作类型
   - **时间范围**：设置开始和结束时间
2. 点击"查询"按钮
3. 查看查询结果

#### 日志分析
- **操作统计**：统计各类操作的数量
- **用户行为**：分析用户的操作行为
- **异常检测**：检测异常的操作行为
- **审计报告**：生成操作审计报告

💡 **系统配置最佳实践**
- 建立清晰的组织架构和权限体系
- 定期审查和清理无效用户账户
- 实施最小权限原则，按需分配权限
- 建立完善的密码策略和安全机制
- 定期备份用户和权限配置数据

⚠️ **安全注意事项**
- 严格控制管理员权限的分配
- 定期检查和清理过期账户
- 监控异常的权限使用行为
- 建立权限变更的审计日志
- 实施双因素认证增强安全性

## 第十二章 个人配置

### 12.1 个人配置概述

个人配置模块允许用户管理自己的个人信息、密码设置和个性化配置。通过个人配置，用户可以自定义系统使用体验，确保账户安全，并管理个人偏好设置。

#### 个人配置功能
- **个人信息管理**：管理个人基本信息
- **密码设置**：修改登录密码和安全设置
- **个性化配置**：自定义界面和功能偏好
- **通知设置**：配置各类通知偏好

### 12.2 个人信息管理

#### 访问路径
📋 **操作步骤**：
1. 点击右上角用户头像
2. 选择"个人配置"进入个人设置页面
3. 或点击左侧导航栏"个人配置"

#### 个人信息编辑
📋 **可编辑信息**：
- **姓名**：用户的真实姓名
- **邮箱**：用户的邮箱地址
- **手机号**：用户的手机号码
- **头像**：上传个人头像图片
- **个人简介**：个人简介和说明
- **联系地址**：联系地址信息

#### 信息修改步骤
📋 **操作步骤**：
1. 在个人信息页面点击"编辑"按钮
2. 修改需要更新的信息
3. 上传新头像（可选）
4. 点击"保存"确认修改
5. 系统验证信息格式
6. 保存成功后显示确认消息

#### 头像上传
- **支持格式**：JPG、PNG、GIF
- **文件大小**：不超过2MB
- **图片尺寸**：建议200×200像素
- **自动裁剪**：系统自动裁剪为正方形

### 12.3 密码设置

#### 密码修改功能
📋 **修改步骤**：
1. 在个人配置页面点击"密码设置"标签
2. 输入当前密码进行身份验证
3. 输入新密码（需符合安全策略）
4. 再次输入新密码进行确认
5. 点击"确认修改"按钮
6. 系统验证密码强度和规则
7. 修改成功后要求重新登录

#### 密码安全要求
📋 **密码规则**：
- **长度要求**：8-20位字符
- **复杂度要求**：包含大小写字母、数字、特殊字符
- **历史密码**：不能与最近3次密码相同
- **常见密码**：不能使用常见弱密码

#### 密码强度检测
系统实时检测密码强度：
- **弱密码**：红色显示，不允许使用
- **中等密码**：黄色显示，建议加强
- **强密码**：绿色显示，推荐使用

#### 安全设置
- **登录通知**：启用异地登录通知
- **密码有效期**：查看密码有效期
- **登录历史**：查看最近登录记录
- **设备管理**：管理登录设备

### 12.4 个性化配置

#### 界面设置
📋 **界面配置**：
- **主题设置**：选择界面主题（浅色/深色）
- **语言设置**：选择界面语言
- **时区设置**：设置时区和时间格式
- **首页配置**：自定义首页显示内容

#### 功能偏好
📋 **偏好设置**：
- **默认页面**：设置登录后的默认页面
- **列表显示**：设置列表每页显示数量
- **数据刷新**：设置数据自动刷新频率
- **操作确认**：设置是否显示操作确认对话框

#### 通知偏好
📋 **通知设置**：
- **邮件通知**：配置邮件通知偏好
  - 系统通知：是/否
  - 报警通知：是/否
  - 工单通知：是/否
- **短信通知**：配置短信通知偏好
- **系统通知**：配置系统内通知偏好
- **通知时间**：设置接收通知的时间段

#### 快捷设置
📋 **快捷配置**：
- **常用功能**：设置常用功能快捷入口
- **收藏页面**：收藏常用页面
- **快捷键**：自定义快捷键设置
- **工作台**：自定义个人工作台

#### 数据导出偏好
📋 **导出设置**：
- **默认格式**：设置默认导出格式
- **字段选择**：设置默认导出字段
- **文件命名**：设置导出文件命名规则
- **导出历史**：查看导出历史记录

#### 配置保存和恢复
- **保存配置**：将当前配置保存为模板
- **恢复默认**：恢复系统默认配置
- **导入配置**：导入之前保存的配置
- **配置同步**：在多设备间同步配置

💡 **个人配置使用技巧**
- 定期更新个人信息确保准确性
- 设置强密码并定期更换
- 合理配置通知偏好避免信息过载
- 自定义工作台提高工作效率
- 定期检查登录历史确保账户安全

⚠️ **安全提醒**
- 不要在公共场所修改密码
- 及时更新联系方式确保能收到通知
- 发现异常登录立即修改密码
- 不要与他人分享账户信息

---

## 📝 手册总结

### 平台功能概览

项目中心平台是一个功能完整的物联网管理系统，本手册详细介绍了平台的各项功能和操作方法。

#### 核心功能模块
- **网关配置**：管理物联网网关设备，支持多种协议接入
- **设备配置**：三层设备管理架构，支持设备全生命周期管理
- **空间配置**：管理空间层级和区域划分
- **台账配置**：完整的资产管理和到期提醒机制
- **报警配置**：智能报警系统，支持分级通知和处理
- **工单配置**：完整的工单流程管理
- **规则配置**：灵活的业务规则引擎
- **任务配置**：定时任务调度和管理
- **可视大屏配置**：数据可视化和大屏展示
- **系统配置**：用户、权限和组织管理
- **个人配置**：个性化设置和偏好管理

#### 平台特色
- **多项目支持**：支持多项目管理和数据隔离
- **权限控制**：基于RBAC的精细化权限控制
- **实时监控**：实时数据监控和状态展示
- **智能报警**：多级报警和自动通知机制
- **数据分析**：丰富的数据统计和分析功能
- **移动适配**：响应式设计，支持多种设备

#### 使用建议
- **新用户**：建议从平台概述开始，了解整体架构
- **管理员**：重点关注系统配置和权限管理
- **操作员**：重点关注设备管理和台账管理
- **维护人员**：重点关注报警配置和工单管理

#### 技术支持
如果在使用过程中遇到问题，请：
1. 首先查阅本手册的相关章节
2. 联系系统管理员或技术支持团队
3. 提供详细的问题描述和错误信息

### 版本信息
- **手册版本**：v1.0
- **平台版本**：项目中心平台 v1.0
- **更新日期**：2024年12月
- **文档状态**：正式版

---

*感谢您使用项目中心平台！希望本手册能够帮助您更好地使用平台功能。*
*如有任何建议或意见，请联系我们的技术支持团队。*

---
