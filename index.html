<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧物联网平台 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 添加图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- 引入公共样式 -->
    <link href="./styles/common.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏容器 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 台账管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-3-line mr-2"></i>
                                <span>台账管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ledger.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 台账记录
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="asset-type.html" class="flex items-center w-full">
                                    <i class="ri-price-tag-3-line mr-2"></i> 资产类型管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 视频监控 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-camera-line mr-2"></i>
                                <span>视频监控</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="video-monitor/index.html" class="flex items-center w-full">
                                    <i class="ri-live-line mr-2"></i> 实时监控
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="video-monitor/playback.html" class="flex items-center w-full">
                                    <i class="ri-play-circle-line mr-2"></i> 录像回放
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="video-monitor/ai-analysis.html" class="flex items-center w-full">
                                    <i class="ri-brain-line mr-2"></i> 智能分析
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="video-monitor/alerts.html" class="flex items-center w-full">
                                    <i class="ri-alarm-warning-line mr-2"></i> 报警联动
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="video-monitor/storage.html" class="flex items-center w-full">
                                    <i class="ri-database-2-line mr-2"></i> 存储管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="operation-log.html" class="flex items-center w-full">
                                    <i class="ri-file-text-line mr-2"></i> 操作日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 个人配置 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600 space-x-4">
                            <i class="ri-home-line mr-2"></i>
                            <span>首页</span>
                            <select class="pl-3 pr-8 py-1 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none text-gray-700 text-sm">
                                <option value="aaa">AAA项目</option>
                                <option value="bbb">BBB项目</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <i class="ri-search-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 欢迎卡片 -->
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-8 rounded-xl shadow-lg text-white overflow-hidden relative">
                    <div class="absolute right-0 top-0 w-64 h-64 bg-white/10 rounded-full -mr-32 -mt-32"></div>
                    <div class="relative z-10 flex items-center">
                        <img src="https://via.placeholder.com/80" class="w-20 h-20 rounded-full border-4 border-white/30 mr-8" alt="用户头像">
                        <div class="flex-1">
                            <h3 class="text-3xl font-bold mb-2">您好，管理员！</h3>
                            <p class="text-blue-100 text-lg">今天是晴朗的一天，祝您工作愉快！</p>
                        </div>
                        <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-blue-50 transition-colors shadow-lg">
                            进入大屏
                        </button>
                    </div>
                </div>

                <!-- 数据概览 -->
                <div class="grid grid-cols-4 gap-6 mb-6">
                    <!-- 添加更多数据卡片... -->
                </div>

                <!-- 版本信息卡片 -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-xl font-bold text-blue-600">项目版本名称</h3>
                        <span class="px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">已授权</span>
                    </div>
                    <div class="grid grid-cols-3 gap-6 p-6">
                        <!-- 拥有功能 -->
                        <div class="bg-gray-50 p-6 rounded-xl hover:shadow-md transition-shadow">
                            <h4 class="text-lg font-bold flex items-center mb-4">
                                <i class="ri-function-line mr-2 text-blue-500"></i>
                                拥有功能
                            </h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-center">
                                    <i class="ri-checkbox-circle-fill text-green-500 mr-2"></i>
                                    消息中心
                                </li>
                                <!-- 添加更多功能项... -->
                            </ul>
                        </div>

                        <!-- 拥有资源 -->
                        <div class="bg-gray-50 p-6 rounded-xl hover:shadow-md transition-shadow">
                            <h4 class="text-lg font-bold flex items-center mb-4">
                                <i class="ri-database-2-line mr-2 text-blue-500"></i>
                                拥有资源
                            </h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-center">
                                    <i class="ri-radar-line mr-2 text-blue-500"></i>
                                    点位数: 20000/20000
                                </li>
                                <!-- 其他资源项... -->
                            </ul>
                        </div>

                        <!-- 拥有大屏 -->
                        <div class="bg-gray-50 p-6 rounded-xl hover:shadow-md transition-shadow">
                            <h4 class="text-lg font-bold flex items-center mb-4">
                                <i class="ri-computer-line mr-2 text-blue-500"></i>
                                拥有大屏
                            </h4>
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-center">
                                    <i class="ri-dashboard-3-line mr-2 text-blue-500"></i>
                                    企业家大厦大屏
                                </li>
                                <!-- 其他大屏项... -->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共 JS -->
    <script src="./js/common.js"></script>
    <script>
        // 初始化导航菜单
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
        });
    </script>
</body>
</html>