<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧物联网平台 - 个人配置</title>
    <!-- Tailwind CSS 国内CDN -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Remixicon 国内CDN -->
    <link href="https://cdn.staticfile.org/remixicon/3.5.0/remixicon.min.css" rel="stylesheet">
    <style>
        body { 
            background: #f4f6fa;
            min-height: 100vh;
        }
        .sidebar {
            background: #232946;
            color: #fff;
            min-height: 100vh;
            position: fixed;
            width: 16rem;
        }
        .nav-item .nav-submenu {
            display: none;
            opacity: 0;
            transition: all 0.3s;
        }
        .nav-item.active .nav-submenu {
            display: block;
            opacity: 1;
        }
        .nav-item .nav-arrow {
            transition: transform 0.3s;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .nav-submenu {
            padding-left: 2rem;
            margin-top: 0.25rem;
        }
        .nav-submenu li {
            padding: 0.375rem 0;
            opacity: 0.7;
            transition: all 0.2s;
        }
        .nav-submenu li:hover {
            opacity: 1;
        }
        .sidebar .active {
            background: #eebbc3;
            color: #232946;
        }
        .sidebar a, .sidebar .menu-item { 
            transition: all .2s;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .sidebar a:hover, .sidebar .menu-item:hover { 
            background: rgba(238, 187, 195, 0.1);
            color: #eebbc3; 
        }
        .sidebar a.active:hover {
            background: #eebbc3;
            color: #232946;
        }
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
        }
        .card {
            background: #fff;
            border-radius: 1rem;
            box-shadow: 0 4px 24px 0 rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 6px 30px 0 rgba(0,0,0,0.08);
        }
        .card-header {
            border-bottom: 1px solid #f0f0f0;
            padding: 1.25rem 1.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            color: #1f2937;
        }
        .card-header i {
            color: #3b82f6;
            font-size: 1.25rem;
            margin-right: 0.75rem;
            background: rgba(59, 130, 246, 0.1);
            padding: 0.5rem;
            border-radius: 0.5rem;
        }
        .card-body {
            padding: 1.5rem;
        }
        .form-group {
            display: flex;
            align-items: center;
            margin-bottom: 1.25rem;
        }
        .form-label {
            min-width: 6rem;
            color: #4b5563;
            font-weight: 500;
            padding-right: 1rem;
        }
        .form-input, .form-select {
            flex: 1;
            max-width: 24rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 0.625rem 1rem;
            background: #f9fafb;
            transition: all 0.2s;
            color: #1f2937;
        }
        .form-input:focus, .form-select:focus {
            border-color: #3b82f6;
            outline: none;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .btn-main {
            background: #3b82f6;
            color: #fff;
            border-radius: 0.5rem;
            padding: 0.625rem 1.5rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        .btn-main:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .btn-main:active {
            transform: translateY(0);
        }
        .avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #eebbc3;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .avatar-large {
            width: 80px;
            height: 80px;
        }
        .logo-preview {
            width: 120px;
            height: 60px;
            object-fit: contain;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            background: #fff;
            padding: 0.5rem;
            margin: 0 1rem;
        }
        .upload-btn {
            display: inline-flex;
            align-items: center;
            background: #eebbc3;
            color: #232946;
            border-radius: 0.5rem;
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        .upload-btn:hover {
            background: #ffd6e0;
            transform: translateY(-1px);
        }
        .upload-btn:active {
            transform: translateY(0);
        }
        .upload-btn i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }
        .top-bar {
            background: #fff;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .user-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
        }
        .user-role {
            font-size: 0.875rem;
            color: #6b7280;
        }
        /* 添加标签页样式 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 2rem;
            background: #fff;
            padding: 0 1rem;
            border-radius: 1rem 1rem 0 0;
        }
        .tab-item {
            padding: 1rem 2rem;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
        }
        .tab-item:hover {
            color: #3b82f6;
        }
        .tab-item.active {
            color: #3b82f6;
        }
        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #3b82f6;
            border-radius: 2px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <aside class="sidebar">
        <div class="p-4">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li>
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li>
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-layout-line mr-2"></i> 空间类型管理
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-building-2-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li>
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li>
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li>
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li>
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                            <li>
                                <a href="system-log.html" class="flex items-center w-full">
                                    <i class="ri-file-search-line mr-2"></i> 系统日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 个人配置 -->
                    <li class="py-2 px-3 bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </aside>

    <!-- 主体内容 -->
    <main class="main-content">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <div class="flex justify-between items-center">
                <div class="user-info">
                    <img src="https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=96&q=80" class="avatar" alt="用户头像">
                    <div>
                        <div class="user-name">张三</div>
                        <div class="user-role">管理员</div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 hover:bg-gray-100 rounded-full transition-colors">
                        <i class="ri-notification-3-line text-xl text-gray-500"></i>
                    </button>
                    <button class="p-2 hover:bg-gray-100 rounded-full transition-colors">
                        <i class="ri-settings-3-line text-xl text-gray-500"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 内容区 -->
        <div class="max-w-4xl mx-auto py-8 px-6">
            <!-- 标签页导航 -->
            <div class="tabs">
                <div class="tab-item active" data-tab="profile">
                    <i class="ri-user-line mr-2"></i>个人信息
                </div>
                <div class="tab-item" data-tab="password">
                    <i class="ri-lock-password-line mr-2"></i>修改密码
                </div>
                <div class="tab-item" data-tab="logo">
                    <i class="ri-image-edit-line mr-2"></i>Logo设置
                </div>
            </div>

            <!-- 个人信息标签页 -->
            <div class="tab-content active" id="profile">
                <div class="card">
                    <div class="card-body">
                        <div class="mb-6 flex items-center">
                            <img src="https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=96&q=80" class="avatar avatar-large" alt="用户头像">
                            <div class="ml-4">
                                <div class="text-lg font-semibold text-gray-800">张三</div>
                                <div class="text-sm text-gray-500">管理员</div>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" value="张三" placeholder="请输入用户名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-input" value="<EMAIL>" placeholder="请输入邮箱">
                            </div>
                            <div class="form-group">
                                <label class="form-label">手机号</label>
                                <input type="text" class="form-input" value="13800000000" placeholder="请输入手机号">
                            </div>
                            <div class="flex justify-end">
                                <button type="button" class="btn-main">
                                    <i class="ri-save-line mr-2"></i>保存信息
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 修改密码标签页 -->
            <div class="tab-content" id="password">
                <div class="card">
                    <div class="card-body">
                        <div class="space-y-4">
                            <div class="form-group">
                                <label class="form-label">原密码</label>
                                <input type="password" class="form-input" placeholder="请输入原密码">
                            </div>
                            <div class="form-group">
                                <label class="form-label">新密码</label>
                                <input type="password" class="form-input" placeholder="请输入新密码">
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认密码</label>
                                <input type="password" class="form-input" placeholder="请再次输入新密码">
                            </div>
                            <div class="flex justify-end">
                                <button type="button" class="btn-main">
                                    <i class="ri-key-line mr-2"></i>修改密码
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logo设置标签页 -->
            <div class="tab-content" id="logo">
                <div class="card">
                    <div class="card-body">
                        <div class="space-y-6">
                            <div class="form-group items-start">
                                <label class="form-label mt-2">大屏Logo</label>
                                <div class="flex items-center flex-1">
                                    <img id="mainLogoPreview" class="logo-preview" src="https://images.pexels.com/photos/1103970/pexels-photo-1103970.jpeg?auto=compress&w=160&q=80" alt="主界面Logo">
                                    <label class="upload-btn">
                                        <input type="file" id="mainLogo" accept="image/*" onchange="previewLogo(event, 'mainLogoPreview')" hidden>
                                        <i class="ri-upload-2-line"></i>
                                        <span>上传Logo</span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group items-start">
                                <label class="form-label mt-2">后台Logo</label>
                                <div class="flex items-center flex-1">
                                    <img id="backendLogoPreview" class="logo-preview" src="https://images.pexels.com/photos/325185/pexels-photo-325185.jpeg?auto=compress&w=160&q=80" alt="后台Logo">
                                    <label class="upload-btn">
                                        <input type="file" id="backendLogo" accept="image/*" onchange="previewLogo(event, 'backendLogoPreview')" hidden>
                                        <i class="ri-upload-2-line"></i>
                                        <span>上传Logo</span>
                                    </label>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button type="button" class="btn-main">
                                    <i class="ri-save-line mr-2"></i>保存Logo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 添加导航菜单交互
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏菜单交互
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                const toggle = item.querySelector('.py-2');
                if (toggle) {
                    toggle.addEventListener('click', () => {
                        item.classList.toggle('active');
                    });
                }
            });

            // 标签页切换
            const tabs = document.querySelectorAll('.tab-item');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // 添加当前活动状态
                    tab.classList.add('active');
                    const content = document.getElementById(tab.dataset.tab);
                    if (content) {
                        content.classList.add('active');
                    }
                });
            });
        });

        function previewLogo(event, previewId) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
</body>
</html> 