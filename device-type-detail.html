<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备类型详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
+                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
+                        <a href="personal.html" class="flex items-center w-full">
+                            <i class="ri-user-settings-line mr-2"></i> 个人配置
+                        </a>
+                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">设备配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">设备类型管理</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">设备类型详情</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备类型信息 -->
            <div class="p-6">
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div class="flex items-start space-x-6">
                        <div class="w-24 h-24 bg-blue-50 rounded-lg flex items-center justify-center">
                            <i class="ri-sensor-line text-5xl text-blue-500"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-4 mb-2">
                                <h2 class="text-2xl font-bold">温度传感器</h2>
                                <span class="px-3 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                            </div>
                            <div class="text-gray-500 mb-2">设备类型ID：TYPE_001</div>
                            <p class="text-gray-600">用于监测环境温度的传感器设备类型，支持温度数据采集和报警功能。</p>
                        </div>
                    </div>
                </div>

                <!-- 功能标签页 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="border-b">
                        <div class="flex">
                            <button class="tab-button px-6 py-4 text-blue-600 border-b-2 border-blue-600 font-medium" onclick="showTab('devices')">
                                关联设备
                            </button>
                            <button class="tab-button px-6 py-4 text-gray-500" onclick="showTab('functions')">
                                点位
                            </button>
                            <button class="tab-button px-6 py-4 text-gray-500" onclick="showTab('alarms')">
                                报警
                            </button>
                            <button class="tab-button px-6 py-4 text-gray-500" onclick="showTab('rules')">
                                规则
                            </button>
                            <button class="tab-button px-6 py-4 text-gray-500" onclick="showTab('tasks')">
                                任务
                            </button>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <!-- 关联设备内容 -->
                    <div id="devices" class="tab-content p-6">
                        <div class="flex justify-between mb-6">
                            <div class="flex space-x-4">
                                <input type="text" placeholder="搜索设备" class="border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg hover:bg-gray-200">
                                    <i class="ri-filter-3-line mr-2"></i>筛选
                                </button>
                            </div>
                            <button onclick="toggleAddDeviceDrawer()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center">
                                <i class="ri-add-line mr-2"></i>新增设备
                            </button>
                        </div>

                        <table class="min-w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备编码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在线状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后活跃时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">DEV_001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">温度传感器-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">在线</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-gray-500">2024-03-15 14:30:00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-red-50 text-red-600 rounded-full text-sm">有报警</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                        <a href="device-detail.html" class="text-blue-600 hover:text-blue-800">查看</a>
                                        <button onclick="toggleEditDeviceDrawer(this)" class="text-green-600 hover:text-green-800">编辑</button>
                                        <button onclick="deleteDevice(this)" class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                                <!-- 可以添加更多行... -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 功能定义标签页内容 -->
                    <div id="functions" class="tab-content p-6 hidden">
                        <!-- 点位列表部分 -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center space-x-4">
                                    <!-- <h3 class="text-lg font-bold">点位列表</h3> -->
                                    <!-- 搜索框区域 -->
                                    <div class="flex space-x-4">
                                        <!-- 点位名称搜索 -->
                                        <div class="relative">
                                            <input type="text" placeholder="搜索点位名称" 
                                                class="pl-4 pr-10 py-2 border rounded-lg w-64 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                                            <i class="ri-search-line absolute right-3 top-2.5 text-gray-400"></i>
                                        </div>
                                        <!-- 标识符搜索 -->
                                        <div class="relative">
                                            <input type="text" placeholder="搜索标识符" 
                                                class="pl-4 pr-10 py-2 border rounded-lg w-64 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                                            <i class="ri-search-line absolute right-3 top-2.5 text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                <button onclick="toggleAddPointDrawer()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                    <i class="ri-add-line"></i>
                                    <span>新增点位</span>
                                </button>
                                    <button onclick="toggleImportPointsModal()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center space-x-2">
                                        <i class="ri-upload-2-line"></i>
                                        <span>导入点位</span>
                                    </button>
                                    <button onclick="exportPoints()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                        <i class="ri-download-2-line"></i>
                                        <span>导出点位</span>
                                </button>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg shadow overflow-hidden">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位名称</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标识符</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位类型</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据类型</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位属性</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <!-- 示例数据 -->
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">温度值</td>
                                            <td class="px-6 py-4 whitespace-nowrap">temperature</td>
                                            <td class="px-6 py-4 whitespace-nowrap">上报</td>
                                            <td class="px-6 py-4 whitespace-nowrap">数值</td>
                                            <td class="px-6 py-4 whitespace-nowrap">温度</td>
                                            <td class="px-6 py-4 whitespace-nowrap">°C</td>
                                            <td class="px-6 py-4">设备当前温度值</td>
                                            <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                                <!-- <button class="text-blue-600 hover:text-blue-800">查看</button> -->
                                                <button class="text-green-600 hover:text-green-800">编辑</button>
                                                <button class="text-red-600 hover:text-red-800">删除</button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">湿度值</td>
                                            <td class="px-6 py-4 whitespace-nowrap">humidity</td>
                                            <td class="px-6 py-4 whitespace-nowrap">上报</td>
                                            <td class="px-6 py-4 whitespace-nowrap">数值</td>
                                            <td class="px-6 py-4 whitespace-nowrap">湿度</td>
                                            <td class="px-6 py-4 whitespace-nowrap">%</td>
                                            <td class="px-6 py-4">设备当前湿度值</td>
                                            <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                                <!-- <button class="text-blue-600 hover:text-blue-800">查看</button> -->
                                                <button class="text-green-600 hover:text-green-800">编辑</button>
                                                <button class="text-red-600 hover:text-red-800">删除</button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">工作模式</td>
                                            <td class="px-6 py-4 whitespace-nowrap">workMode</td>
                                            <td class="px-6 py-4 whitespace-nowrap">下发</td>
                                            <td class="px-6 py-4 whitespace-nowrap">枚举</td>
                                            <td class="px-6 py-4 whitespace-nowrap">-</td>
                                            <td class="px-6 py-4 whitespace-nowrap">-</td>
                                            <td class="px-6 py-4">设备工作模式：自动/手动</td>
                                            <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                                <!-- <button class="text-blue-600 hover:text-blue-800">查看</button> -->
                                                <button class="text-green-600 hover:text-green-800">编辑</button>
                                                <button class="text-red-600 hover:text-red-800">删除</button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">开关状态</td>
                                            <td class="px-6 py-4 whitespace-nowrap">switch</td>
                                            <td class="px-6 py-4 whitespace-nowrap">下发</td>
                                            <td class="px-6 py-4 whitespace-nowrap">布尔</td>
                                            <td class="px-6 py-4 whitespace-nowrap">-</td>
                                            <td class="px-6 py-4 whitespace-nowrap">-</td>
                                            <td class="px-6 py-4">设备开关状态</td>
                                            <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                                <!-- <button class="text-blue-600 hover:text-blue-800">查看</button> -->
                                                <button class="text-green-600 hover:text-green-800">编辑</button>
                                                <button class="text-red-600 hover:text-red-800">删除</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 报警 -->
                    <div id="alarms" class="tab-content p-6 hidden">
                        <div class="flex justify-between items-center mb-6">
                            <!-- <h3 class="text-lg font-bold">报警配置</h3> -->
                            <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2" onclick="toggleAddAlarmRuleDrawer()">
                                <i class="ri-add-line"></i>
                                <span>新增报警规则</span>
                            </button>
                        </div>

                        <!-- 报警规则列表 -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">触发条件</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知组</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">温度过高报警</td>
                                        <td class="px-6 py-4 whitespace-nowrap">温度 > 30°C</td>
                                        <td class="px-6 py-4 whitespace-nowrap">运维组</td>
                                        <td class="px-6 py-4 whitespace-nowrap">2024-01-15 10:30:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                            <button class="text-blue-600 hover:text-blue-800">查看</button>
                                            <button class="text-green-600 hover:text-green-800">编辑</button>
                                            <button class="text-red-600 hover:text-red-800">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">湿度过低报警</td>
                                        <td class="px-6 py-4 whitespace-nowrap">湿度 < 20%</td>
                                        <td class="px-6 py-4 whitespace-nowrap">运维组</td>
                                        <td class="px-6 py-4 whitespace-nowrap">2024-01-16 14:20:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 bg-gray-50 text-gray-600 rounded-full text-sm">已禁用</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                            <button class="text-blue-600 hover:text-blue-800">查看</button>
                                            <button class="text-green-600 hover:text-green-800">编辑</button>
                                            <button class="text-red-600 hover:text-red-800">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 规则内容 -->
                    <div id="rules" class="tab-content p-6 hidden">
                        <div class="flex justify-between items-center mb-6">
                            <!-- <h3 class="text-lg font-bold">规则配置</h3> -->
                            <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2" onclick="toggleAddRuleDrawer()">
                                <i class="ri-add-line"></i>
                                <span>添加规则</span>
                            </button>
                        </div>

                        <!-- 规则列表 -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">触发条件</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">温度超限处理规则</td>
                                        <td class="px-6 py-4 whitespace-nowrap">属性上报预处理</td>
                                        <td class="px-6 py-4 whitespace-nowrap">温度 > 30°C</td>
                                        <td class="px-6 py-4 whitespace-nowrap">2024-01-15 10:30:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                            <button class="text-blue-600 hover:text-blue-800">查看</button>
                                            <button class="text-green-600 hover:text-green-800">编辑</button>
                                            <button class="text-red-600 hover:text-red-800">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">湿度数据转换规则</td>
                                        <td class="px-6 py-4 whitespace-nowrap">属性下发预处理</td>
                                        <td class="px-6 py-4 whitespace-nowrap">湿度 < 20%</td>
                                        <td class="px-6 py-4 whitespace-nowrap">2024-01-16 14:20:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 bg-gray-50 text-gray-600 rounded-full text-sm">已禁用</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                            <button class="text-blue-600 hover:text-blue-800">查看</button>
                                            <button class="text-green-600 hover:text-green-800">编辑</button>
                                            <button class="text-red-600 hover:text-red-800">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 任务内容 -->
                    <div id="tasks" class="tab-content p-6 hidden">
                        <div class="flex justify-between items-center mb-6">
                            <!-- <h3 class="text-lg font-bold">任务配置</h3> -->
                            <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2" onclick="toggleAddTaskDrawer()">
                                <i class="ri-add-line"></i>
                                <span>添加任务</span>
                            </button>
                        </div>

                        <!-- 任务列表 -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行周期</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">温度数据定时采集</td>
                                        <td class="px-6 py-4 whitespace-nowrap">属性采集</td>
                                        <td class="px-6 py-4 whitespace-nowrap">每5分钟</td>
                                        <td class="px-6 py-4 whitespace-nowrap">2024-01-15 10:30:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">已启用</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                            <button class="text-blue-600 hover:text-blue-800">查看</button>
                                            <button class="text-green-600 hover:text-green-800">编辑</button>
                                            <button class="text-red-600 hover:text-red-800">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">设备状态检查</td>
                                        <td class="px-6 py-4 whitespace-nowrap">状态检查</td>
                                        <td class="px-6 py-4 whitespace-nowrap">每天 00:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">2024-01-16 14:20:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 bg-gray-50 text-gray-600 rounded-full text-sm">已禁用</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                                            <button class="text-blue-600 hover:text-blue-800">查看</button>
                                            <button class="text-green-600 hover:text-green-800">编辑</button>
                                            <button class="text-red-600 hover:text-red-800">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增设备抽屉 -->
    <div id="addDeviceDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddDeviceDrawer()"></div>
        
        <!-- 侧边栏内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-medium">新增设备</h3>
                        <button onclick="toggleAddDeviceDrawer()" class="text-gray-400 hover:text-gray-500">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 表单内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addDeviceForm" class="space-y-6">
                        <!-- 设备编码 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备编码</label>
                            <input type="text" name="deviceCode" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备名称</label>
                            <input type="text" name="deviceName" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备类型</label>
                            <input type="text" value="温度传感器" disabled
                                class="w-full px-3 py-2 bg-gray-100 border rounded-lg text-gray-500 cursor-not-allowed">
                        </div>

                        <!-- 所属空间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属空间</label>
                            <select name="space" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择空间</option>
                                <option value="1">A栋1层</option>
                                <option value="2">A栋2层</option>
                                <option value="3">B栋1层</option>
                            </select>
                        </div>

                        <!-- 设备组 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备组</label>
                            <select name="deviceGroup" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择设备组</option>
                                <option value="1">温控设备组</option>
                                <option value="2">监控设备组</option>
                            </select>
                        </div>

                        <!-- 状态 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="enabled" checked
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">启用</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="disabled"
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">停用</span>
                                </label>
                            </div>
                        </div>

                        <!-- 设备描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备描述</label>
                            <textarea name="description" rows="4"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 底部按钮 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleAddDeviceDrawer()" 
                            class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitAddDevice()" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增点位抽屉 -->
    <div id="addPointDrawer" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">新增点位</h3>
                    <button class="text-gray-500 hover:text-gray-700" onclick="toggleAddPointDrawer()">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>
                
                <form id="addPointForm" class="space-y-4">
                    <!-- 点位名称 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <span class="text-red-500">*</span> 点位名称
                        </label>
                        <div class="relative">
                            <input type="text" name="pointName" maxlength="30"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all"
                                   placeholder="请输入点位名称"
                                   oninput="updateCharCount(this, 'pointNameCount', 30)">
                            <span id="pointNameCount" class="absolute right-3 top-2 text-xs text-gray-400">0 / 30</span>
                        </div>
                    </div>

                    <!-- 标识符 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <span class="text-red-500">*</span> 标识符
                        </label>
                        <div class="relative">
                            <input type="text" name="pointIdentifier" maxlength="48"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all"
                                   placeholder="请输入标识符"
                                   oninput="updateCharCount(this, 'pointIdentifierCount', 48)">
                            <span id="pointIdentifierCount" class="absolute right-3 top-2 text-xs text-gray-400">0 / 48</span>
                        </div>
                    </div>

                    <!-- 点位类型 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <span class="text-red-500">*</span> 点位类型
                        </label>
                        <div class="flex space-x-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="pointType" value="report" checked
                                       class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">上报</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="pointType" value="control"
                                       class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">下发</span>
                            </label>
                        </div>
                    </div>

                    <!-- 数据类型 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <span class="text-red-500">*</span> 数据类型
                        </label>
                        <select name="dataType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="">数值</option>
                            <option value="number">数值</option>
                            <option value="boolean">布尔</option>
                            <option value="string">字符串</option>
                            <option value="enum">枚举</option>
                        </select>
                    </div>

                    <!-- 单位 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">单位</label>
                        <div class="relative">
                            <input type="text" name="pointUnit" maxlength="30"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all"
                                   placeholder=""
                                   oninput="updateCharCount(this, 'pointUnitCount', 30)">
                            <span id="pointUnitCount" class="absolute right-3 top-2 text-xs text-gray-400">0 / 30</span>
                        </div>
                    </div>

                    <!-- 默认值 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">默认值</label>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="adjustValue('defaultValue', -1)"
                                    class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <span class="text-lg">−</span>
                            </button>
                            <input type="number" name="defaultValue" id="defaultValue" value="0" step="0.01"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all text-center">
                            <button type="button" onclick="adjustValue('defaultValue', 1)"
                                    class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <span class="text-lg">+</span>
                            </button>
                        </div>
                    </div>

                    <!-- 精度 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">精度</label>
                        <select name="precision" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="">保留2位小数</option>
                            <option value="0">保留0位小数</option>
                            <option value="1">保留1位小数</option>
                            <option value="2">保留2位小数</option>
                            <option value="3">保留3位小数</option>
                            <option value="4">保留4位小数</option>
                        </select>
                    </div>

                    <!-- 最小值 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">最小值</label>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="adjustValue('minValue', -1)"
                                    class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <span class="text-lg">−</span>
                            </button>
                            <input type="number" name="minValue" id="minValue" value="" step="0.01"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all text-center">
                            <button type="button" onclick="adjustValue('minValue', 1)"
                                    class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <span class="text-lg">+</span>
                            </button>
                        </div>
                    </div>

                    <!-- 最大值 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">最大值</label>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="adjustValue('maxValue', -1)"
                                    class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <span class="text-lg">−</span>
                            </button>
                            <input type="number" name="maxValue" id="maxValue" value="" step="0.01"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all text-center">
                            <button type="button" onclick="adjustValue('maxValue', 1)"
                                    class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <span class="text-lg">+</span>
                            </button>
                        </div>
                    </div>

                    <!-- 是否存储 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">是否存储</label>
                        <div class="flex space-x-6">
                            <label class="flex items-center">
                                <input type="radio" name="isStorage" value="yes"
                                       class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">是</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="isStorage" value="no" checked
                                       class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">否</span>
                            </label>
                        </div>
                    </div>

                    <!-- 点位属性 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">点位属性</label>
                        <select name="pointAttribute" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="">请选择点位属性（可选）</option>
                            <option value="电量">电量</option>
                            <option value="水量">水量</option>
                            <option value="燃气量">燃气量</option>
                            <option value="气压">气压</option>
                            <option value="温度">温度</option>
                            <option value="湿度">湿度</option>
                            <option value="光照">光照</option>
                            <option value="噪音">噪音</option>
                            <option value="振动">振动</option>
                            <option value="流量">流量</option>
                            <option value="电压">电压</option>
                            <option value="电流">电流</option>
                            <option value="功率">功率</option>
                            <option value="频率">频率</option>
                        </select>
                    </div>

                    <!-- 点位描述 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">点位描述</label>
                        <div class="relative">
                            <textarea name="pointDescription" rows="4" maxlength="200"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all resize-none"
                                      placeholder="请输入点位描述"
                                      oninput="updateCharCount(this, 'pointDescCount', 200)"></textarea>
                            <span id="pointDescCount" class="absolute right-3 bottom-2 text-xs text-gray-400">0 / 200</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                        <button type="button" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors" onclick="toggleAddPointDrawer()">取消</button>
                        <button type="button" class="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors" onclick="submitAddPoint()">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新增报警规则抽屉 -->
    <div id="addAlarmRuleDrawer" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">新增报警规则</h3>
                    <button class="text-gray-500 hover:text-gray-700" onclick="toggleAddAlarmRuleDrawer()">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>
                
                <form id="addAlarmRuleForm" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                        <input type="text" name="ruleName" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入规则名称">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">触发条件</label>
                        <div id="thresholdList" class="space-y-4">
                            <div class="flex items-center space-x-2">
                                <select name="property" class="flex-1 px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                                    <option value="">选择属性</option>
                                    <option value="temperature">温度</option>
                                    <option value="humidity">湿度</option>
                                    <option value="power">电量</option>
                                </select>
                                <select name="operator" class="px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                                    <option value="gt">大于</option>
                                    <option value="lt">小于</option>
                                    <option value="eq">等于</option>
                                    <option value="neq">不等于</option>
                                    <option value="gte">大于等于</option>
                                    <option value="lte">小于等于</option>
                                </select>
                                <input type="number" name="value" class="w-32 px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="阈值">
                                <button type="button" class="text-red-500 hover:text-red-700" onclick="removeThreshold(this)">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        </div>
                        <button type="button" class="mt-2 text-blue-500 hover:text-blue-700 flex items-center text-sm" onclick="addThreshold()">
                            <i class="ri-add-line mr-1"></i> 添加条件
                        </button>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">报警等级</label>
                        <select name="alarmLevel" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="1">一级报警</option>
                            <option value="2">二级报警</option>
                            <option value="3">三级报警</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">通知方式</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="notifyEmail" class="mr-2 text-blue-500">
                                邮件通知
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="notifySMS" class="mr-2 text-blue-500">
                                短信通知
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="notifyWechat" class="mr-2 text-blue-500">
                                微信通知
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">通知组</label>
                        <select name="notificationGroup" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="ops">运维组</option>
                            <option value="dev">开发组</option>
                            <option value="admin">管理员组</option>
                        </select>
                    </div>

                    <div>
                        <div class="flex items-center justify-between">
                            <label class="block text-sm font-medium text-gray-700">生效时间</label>
                            <label class="flex items-center text-sm text-gray-600">
                                <input type="checkbox" id="enableTimeRange" class="mr-2 text-blue-500">
                                启用时间范围
                            </label>
                        </div>
                        <div id="timeRangeSelector" class="mt-2 p-4 border rounded-lg hidden">
                            <!-- 时间选择器将通过 JavaScript 动态生成 -->
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则描述</label>
                        <textarea name="description" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入规则描述..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" class="px-6 py-2 border rounded-lg hover:bg-gray-50" onclick="toggleAddAlarmRuleDrawer()">取消</button>
                        <button type="button" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600" onclick="submitAddAlarmRule()">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新增规则抽屉 -->
    <div id="addRuleDrawer" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">新增规则</h3>
                    <button class="text-gray-500 hover:text-gray-700" onclick="toggleAddRuleDrawer()">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>
                
                <form id="addRuleForm" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                        <input type="text" name="ruleName" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入规则名称">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则类型</label>
                        <select name="ruleType" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="property_report">属性上报预处理</option>
                            <option value="property_set">属性下发预处理</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">触发条件</label>
                        <div id="conditionList" class="space-y-4">
                            <div class="condition-item flex items-center space-x-2">
                                <select name="property" class="flex-1 px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                                    <option value="">选择属性</option>
                                    <option value="temperature">温度</option>
                                    <option value="humidity">湿度</option>
                                    <option value="power">电量</option>
                                </select>
                                <select name="operator" class="px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                                    <option value="gt">大于</option>
                                    <option value="lt">小于</option>
                                    <option value="eq">等于</option>
                                    <option value="neq">不等于</option>
                                    <option value="gte">大于等于</option>
                                    <option value="lte">小于等于</option>
                                </select>
                                <input type="text" name="value" class="w-32 px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="值">
                                <button type="button" class="text-red-500 hover:text-red-700" onclick="removeCondition(this)">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        </div>
                        <button type="button" class="mt-2 text-blue-500 hover:text-blue-700 flex items-center text-sm" onclick="addRuleCondition()">
                            <i class="ri-add-line mr-1"></i> 添加条件
                        </button>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">执行动作</label>
                        <select name="action" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="transform">数据转换</option>
                            <option value="filter">数据过滤</option>
                            <option value="enrich">数据增强</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则描述</label>
                        <textarea name="description" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入规则描述..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" class="px-6 py-2 border rounded-lg hover:bg-gray-50" onclick="toggleAddRuleDrawer()">取消</button>
                        <button type="button" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600" onclick="submitAddRule()">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑设备抽屉 -->
    <div id="editDeviceDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleEditDeviceDrawer()"></div>
        
        <!-- 侧边栏内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-medium">编辑设备</h3>
                        <button onclick="toggleEditDeviceDrawer()" class="text-gray-400 hover:text-gray-500">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 表单内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="editDeviceForm" class="space-y-6">
                        <!-- 设备编码 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备编码</label>
                            <input type="text" name="deviceCode" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备名称</label>
                            <input type="text" name="deviceName" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 设备类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备类型</label>
                            <input type="text" value="温度传感器" disabled
                                class="w-full px-3 py-2 bg-gray-100 border rounded-lg text-gray-500 cursor-not-allowed">
                        </div>

                        <!-- 所属空间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属空间</label>
                            <select name="space" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择空间</option>
                                <option value="1">A栋1层</option>
                                <option value="2">A栋2层</option>
                                <option value="3">B栋1层</option>
                            </select>
                        </div>

                        <!-- 设备组 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备组</label>
                            <select name="deviceGroup" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择设备组</option>
                                <option value="1">温控设备组</option>
                                <option value="2">监控设备组</option>
                            </select>
                        </div>

                        <!-- 状态 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="enabled" checked
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">启用</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="disabled"
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">停用</span>
                                </label>
                            </div>
                        </div>

                        <!-- 设备描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备描述</label>
                            <textarea name="description" rows="4"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 底部按钮 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleEditDeviceDrawer()" 
                            class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitEditDevice()" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增任务抽屉 -->
    <div id="addTaskDrawer" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300 h-full overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">新增任务</h3>
                    <button class="text-gray-500 hover:text-gray-700" onclick="toggleAddTaskDrawer()">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>
                
                <form id="addTaskForm" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">任务名称</label>
                        <input type="text" name="taskName" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入任务名称">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">任务类型</label>
                        <select name="taskType" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="">请选择任务类型</option>
                            <option value="1">定时任务</option>
                            <option value="2">周期任务</option>
                            <option value="3">事件触发任务</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                        <textarea name="taskDescription" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入任务描述..."></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" class="px-6 py-2 border rounded-lg hover:bg-gray-50" onclick="toggleAddTaskDrawer()">取消</button>
                        <button type="button" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600" onclick="submitAddTask()">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 编辑设备抽屉控制
        function toggleEditDeviceDrawer(button) {
            const drawer = document.getElementById('editDeviceDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                if (button) {
                    // 获取当前行的数据并填充表单
                    const row = button.closest('tr');
                    const form = document.getElementById('editDeviceForm');
                    form.querySelector('[name="deviceCode"]').value = row.cells[0].textContent;
                    form.querySelector('[name="deviceName"]').value = row.cells[1].textContent;
                    // 其他字段的填充...
                }
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 提交编辑设备
        function submitEditDevice() {
            const form = document.getElementById('editDeviceForm');
            const formData = new FormData(form);
            
            // 更新表格中的数据
            // ... 实现更新逻辑

            // 关闭抽屉
            toggleEditDeviceDrawer();
        }

        // 删除设备
        function deleteDevice(button) {
            if (confirm('确定要删除该设备吗？')) {
                button.closest('tr').remove();
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 默认显示关联设备标签页
            showTab('devices');
        });
    </script>

    <!-- 导入点位模态框 -->
    <div id="importPointsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden">
            <div class="bg-white p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">批量导入点位</h3>
                    <button class="text-gray-500 hover:text-gray-700" onclick="toggleImportPointsModal()">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
</div>
                
                <form id="importPointsForm" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed border-gray-300 rounded-md">
                            <div class="space-y-1 text-center">
                                <i class="ri-upload-2-line text-gray-400 text-3xl mb-2"></i>
                                <div class="flex text-sm text-gray-600">
                                    <label for="pointsFile" class="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                                        <span>上传文件</span>
                                        <input id="pointsFile" name="pointsFile" type="file" class="sr-only" accept=".csv, .xlsx, .xls">
                                    </label>
                                    <p class="pl-1">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    支持 CSV、Excel 格式文件
                                </p>
                            </div>
                        </div>
                        <div id="fileInfo" class="text-sm text-gray-500 mt-2"></div>
                    </div>
                    
                    <div class="mt-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">导入模式</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="importMode" value="append" checked class="mr-2 text-blue-500">
                                <span>追加</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="importMode" value="replace" class="mr-2 text-blue-500">
                                <span>替换</span>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">
                            追加：保留现有点位，添加新点位；替换：删除所有现有点位，替换为导入的点位
                        </p>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" onclick="toggleImportPointsModal()">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700" onclick="importPoints()">
                            开始导入
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 点位模板下载链接 -->
    <a id="pointsTemplateLink" href="#" download="点位导入模板.xlsx" class="hidden"></a>

    <script src="./js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置设备配置菜单为展开状态
            const deviceConfigItem = document.querySelector('.nav-item:nth-child(3)');
            deviceConfigItem.classList.add('active');
            
            // 文件上传预览
            const fileInput = document.getElementById('pointsFile');
            const fileInfo = document.getElementById('fileInfo');
            
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    fileInfo.textContent = `已选择: ${file.name} (${formatFileSize(file.size)})`;
            } else {
                    fileInfo.textContent = '';
                }
            });
            
            // 拖拽上传
            const dropZone = document.querySelector('.border-dashed');
            
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('border-blue-500');
            });
            
            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('border-blue-500');
            });
            
            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('border-blue-500');
                
                const file = e.dataTransfer.files[0];
                if (file) {
                    fileInput.files = e.dataTransfer.files;
                    fileInfo.textContent = `已选择: ${file.name} (${formatFileSize(file.size)})`;
                }
            });
        });
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
            else return (bytes / 1048576).toFixed(2) + ' MB';
        }
        
        // 导入点位模态框控制
        function toggleImportPointsModal() {
            const modal = document.getElementById('importPointsModal');
            modal.classList.toggle('hidden');
            
            // 重置表单
            if (!modal.classList.contains('hidden')) {
                document.getElementById('fileInfo').textContent = '';
                document.getElementById('pointsFile').value = '';
            }
        }
        
        // 导入点位
        function importPoints() {
            const fileInput = document.getElementById('pointsFile');
            const importMode = document.querySelector('input[name="importMode"]:checked').value;
            
            if (!fileInput.files.length) {
                alert('请选择要导入的文件');
                return;
            }
            
            // 模拟导入过程
            // 实际应用中，这里应该发送文件到服务器进行处理
            
            // 显示导入中状态
            const importButton = document.querySelector('#importPointsForm button[type="button"]:last-child');
            const originalText = importButton.innerHTML;
            importButton.disabled = true;
            importButton.innerHTML = '<i class="ri-loader-2-line animate-spin mr-2"></i>导入中...';
            
            // 模拟导入过程
                setTimeout(() => {
                alert('点位导入成功');
                importButton.disabled = false;
                importButton.innerHTML = originalText;
                toggleImportPointsModal();
                
                // 模拟添加导入的点位到表格
                if (importMode === 'replace') {
                    // 替换模式：清空现有点位
                    const tbody = document.querySelector('#functions table tbody');
                    tbody.innerHTML = '';
                }
                
                // 添加模拟数据
                const samplePoints = [
                    {name: '电源状态', identifier: 'power_status', type: '上报', dataType: 'boolean', desc: '设备电源状态'},
                    {name: '信号强度', identifier: 'signal_strength', type: '上报', dataType: 'integer', desc: '设备信号强度'},
                ];
                
                const tbody = document.querySelector('#functions table tbody');
                samplePoints.forEach(point => {
            const newRow = `
                <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">${point.name}</td>
                            <td class="px-6 py-4 whitespace-nowrap">${point.identifier}</td>
                            <td class="px-6 py-4 whitespace-nowrap">${point.type}</td>
                            <td class="px-6 py-4 whitespace-nowrap">${point.dataType}</td>
                            <td class="px-6 py-4">${point.desc}</td>
                    <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                        <button class="text-green-600 hover:text-green-800">编辑</button>
                        <button class="text-red-600 hover:text-red-800">删除</button>
                    </td>
                </tr>
            `;
                    tbody.insertAdjacentHTML('beforeend', newRow);
                });
            }, 1500);
        }
        
        // 导出点位
        function exportPoints() {
            // 模拟导出功能
            // 在实际应用中，应当向服务器发送请求，获取导出文件
            alert('正在导出点位数据，请稍候...');
            
            // 模拟下载过程
                setTimeout(() => {
                alert('点位导出成功');
                
                // 创建一个假的下载链接（仅用于演示）
                // 实际应用中，这里应该下载服务器生成的文件
                const link = document.createElement('a');
                link.href = '#';
                link.setAttribute('download', '点位导出数据.xlsx');
                link.click();
            }, 1000);
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            // 重置所有标签按钮样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                button.classList.add('text-gray-500');
            });
            // 显示选中的标签页
            document.getElementById(tabName).classList.remove('hidden');
            // 设置选中标签按钮样式
            event.currentTarget.classList.remove('text-gray-500');
            event.currentTarget.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        }

        // 字符计数更新函数
        function updateCharCount(input, counterId, maxLength) {
            const currentLength = input.value.length;
            const counter = document.getElementById(counterId);
            counter.textContent = `${currentLength} / ${maxLength}`;

            // 如果接近限制，改变颜色
            if (currentLength > maxLength * 0.8) {
                counter.classList.add('text-orange-500');
            } else {
                counter.classList.remove('text-orange-500');
            }

            if (currentLength >= maxLength) {
                counter.classList.add('text-red-500');
            } else {
                counter.classList.remove('text-red-500');
            }
        }

        // 数值调整函数
        function adjustValue(inputId, delta) {
            const input = document.getElementById(inputId);
            const currentValue = parseFloat(input.value) || 0;
            const step = parseFloat(input.step) || 1;
            const newValue = currentValue + (delta * step);
            input.value = newValue;
        }

        // 新增点位抽屉控制
        function toggleAddPointDrawer() {
            const drawer = document.getElementById('addPointDrawer');
            const drawerContent = drawer.querySelector('.absolute');

            if (drawer.classList.contains('hidden')) {
                // 显示抽屉
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 10);

                // 重置表单
                document.getElementById('addPointForm').reset();
                // 重置字符计数
                resetCharCounters();
            } else {
                // 隐藏抽屉
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 重置字符计数器
        function resetCharCounters() {
            document.getElementById('pointNameCount').textContent = '0 / 30';
            document.getElementById('pointIdentifierCount').textContent = '0 / 48';
            document.getElementById('pointUnitCount').textContent = '0 / 30';
            document.getElementById('pointDescCount').textContent = '0 / 200';
        }

        // 提交新增点位
        function submitAddPoint() {
            const form = document.getElementById('addPointForm');
            const formData = new FormData(form);

            // 获取点位类型（可能有多个选中）
            const pointTypes = [];
            const pointTypeInputs = form.querySelectorAll('input[name="pointType"]:checked');
            pointTypeInputs.forEach(input => pointTypes.push(input.value));

            // 获取表单数据
            const pointData = {
                name: formData.get('pointName'),
                identifier: formData.get('pointIdentifier'),
                types: pointTypes,
                dataType: formData.get('dataType'),
                unit: formData.get('pointUnit'),
                defaultValue: formData.get('defaultValue'),
                precision: formData.get('precision'),
                minValue: formData.get('minValue'),
                maxValue: formData.get('maxValue'),
                isStorage: formData.get('isStorage'),
                attribute: formData.get('pointAttribute'),
                description: formData.get('pointDescription')
            };

            // 验证必填字段
            if (!pointData.name || !pointData.identifier || pointTypes.length === 0 || !pointData.dataType) {
                alert('请填写所有必填字段（点位名称、标识符、点位类型、数据类型）');
                return;
            }

            // 验证标识符格式
            const identifierRegex = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
            if (!identifierRegex.test(pointData.identifier)) {
                alert('标识符格式不正确，只能包含英文字母、数字和下划线，且不能以数字开头');
                return;
            }

            // 验证数值范围
            if (pointData.minValue && pointData.maxValue) {
                const min = parseFloat(pointData.minValue);
                const max = parseFloat(pointData.maxValue);
                if (min >= max) {
                    alert('最小值必须小于最大值');
                    return;
                }
            }

            // 模拟提交到服务器
            console.log('新增点位数据:', pointData);

            // 添加到点位列表表格
            const tbody = document.querySelector('#functions table tbody');
            const typeDisplay = pointTypes.map(type => type === 'report' ? '上报' : '下发').join('/');
            const newRow = `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">${pointData.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${pointData.identifier}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${typeDisplay}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${pointData.dataType}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${pointData.attribute || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${pointData.unit || '-'}</td>
                    <td class="px-6 py-4">${pointData.description || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap space-x-3 text-sm">
                        <button class="text-green-600 hover:text-green-800">编辑</button>
                        <button class="text-red-600 hover:text-red-800">删除</button>
                    </td>
                </tr>
            `;
            tbody.insertAdjacentHTML('beforeend', newRow);

            // 显示成功消息
            alert('点位添加成功');

            // 关闭抽屉
            toggleAddPointDrawer();
        }
    </script>
</body>
</html> 