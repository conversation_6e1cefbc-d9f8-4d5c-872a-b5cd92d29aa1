<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备类型管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 text-white bg-blue-600 rounded-md flex items-center cursor-pointer px-2">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space-type.html" class="flex items-center w-full">
                                    <i class="ri-layout-line mr-2"></i> 空间类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-building-2-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-organization-chart-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">设备配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">设备类型管理</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="flex items-center space-x-1 text-gray-500 hover:text-blue-600">
                            <i class="ri-refresh-line text-xl"></i>
                            <span>刷新</span>
                        </button>
                        <div class="h-5 w-px bg-gray-300"></div>
                        <div class="flex items-center space-x-3">
                            <img src="https://via.placeholder.com/36" class="w-8 h-8 rounded-full border-2 border-blue-100" alt="用户头像">
                            <span class="text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 搜索和操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4 items-center">
                            <div class="relative">
                                <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" placeholder="请输入类型名称" 
                                    class="pl-10 pr-4 py-2 border rounded-lg w-64 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            </div>
                            <button class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-search-line"></i>
                                <span>搜索</span>
                            </button>
                            <button class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                                <i class="ri-refresh-line"></i>
                                <span>重置</span>
                            </button>
                        </div>
                        <div class="flex space-x-4 items-center">
                            <button onclick="toggleAddTypeDrawer()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-add-line"></i>
                                <span>新增类型</span>
                            </button>
                            <button class="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-colors flex items-center space-x-2">
                                <i class="ri-delete-bin-line"></i>
                                <span>批量删除</span>
                            </button>
                            <button onclick="toggleView()" class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                                <i class="ri-layout-grid-line"></i>
                                <span>切换视图</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" class="grid grid-cols-3 gap-6">
                    <!-- 设备类型卡片 -->
                    <div onclick="location.href='device-type-detail.html'" class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-sensor-line text-3xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">温度传感器</h3>
                                        <p class="text-gray-500 text-sm">ID: TYPE_001</p>
                                    </div>
                                    <span class="px-3 py-1 text-green-600 bg-green-50 rounded-full text-sm font-medium">启用</span>
                                </div>
                                <p class="text-gray-600 mt-2 text-sm">用于监测环境温度的传感器设备类型</p>
                                <div class="flex space-x-3 mt-4">
                                    <button onclick="event.stopPropagation(); location.href='device-type-detail.html'" class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                        <i class="ri-eye-line"></i>
                                        <span>查看</span>
                                    </button>
                                    <button onclick="event.stopPropagation(); editDeviceType(1)" class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button onclick="event.stopPropagation(); deleteDeviceType(1)" class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第二个卡片示例 -->
                    <div onclick="location.href='device-type-detail.html'" class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-sensor-line text-3xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">湿度传感器</h3>
                                        <p class="text-gray-500 text-sm">ID: TYPE_002</p>
                                    </div>
                                    <span class="px-3 py-1 text-green-600 bg-green-50 rounded-full text-sm font-medium">启用</span>
                                </div>
                                <p class="text-gray-600 mt-2 text-sm">用于监测环境湿度的传感器设备类型</p>
                                <div class="flex space-x-3 mt-4">
                                    <button onclick="event.stopPropagation(); location.href='device-type-detail.html'" class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                        <i class="ri-eye-line"></i>
                                        <span>查看</span>
                                    </button>
                                    <button onclick="event.stopPropagation(); editDeviceType(2)" class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button onclick="event.stopPropagation(); deleteDeviceType(2)" class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第三个卡片示例 -->
                    <div onclick="location.href='device-type-detail.html'" class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-sensor-line text-3xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">压力传感器</h3>
                                        <p class="text-gray-500 text-sm">ID: TYPE_003</p>
                                    </div>
                                    <span class="px-3 py-1 text-yellow-600 bg-yellow-50 rounded-full text-sm font-medium">停用</span>
                                </div>
                                <p class="text-gray-600 mt-2 text-sm">用于监测环境压力的传感器设备类型</p>
                                <div class="flex space-x-3 mt-4">
                                    <button onclick="event.stopPropagation(); location.href='device-type-detail.html'" class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                        <i class="ri-eye-line"></i>
                                        <span>查看</span>
                                    </button>
                                    <button onclick="event.stopPropagation(); editDeviceType(3)" class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button onclick="event.stopPropagation(); deleteDeviceType(3)" class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 列表视图 -->
                <div id="listView" class="hidden">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        设备类型
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        ID
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        描述
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        状态
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="location.href='device-type-detail.html'">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 rounded bg-blue-50 flex items-center justify-center">
                                                <i class="ri-sensor-line text-xl text-blue-500"></i>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">温度传感器</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">TYPE_001</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">用于监测环境温度的传感器设备类型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-3 py-1 text-green-600 bg-green-50 rounded-full text-sm font-medium">
                                            启用
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                        <button class="text-blue-600 hover:text-blue-800">查看</button>
                                        <button class="text-green-600 hover:text-green-800">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                                <!-- 可以添加更多行... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增设备类型抽屉 -->
    <div id="addTypeDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddTypeDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-medium">新增设备类型</h3>
                        <button onclick="toggleAddTypeDrawer()" class="text-gray-400 hover:text-gray-500">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 表单内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addTypeForm" class="space-y-6">
                        <!-- 设备类型名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备类型名称</label>
                            <input type="text" name="typeName" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 图标上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">图标</label>
                            <div class="flex items-center space-x-4">
                                <div id="iconPreview" class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="ri-image-line text-2xl text-gray-400"></i>
                                </div>
                                <div>
                                    <button type="button" onclick="document.getElementById('iconInput').click()" 
                                        class="px-4 py-2 border rounded-lg hover:bg-gray-50">
                                        上传图标
                                    </button>
                                    <input type="file" id="iconInput" accept="image/*" class="hidden" onchange="previewIcon(event)">
                                    <p class="text-xs text-gray-500 mt-1">支持 JPG、PNG 格式，建议尺寸 128x128</p>
                                </div>
                            </div>
                        </div>

                        <!-- 设备类型描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备类型描述</label>
                            <textarea name="description" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入设备类型描述..."></textarea>
                        </div>

                    </form>
                </div>

                <!-- 底部按钮 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleAddTypeDrawer()" 
                            class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitAddType()" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置设备配置菜单为展开状态
            const deviceConfigItem = document.querySelector('.nav-item:nth-child(3)');
            deviceConfigItem.classList.add('active');
        });

        // 切换视图
        function toggleView() {
            const cardView = document.getElementById('cardView');
            const listView = document.getElementById('listView');
            const viewIcon = document.querySelector('.ri-layout-grid-line');

            if (cardView.classList.contains('hidden')) {
                cardView.classList.remove('hidden');
                listView.classList.add('hidden');
            } else {
                cardView.classList.add('hidden');
                listView.classList.remove('hidden');
            }
        }

        // 切换抽屉显示状态
        function toggleAddTypeDrawer() {
            const drawer = document.getElementById('addTypeDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 预览图标
        function previewIcon(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('iconPreview');
                    preview.innerHTML = `<img src="${e.target.result}" class="w-10 h-10 object-contain">`;
                }
                reader.readAsDataURL(file);
            }
        }

        // 提交新增类型
        function submitAddType() {
            const form = document.getElementById('addTypeForm');
            const formData = new FormData(form);
            
            // 创建新的类型卡片
            const newCard = `
                <div onclick="location.href='device-type-detail.html'" class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                    <div class="flex items-start space-x-4">
                        <div class="w-16 h-16 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                            <i class="ri-device-line text-3xl text-blue-500"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-lg font-bold text-gray-800">${formData.get('typeName')}</h3>
                                    <p class="text-gray-500 text-sm">ID: TYPE_${Date.now()}</p>
                                </div>
                                <span class="px-3 py-1 text-green-600 bg-green-50 rounded-full text-sm font-medium">启用</span>
                            </div>
                            <p class="text-gray-600 mt-2 text-sm">${formData.get('description')}</p>
                            <div class="flex space-x-3 mt-4">
                                <button onclick="event.stopPropagation(); location.href='device-type-detail.html'" class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                    <i class="ri-eye-line"></i>
                                    <span>查看</span>
                                </button>
                                <button onclick="event.stopPropagation()" class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                    <i class="ri-edit-line"></i>
                                    <span>编辑</span>
                                </button>
                                <button onclick="event.stopPropagation()" class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                    <i class="ri-delete-bin-line"></i>
                                    <span>删除</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加到列表
            document.querySelector('.grid').insertAdjacentHTML('afterbegin', newCard);
            
            // 关闭抽屉并重置表单
            toggleAddTypeDrawer();
            form.reset();
            document.getElementById('iconPreview').innerHTML = '<i class="ri-image-line text-2xl text-gray-400"></i>';
        }

        // 编辑设备类型
        function editDeviceType(id) {
            event.stopPropagation();
            console.log('编辑设备类型:', id);
        }

        // 删除设备类型
        function deleteDeviceType(id) {
            event.stopPropagation();
            if (confirm('确定要删除该设备类型吗？')) {
                console.log('删除设备类型:', id);
            }
        }

        // 添加标签功能
        function addTag() {
            const tagInput = document.getElementById('tagInput');
            const tagContainer = document.getElementById('tagContainer');
            const tagText = tagInput.value.trim();
            
            if (tagText) {
                // 创建标签元素
                const tagElement = document.createElement('div');
                tagElement.className = 'flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm';
                tagElement.innerHTML = `
                    <span>${tagText}</span>
                    <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="removeTag(this)">
                        <i class="ri-close-line"></i>
                    </button>
                    <input type="hidden" name="tags[]" value="${tagText}">
                `;
                
                // 添加到容器
                tagContainer.appendChild(tagElement);
                
                // 清空输入框
                tagInput.value = '';
                tagInput.focus();
            }
        }

        // 删除标签
        function removeTag(button) {
            button.closest('div').remove();
        }

        // 监听回车键添加标签
        document.addEventListener('DOMContentLoaded', function() {
            const tagInput = document.getElementById('tagInput');
            if (tagInput) {
                tagInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        addTag();
                    }
                });
            }
        });
    </script>
</body>
</html>