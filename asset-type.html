<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资产类型管理 - 智慧物联网平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .drawer {
            transition: transform 0.3s ease-in-out;
        }
        
        /* 表格行悬停效果 */
        .table-row:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        /* 状态标签动画 */
        .status-badge {
            transition: all 0.2s ease;
        }
        
        .status-badge:hover {
            transform: scale(1.05);
        }
        
        /* 加载动画 */
        .loading {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 搜索框聚焦效果 */
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }
        
        /* 按钮悬停效果 */
        .btn-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 台账管理 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-3-line mr-2"></i>
                                <span>台账管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ledger.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 台账记录
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="asset-type.html" class="flex items-center w-full">
                                    <i class="ri-price-tag-3-line mr-2"></i> 资产类型管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="operation-log.html" class="flex items-center w-full">
                                    <i class="ri-file-text-line mr-2"></i> 操作日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 个人配置 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">台账管理</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">资产类型管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6 space-y-6">
                <!-- 操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">资产类型管理</h2>
                        <div class="flex space-x-3">
                            <button onclick="toggleAddAssetTypeDrawer()" 
                                class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-add-line text-lg"></i>
                                <span>新增类型</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索区域 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="relative">
                            <input id="searchKeyword" type="text" placeholder="搜索类型名称或编码" 
                                class="search-input w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all">
                            <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="启用">启用</option>
                                <option value="禁用">禁用</option>
                            </select>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="searchAssetType()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                <i class="ri-search-line"></i>
                            </button>
                            <button onclick="resetSearch()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                                <i class="ri-refresh-line"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 类型列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型编码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="assetTypeTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-3 flex items-center justify-between border-t">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                上一页
                            </button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下一页
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示第 <span id="pageStart" class="font-medium">1</span> 到 <span id="pageEnd" class="font-medium">10</span> 条，
                                    共 <span id="totalCount" class="font-medium">50</span> 条记录
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                                    <!-- 分页按钮将通过JavaScript动态生成 -->
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增/编辑资产类型抽屉 -->
    <div id="addAssetTypeDrawer" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-[500px] bg-white transform translate-x-full drawer">
            <div class="flex flex-col h-full">
                <!-- 抽屉头部 -->
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 id="drawerTitle" class="text-lg font-medium">新增资产类型</h3>
                        <button onclick="toggleAddAssetTypeDrawer()" class="text-gray-400 hover:text-gray-600">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="assetTypeForm" class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>

                         

                            <!-- 类型名称 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    类型名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="typeName" id="typeName"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入类型名称" required>
                            </div>

                            <!-- 状态 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    状态 <span class="text-red-500">*</span>
                                </label>
                                <select name="status" id="status"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    <option value="启用">启用</option>
                                    <option value="禁用">禁用</option>
                                </select>
                            </div>

                            <!-- 描述 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                <textarea name="description" id="description" rows="3"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                    placeholder="请输入类型描述"></textarea>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <button onclick="submitAssetTypeForm()"
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                        <button onclick="toggleAddAssetTypeDrawer()"
                            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // ==================== 资产类型管理数据和逻辑 ====================

        // 模拟资产类型数据
        let assetTypeData = [
            {
                id: 'AT001',
                typeCode: 'SERVER',
                typeName: '服务器设备',
                status: '启用',
                description: '包括各类服务器、存储设备等',
                createTime: '2024-01-15 10:30:00',
                updateTime: '2024-01-15 10:30:00'
            },
            {
                id: 'AT002',
                typeCode: 'NETWORK',
                typeName: '网络设备',
                status: '启用',
                description: '包括交换机、路由器、防火墙等网络设备',
                createTime: '2024-01-16 14:20:00',
                updateTime: '2024-01-16 14:20:00'
            },
            {
                id: 'AT003',
                typeCode: 'OFFICE',
                typeName: '办公设备',
                status: '启用',
                description: '包括电脑、打印机、投影仪等办公设备',
                createTime: '2024-01-17 09:15:00',
                updateTime: '2024-01-17 09:15:00'
            },
            {
                id: 'AT004',
                typeCode: 'SECURITY',
                typeName: '安防设备',
                status: '启用',
                description: '包括监控摄像头、门禁系统、报警器等',
                createTime: '2024-01-18 16:45:00',
                updateTime: '2024-01-18 16:45:00'
            },
            {
                id: 'AT005',
                typeCode: 'OTHER',
                typeName: '其他设备',
                status: '禁用',
                description: '不属于以上分类的其他设备',
                createTime: '2024-01-19 11:30:00',
                updateTime: '2024-01-19 11:30:00'
            }
        ];

        // 分页配置
        let currentPage = 1;
        let pageSize = 10;
        let filteredData = [...assetTypeData];
        let currentEditId = null;

        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置台账管理菜单为展开状态
            const assetTypeItem = document.querySelector('.nav-item.active');
            if (assetTypeItem) {
                assetTypeItem.classList.add('active');
            }

            // 初始化资产类型列表
            initAssetTypeList();

            // 绑定搜索事件
            bindSearchEvents();
        });

        // 初始化资产类型列表
        function initAssetTypeList() {
            filteredData = [...assetTypeData];
            renderAssetTypeTable();
            renderPagination();
        }

        // 渲染资产类型表格
        function renderAssetTypeTable() {
            const tbody = document.getElementById('assetTypeTableBody');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);

            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <div>暂无资产类型记录</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = pageData.map((item, index) => `
                <tr class="table-row cursor-pointer">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${startIndex + index + 1}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">${item.typeCode}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">${item.typeName}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge px-2 py-1 text-xs font-medium rounded-full ${getStatusClass(item.status)}">
                            ${item.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.createTime}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                        <button onclick="editAssetType('${item.id}')"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors">
                            <i class="ri-edit-line mr-1"></i>编辑
                        </button>
                        <button onclick="deleteAssetType('${item.id}')"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 transition-colors">
                            <i class="ri-delete-bin-line mr-1"></i>删除
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '启用':
                    return 'bg-green-100 text-green-800';
                case '禁用':
                    return 'bg-red-100 text-red-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 渲染分页
        function renderPagination() {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredData.length);

            // 更新统计信息
            document.getElementById('pageStart').textContent = filteredData.length > 0 ? startIndex : 0;
            document.getElementById('pageEnd').textContent = endIndex;
            document.getElementById('totalCount').textContent = filteredData.length;

            // 生成分页按钮
            const pagination = document.getElementById('pagination');
            let paginationHTML = '';

            // 上一页按钮
            paginationHTML += `
                <button onclick="changePage(${currentPage - 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}"
                    ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="ri-arrow-left-s-line"></i>
                </button>
            `;

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button onclick="changePage(${i})"
                            class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                i === currentPage
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += `
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                    `;
                }
            }

            // 下一页按钮
            paginationHTML += `
                <button onclick="changePage(${currentPage + 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}"
                    ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="ri-arrow-right-s-line"></i>
                </button>
            `;

            pagination.innerHTML = paginationHTML;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderAssetTypeTable();
                renderPagination();
            }
        }

        // 切换新增/编辑抽屉
        function toggleAddAssetTypeDrawer() {
            const drawer = document.getElementById('addAssetTypeDrawer');
            const drawerContent = drawer.querySelector('.drawer');

            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                    currentEditId = null;
                    resetForm();
                }, 300);
            }
        }

        // 重置表单
        function resetForm() {
            document.getElementById('assetTypeForm').reset();
            document.getElementById('drawerTitle').textContent = '新增资产类型';
        }

        // 生成资产类型ID
        function generateAssetTypeId() {
            const maxId = assetTypeData.reduce((max, item) => {
                const num = parseInt(item.id.replace('AT', ''));
                return num > max ? num : max;
            }, 0);
            return 'AT' + String(maxId + 1).padStart(3, '0');
        }

        // 提交资产类型表单
        function submitAssetTypeForm() {
            const form = document.getElementById('assetTypeForm');
            const formData = new FormData(form);

            // 表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 检查类型编码是否重复
            const typeCode = formData.get('typeCode');
            const existingItem = assetTypeData.find(item =>
                item.typeCode === typeCode && item.id !== currentEditId
            );

            if (existingItem) {
                alert('类型编码已存在，请使用其他编码');
                return;
            }

            // 构建资产类型数据
            const assetTypeItem = {
                id: currentEditId || generateAssetTypeId(),
                typeCode: formData.get('typeCode'),
                typeName: formData.get('typeName'),
                status: formData.get('status'),
                description: formData.get('description') || '',
                createTime: currentEditId ?
                    assetTypeData.find(item => item.id === currentEditId).createTime :
                    new Date().toLocaleString('zh-CN'),
                updateTime: new Date().toLocaleString('zh-CN')
            };

            if (currentEditId) {
                // 编辑模式
                const index = assetTypeData.findIndex(item => item.id === currentEditId);
                assetTypeData[index] = assetTypeItem;
                alert('资产类型更新成功');
            } else {
                // 新增模式
                assetTypeData.unshift(assetTypeItem);
                alert('资产类型添加成功');
            }

            // 关闭抽屉并刷新列表
            toggleAddAssetTypeDrawer();
            searchAssetType();
        }

        // 编辑资产类型
        function editAssetType(id) {
            const item = assetTypeData.find(item => item.id === id);
            if (!item) return;

            currentEditId = id;

            // 填充表单数据
            document.getElementById('typeCode').value = item.typeCode;
            document.getElementById('typeName').value = item.typeName;
            document.getElementById('status').value = item.status;
            document.getElementById('description').value = item.description;

            // 更新抽屉标题
            document.getElementById('drawerTitle').textContent = '编辑资产类型';

            // 打开抽屉
            toggleAddAssetTypeDrawer();
        }

        // 删除资产类型
        function deleteAssetType(id) {
            const item = assetTypeData.find(item => item.id === id);
            if (!item) return;

            if (confirm(`确定要删除资产类型"${item.typeName}"吗？\n删除后不可恢复。`)) {
                const index = assetTypeData.findIndex(item => item.id === id);
                assetTypeData.splice(index, 1);

                alert('资产类型删除成功');
                searchAssetType();
            }
        }

        // 绑定搜索事件
        function bindSearchEvents() {
            // 实时搜索
            document.getElementById('searchKeyword').addEventListener('input', debounce(searchAssetType, 300));
            document.getElementById('statusFilter').addEventListener('change', searchAssetType);
        }

        // 搜索资产类型
        function searchAssetType() {
            const keyword = document.getElementById('searchKeyword').value.toLowerCase();
            const status = document.getElementById('statusFilter').value;

            filteredData = assetTypeData.filter(item => {
                // 关键词搜索
                const matchKeyword = !keyword ||
                    item.typeName.toLowerCase().includes(keyword) ||
                    item.typeCode.toLowerCase().includes(keyword) ||
                    item.description.toLowerCase().includes(keyword);

                // 状态筛选
                const matchStatus = !status || item.status === status;

                return matchKeyword && matchStatus;
            });

            currentPage = 1;
            renderAssetTypeTable();
            renderPagination();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('statusFilter').value = '';

            filteredData = [...assetTypeData];
            currentPage = 1;
            renderAssetTypeTable();
            renderPagination();
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N 新增资产类型
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                toggleAddAssetTypeDrawer();
            }

            // Escape 关闭抽屉
            if (e.key === 'Escape') {
                const drawer = document.getElementById('addAssetTypeDrawer');
                if (!drawer.classList.contains('hidden')) {
                    toggleAddAssetTypeDrawer();
                }
            }
        });
    </script>
</body>
</html>
