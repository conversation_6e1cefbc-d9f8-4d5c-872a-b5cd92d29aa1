<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控 - 录像回放</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/common.css" rel="stylesheet">
    <link href="./css/video-monitor.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 返回首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="../index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 返回首页
                        </a>
                    </li>
                    
                    <!-- 视频监控菜单 -->
                    <li class="nav-item mt-4">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="ri-camera-line mr-2"></i>
                                <span>视频监控</span>
                            </div>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1 max-h-none">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="index.html" class="flex items-center w-full">
                                    <i class="ri-live-line mr-2"></i> 实时监控
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="playback.html" class="flex items-center w-full">
                                    <i class="ri-play-circle-line mr-2"></i> 录像回放
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ai-analysis.html" class="flex items-center w-full">
                                    <i class="ri-brain-line mr-2"></i> 智能分析
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alerts.html" class="flex items-center w-full">
                                    <i class="ri-alarm-warning-line mr-2"></i> 报警联动
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="storage.html" class="flex items-center w-full">
                                    <i class="ri-database-2-line mr-2"></i> 存储管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600 space-x-2">
                            <i class="ri-camera-line mr-2"></i>
                            <span>视频监控</span>
                            <i class="ri-arrow-right-s-line text-gray-400"></i>
                            <span class="text-blue-600 font-medium">录像回放</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 录像回放内容 -->
            <div class="p-6">
                <!-- 查询条件 -->
                <div class="bg-white rounded-xl shadow-sm mb-6">
                    <div class="p-6 border-b">
                        <h3 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="ri-search-line mr-2 text-blue-600"></i>
                            录像查询
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- 摄像头选择 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">摄像头</label>
                                <select id="cameraSelect" class="w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                    <option value="">全部摄像头</option>
                                    <option value="1">大门入口</option>
                                    <option value="2">停车场A区</option>
                                    <option value="3">电梯厅1F</option>
                                    <option value="4">办公区走廊</option>
                                </select>
                            </div>
                            
                            <!-- 开始时间 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
                                <input type="datetime-local" id="startTime" class="w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                            </div>
                            
                            <!-- 结束时间 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
                                <input type="datetime-local" id="endTime" class="w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                            </div>
                            
                            <!-- 事件类型 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">事件类型</label>
                                <select id="eventType" class="w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                    <option value="">全部事件</option>
                                    <option value="motion">移动检测</option>
                                    <option value="alarm">报警事件</option>
                                    <option value="manual">手动录制</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center space-x-4">
                                <button id="searchBtn" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                                    <i class="ri-search-line mr-2"></i>
                                    查询录像
                                </button>
                                <button id="resetBtn" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                                    <i class="ri-refresh-line mr-2"></i>
                                    重置条件
                                </button>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium text-gray-700">显示方式:</label>
                                <select id="viewMode" class="px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                    <option value="timeline">时间轴</option>
                                    <option value="list">列表</option>
                                    <option value="thumbnail">缩略图</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 录像播放区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 视频播放器 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b flex justify-between items-center">
                                <h4 class="font-semibold text-gray-800">录像播放</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-500">当前播放:</span>
                                    <span id="currentPlayback" class="text-sm font-medium text-blue-600">未选择录像</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <div id="videoPlayer" class="aspect-video bg-gray-900 rounded-lg flex items-center justify-center text-white">
                                    <div class="text-center">
                                        <i class="ri-play-circle-line text-6xl mb-4 opacity-50"></i>
                                        <div class="text-lg">请选择录像文件进行播放</div>
                                    </div>
                                </div>
                                
                                <!-- 播放控制栏 -->
                                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-3">
                                            <button id="playBtn" class="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                <i class="ri-play-line"></i>
                                            </button>
                                            <button id="pauseBtn" class="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                                <i class="ri-pause-line"></i>
                                            </button>
                                            <button id="stopBtn" class="p-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                                <i class="ri-stop-line"></i>
                                            </button>
                                        </div>
                                        
                                        <div class="flex items-center space-x-3">
                                            <span class="text-sm text-gray-600">播放速度:</span>
                                            <select id="playSpeed" class="px-2 py-1 border rounded text-sm">
                                                <option value="0.25">0.25x</option>
                                                <option value="0.5">0.5x</option>
                                                <option value="1" selected>1x</option>
                                                <option value="2">2x</option>
                                                <option value="4">4x</option>
                                                <option value="8">8x</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 进度条 -->
                                    <div class="flex items-center space-x-3">
                                        <span id="currentTime" class="text-sm text-gray-600">00:00:00</span>
                                        <div class="flex-1 bg-gray-300 rounded-full h-2 relative">
                                            <div id="progressBar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                                            <input type="range" id="progressSlider" class="absolute inset-0 w-full h-2 opacity-0 cursor-pointer" min="0" max="100" value="0">
                                        </div>
                                        <span id="totalTime" class="text-sm text-gray-600">00:00:00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 录像列表 -->
                    <div class="space-y-6">
                        <!-- 录像文件列表 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">录像文件</h4>
                            </div>
                            <div class="p-4">
                                <div id="recordingsList" class="space-y-2 max-h-96 overflow-y-auto">
                                    <!-- 录像列表将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 快速操作 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">快速操作</h4>
                            </div>
                            <div class="p-4 space-y-3">
                                <button class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                                    <i class="ri-download-line mr-2"></i>
                                    下载录像
                                </button>
                                <button class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center">
                                    <i class="ri-scissors-line mr-2"></i>
                                    剪辑录像
                                </button>
                                <button class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center">
                                    <i class="ri-share-line mr-2"></i>
                                    分享录像
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="../js/common.js"></script>
    <script src="./js/playback.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initPlayback();
        });
    </script>
</body>
</html>
