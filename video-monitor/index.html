<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控 - 实时监控</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/common.css" rel="stylesheet">
    <link href="./css/video-monitor.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 返回首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="../index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 返回首页
                        </a>
                    </li>
                    
                    <!-- 视频监控菜单 -->
                    <li class="nav-item mt-4">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="ri-camera-line mr-2"></i>
                                <span>视频监控</span>
                            </div>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1 max-h-none">
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="index.html" class="flex items-center w-full">
                                    <i class="ri-live-line mr-2"></i> 实时监控
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="playback.html" class="flex items-center w-full">
                                    <i class="ri-play-circle-line mr-2"></i> 录像回放
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ai-analysis.html" class="flex items-center w-full">
                                    <i class="ri-brain-line mr-2"></i> 智能分析
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alerts.html" class="flex items-center w-full">
                                    <i class="ri-alarm-warning-line mr-2"></i> 报警联动
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="storage.html" class="flex items-center w-full">
                                    <i class="ri-database-2-line mr-2"></i> 存储管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600 space-x-2">
                            <i class="ri-camera-line mr-2"></i>
                            <span>视频监控</span>
                            <i class="ri-arrow-right-s-line text-gray-400"></i>
                            <span class="text-blue-600 font-medium">实时监控</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时监控内容 -->
            <div class="p-6">
                <!-- 控制面板 -->
                <div class="bg-white rounded-xl shadow-sm mb-6">
                    <div class="p-6 border-b">
                        <h3 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="ri-live-line mr-2 text-blue-600"></i>
                            实时监控
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap items-center gap-4">
                            <!-- 显示模式切换 -->
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium text-gray-700">显示模式:</label>
                                <select id="displayMode" class="px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                    <option value="1">单画面</option>
                                    <option value="4" selected>四分屏</option>
                                    <option value="9">九分屏</option>
                                    <option value="16">十六分屏</option>
                                </select>
                            </div>
                            
                            <!-- 清晰度切换 -->
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium text-gray-700">清晰度:</label>
                                <select id="quality" class="px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                    <option value="hd">高清</option>
                                    <option value="sd" selected>标清</option>
                                    <option value="smooth">流畅</option>
                                </select>
                            </div>
                            
                            <!-- 视频墙模式 -->
                            <button id="videoWallBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                                <i class="ri-layout-grid-line mr-2"></i>
                                视频墙模式
                            </button>
                            
                            <!-- 全屏按钮 -->
                            <button id="fullscreenBtn" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                                <i class="ri-fullscreen-line mr-2"></i>
                                全屏
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 视频监控区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- 视频显示区域 -->
                    <div class="lg:col-span-3">
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">监控画面</h4>
                            </div>
                            <div class="p-4">
                                <div id="videoGrid" class="grid grid-cols-2 gap-2 aspect-video bg-gray-900 rounded-lg overflow-hidden">
                                    <!-- 视频画面将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 控制面板 -->
                    <div class="space-y-6">
                        <!-- 摄像头列表 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">摄像头列表</h4>
                            </div>
                            <div class="p-4">
                                <div id="cameraList" class="space-y-2 max-h-64 overflow-y-auto">
                                    <!-- 摄像头列表将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- PTZ控制 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">云台控制</h4>
                            </div>
                            <div class="p-4">
                                <div id="ptzControl" class="space-y-4">
                                    <!-- PTZ控制界面将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="../js/common.js"></script>
    <script src="./js/video-monitor.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initVideoMonitor();
        });
    </script>
</body>
</html>
