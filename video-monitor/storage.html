<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控 - 存储管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/common.css" rel="stylesheet">
    <link href="./css/video-monitor.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 返回首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="../index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 返回首页
                        </a>
                    </li>
                    
                    <!-- 视频监控菜单 -->
                    <li class="nav-item mt-4">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="ri-camera-line mr-2"></i>
                                <span>视频监控</span>
                            </div>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1 max-h-none">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="index.html" class="flex items-center w-full">
                                    <i class="ri-live-line mr-2"></i> 实时监控
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="playback.html" class="flex items-center w-full">
                                    <i class="ri-play-circle-line mr-2"></i> 录像回放
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ai-analysis.html" class="flex items-center w-full">
                                    <i class="ri-brain-line mr-2"></i> 智能分析
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alerts.html" class="flex items-center w-full">
                                    <i class="ri-alarm-warning-line mr-2"></i> 报警联动
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="storage.html" class="flex items-center w-full">
                                    <i class="ri-database-2-line mr-2"></i> 存储管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600 space-x-2">
                            <i class="ri-camera-line mr-2"></i>
                            <span>视频监控</span>
                            <i class="ri-arrow-right-s-line text-gray-400"></i>
                            <span class="text-blue-600 font-medium">存储管理</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 存储管理内容 -->
            <div class="p-6">
                <!-- 存储概览 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">总存储容量</p>
                                <p class="text-2xl font-bold text-blue-600" id="totalStorage">50TB</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="ri-database-2-line text-2xl text-blue-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">已使用</p>
                                <p class="text-2xl font-bold text-green-600" id="usedStorage">32TB</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="ri-hard-drive-2-line text-2xl text-green-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">可用空间</p>
                                <p class="text-2xl font-bold text-orange-600" id="freeStorage">18TB</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="ri-folder-open-line text-2xl text-orange-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">使用率</p>
                                <p class="text-2xl font-bold text-purple-600" id="usageRate">64%</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="ri-pie-chart-line text-2xl text-purple-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 存储设备和策略 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- 存储设备 -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b flex justify-between items-center">
                            <h3 class="text-xl font-bold text-gray-800 flex items-center">
                                <i class="ri-server-line mr-2 text-blue-600"></i>
                                存储设备
                            </h3>
                            <button id="addStorageBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                                <i class="ri-add-line mr-1"></i>
                                添加设备
                            </button>
                        </div>
                        <div class="p-6">
                            <div id="storageDevices" class="space-y-4">
                                <!-- 存储设备列表将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 存储策略 -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-xl font-bold text-gray-800 flex items-center">
                                <i class="ri-settings-3-line mr-2 text-green-600"></i>
                                存储策略
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <!-- 录像保存时长 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">录像保存时长</label>
                                    <select id="retentionPeriod" class="w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                        <option value="7">7天</option>
                                        <option value="15">15天</option>
                                        <option value="30" selected>30天</option>
                                        <option value="60">60天</option>
                                        <option value="90">90天</option>
                                        <option value="365">1年</option>
                                    </select>
                                </div>

                                <!-- 存储模式 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">存储模式</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="radio" name="storageMode" value="continuous" class="mr-2" checked>
                                            <span class="text-sm">连续录像</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="storageMode" value="event" class="mr-2">
                                            <span class="text-sm">事件录像</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="storageMode" value="schedule" class="mr-2">
                                            <span class="text-sm">定时录像</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- 循环覆盖 -->
                                <div class="flex items-center justify-between">
                                    <label class="text-sm font-medium text-gray-700">循环覆盖</label>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" id="cyclicOverwrite" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>

                                <!-- 自动备份 -->
                                <div class="flex items-center justify-between">
                                    <label class="text-sm font-medium text-gray-700">自动备份</label>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" id="autoBackup" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>

                                <button id="saveStrategyBtn" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                    保存策略
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 存储使用情况图表 -->
                <div class="bg-white rounded-xl shadow-sm mb-6">
                    <div class="p-6 border-b">
                        <h3 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="ri-bar-chart-line mr-2 text-purple-600"></i>
                            存储使用趋势
                        </h3>
                    </div>
                    <div class="p-6">
                        <div id="storageChart" class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div class="text-center text-gray-500">
                                <i class="ri-bar-chart-line text-4xl mb-2"></i>
                                <div>存储使用趋势图表</div>
                                <div class="text-sm">（此处可集成图表库显示实际数据）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备份管理 -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="ri-archive-line mr-2 text-orange-600"></i>
                            备份管理
                        </h3>
                        <div class="flex items-center space-x-2">
                            <button id="createBackupBtn" class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 text-sm">
                                <i class="ri-add-line mr-1"></i>
                                创建备份
                            </button>
                            <button id="restoreBackupBtn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm">
                                <i class="ri-refresh-line mr-1"></i>
                                恢复备份
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b">
                                        <th class="text-left py-3 px-4">备份名称</th>
                                        <th class="text-left py-3 px-4">备份时间</th>
                                        <th class="text-left py-3 px-4">备份大小</th>
                                        <th class="text-left py-3 px-4">备份类型</th>
                                        <th class="text-left py-3 px-4">状态</th>
                                        <th class="text-left py-3 px-4">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="backupTable">
                                    <!-- 备份记录将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="../js/common.js"></script>
    <script src="./js/storage.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initStorage();
        });
    </script>
</body>
</html>
