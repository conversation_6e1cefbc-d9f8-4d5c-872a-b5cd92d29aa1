// 存储管理功能
class StorageManager {
    constructor() {
        this.storageDevices = [];
        this.backupRecords = [];
        this.storageStats = {
            total: 50, // TB
            used: 32,  // TB
            free: 18,  // TB
            usageRate: 64 // %
        };
        this.init();
    }

    init() {
        this.initStorageDevices();
        this.initBackupRecords();
        this.initEventListeners();
        this.updateStorageStats();
        this.renderStorageDevices();
        this.renderBackupTable();
    }

    // 初始化存储设备数据
    initStorageDevices() {
        this.storageDevices = [
            {
                id: 1,
                name: '本地存储-主服务器',
                type: 'local',
                capacity: 20, // TB
                used: 15,     // TB
                status: 'online',
                health: 'good',
                location: '机房A-01'
            },
            {
                id: 2,
                name: '云存储-阿里云OSS',
                type: 'cloud',
                capacity: 20, // TB
                used: 12,     // TB
                status: 'online',
                health: 'good',
                location: '华东-杭州'
            },
            {
                id: 3,
                name: 'NVR存储设备-01',
                type: 'nvr',
                capacity: 10, // TB
                used: 5,      // TB
                status: 'online',
                health: 'warning',
                location: '监控室'
            }
        ];
    }

    // 初始化备份记录数据
    initBackupRecords() {
        const now = new Date();
        this.backupRecords = [
            {
                id: 1,
                name: '全量备份-20241201',
                time: new Date(now.getTime() - 24 * 60 * 60 * 1000),
                size: '2.5TB',
                type: 'full',
                status: 'completed',
                location: '云存储'
            },
            {
                id: 2,
                name: '增量备份-20241202',
                time: new Date(now.getTime() - 12 * 60 * 60 * 1000),
                size: '850GB',
                type: 'incremental',
                status: 'completed',
                location: '本地存储'
            },
            {
                id: 3,
                name: '差异备份-20241203',
                time: new Date(now.getTime() - 6 * 60 * 60 * 1000),
                size: '1.2TB',
                type: 'differential',
                status: 'in_progress',
                location: 'NVR存储'
            },
            {
                id: 4,
                name: '全量备份-20241130',
                time: new Date(now.getTime() - 48 * 60 * 60 * 1000),
                size: '2.3TB',
                type: 'full',
                status: 'completed',
                location: '云存储'
            },
            {
                id: 5,
                name: '增量备份-20241129',
                time: new Date(now.getTime() - 72 * 60 * 60 * 1000),
                size: '920GB',
                type: 'incremental',
                status: 'failed',
                location: '本地存储'
            }
        ];
    }

    // 初始化事件监听器
    initEventListeners() {
        // 添加存储设备
        document.getElementById('addStorageBtn').addEventListener('click', () => {
            this.addStorageDevice();
        });

        // 保存存储策略
        document.getElementById('saveStrategyBtn').addEventListener('click', () => {
            this.saveStorageStrategy();
        });

        // 创建备份
        document.getElementById('createBackupBtn').addEventListener('click', () => {
            this.createBackup();
        });

        // 恢复备份
        document.getElementById('restoreBackupBtn').addEventListener('click', () => {
            this.restoreBackup();
        });

        // 存储策略变化监听
        document.getElementById('retentionPeriod').addEventListener('change', () => {
            this.updateRetentionPeriod();
        });

        document.getElementById('cyclicOverwrite').addEventListener('change', () => {
            this.updateCyclicOverwrite();
        });

        document.getElementById('autoBackup').addEventListener('change', () => {
            this.updateAutoBackup();
        });
    }

    // 更新存储统计
    updateStorageStats() {
        document.getElementById('totalStorage').textContent = `${this.storageStats.total}TB`;
        document.getElementById('usedStorage').textContent = `${this.storageStats.used}TB`;
        document.getElementById('freeStorage').textContent = `${this.storageStats.free}TB`;
        document.getElementById('usageRate').textContent = `${this.storageStats.usageRate}%`;
    }

    // 渲染存储设备
    renderStorageDevices() {
        const container = document.getElementById('storageDevices');
        
        container.innerHTML = this.storageDevices.map(device => `
            <div class="border rounded-lg p-4 hover:border-blue-500 transition-colors">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 ${this.getDeviceTypeColor(device.type)} rounded-lg flex items-center justify-center">
                            <i class="${this.getDeviceTypeIcon(device.type)} text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800">${device.name}</h4>
                            <p class="text-sm text-gray-500">${device.location}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 text-xs rounded ${this.getStatusColor(device.status)}">
                            ${this.getStatusText(device.status)}
                        </span>
                        <span class="px-2 py-1 text-xs rounded ${this.getHealthColor(device.health)}">
                            ${this.getHealthText(device.health)}
                        </span>
                    </div>
                </div>
                
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">容量:</span>
                        <span class="font-medium">${device.used}TB / ${device.capacity}TB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: ${(device.used / device.capacity * 100)}%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500">
                        <span>使用率: ${Math.round(device.used / device.capacity * 100)}%</span>
                        <span>剩余: ${(device.capacity - device.used).toFixed(1)}TB</span>
                    </div>
                </div>
                
                <div class="mt-3 flex justify-end space-x-2">
                    <button onclick="storageManager.configDevice(${device.id})" 
                            class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                        配置
                    </button>
                    <button onclick="storageManager.testDevice(${device.id})" 
                            class="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700">
                        测试
                    </button>
                    <button onclick="storageManager.removeDevice(${device.id})" 
                            class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700">
                        移除
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 渲染备份表格
    renderBackupTable() {
        const tbody = document.getElementById('backupTable');
        
        tbody.innerHTML = this.backupRecords.map(backup => `
            <tr class="border-b hover:bg-gray-50">
                <td class="py-3 px-4">${backup.name}</td>
                <td class="py-3 px-4">${this.formatDateTime(backup.time)}</td>
                <td class="py-3 px-4">${backup.size}</td>
                <td class="py-3 px-4">
                    <span class="px-2 py-1 text-xs rounded ${this.getBackupTypeColor(backup.type)}">
                        ${this.getBackupTypeText(backup.type)}
                    </span>
                </td>
                <td class="py-3 px-4">
                    <span class="px-2 py-1 text-xs rounded ${this.getBackupStatusColor(backup.status)}">
                        ${this.getBackupStatusText(backup.status)}
                    </span>
                </td>
                <td class="py-3 px-4">
                    <button onclick="storageManager.downloadBackup(${backup.id})" 
                            class="text-blue-600 hover:text-blue-800 text-sm mr-2">下载</button>
                    <button onclick="storageManager.restoreFromBackup(${backup.id})" 
                            class="text-green-600 hover:text-green-800 text-sm mr-2">恢复</button>
                    <button onclick="storageManager.deleteBackup(${backup.id})" 
                            class="text-red-600 hover:text-red-800 text-sm">删除</button>
                </td>
            </tr>
        `).join('');
    }

    // 添加存储设备
    addStorageDevice() {
        this.showNotification('打开添加存储设备对话框', 'info');
        // 这里可以打开一个模态对话框来添加新的存储设备
    }

    // 配置设备
    configDevice(deviceId) {
        const device = this.storageDevices.find(d => d.id === deviceId);
        if (device) {
            this.showNotification(`配置设备: ${device.name}`, 'info');
        }
    }

    // 测试设备
    testDevice(deviceId) {
        const device = this.storageDevices.find(d => d.id === deviceId);
        if (device) {
            this.showNotification(`正在测试设备: ${device.name}`, 'info');
            setTimeout(() => {
                this.showNotification(`设备测试完成: ${device.name}`, 'success');
            }, 2000);
        }
    }

    // 移除设备
    removeDevice(deviceId) {
        const device = this.storageDevices.find(d => d.id === deviceId);
        if (device && confirm(`确定要移除设备 "${device.name}" 吗？`)) {
            this.storageDevices = this.storageDevices.filter(d => d.id !== deviceId);
            this.renderStorageDevices();
            this.showNotification(`设备已移除: ${device.name}`, 'success');
        }
    }

    // 保存存储策略
    saveStorageStrategy() {
        const retentionPeriod = document.getElementById('retentionPeriod').value;
        const storageMode = document.querySelector('input[name="storageMode"]:checked').value;
        const cyclicOverwrite = document.getElementById('cyclicOverwrite').checked;
        const autoBackup = document.getElementById('autoBackup').checked;

        console.log('存储策略:', {
            retentionPeriod,
            storageMode,
            cyclicOverwrite,
            autoBackup
        });

        this.showNotification('存储策略已保存', 'success');
    }

    // 更新保存时长
    updateRetentionPeriod() {
        const period = document.getElementById('retentionPeriod').value;
        this.showNotification(`录像保存时长已设置为 ${period} 天`, 'info');
    }

    // 更新循环覆盖
    updateCyclicOverwrite() {
        const enabled = document.getElementById('cyclicOverwrite').checked;
        this.showNotification(`循环覆盖已${enabled ? '启用' : '禁用'}`, 'info');
    }

    // 更新自动备份
    updateAutoBackup() {
        const enabled = document.getElementById('autoBackup').checked;
        this.showNotification(`自动备份已${enabled ? '启用' : '禁用'}`, 'info');
    }

    // 创建备份
    createBackup() {
        this.showNotification('正在创建备份...', 'info');
        
        // 模拟备份创建过程
        setTimeout(() => {
            const newBackup = {
                id: Date.now(),
                name: `手动备份-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`,
                time: new Date(),
                size: `${(Math.random() * 2 + 0.5).toFixed(1)}TB`,
                type: 'manual',
                status: 'completed',
                location: '本地存储'
            };
            
            this.backupRecords.unshift(newBackup);
            this.renderBackupTable();
            this.showNotification('备份创建成功', 'success');
        }, 3000);
    }

    // 恢复备份
    restoreBackup() {
        this.showNotification('请选择要恢复的备份文件', 'info');
    }

    // 从备份恢复
    restoreFromBackup(backupId) {
        const backup = this.backupRecords.find(b => b.id === backupId);
        if (backup && confirm(`确定要从备份 "${backup.name}" 恢复数据吗？`)) {
            this.showNotification(`正在从备份恢复: ${backup.name}`, 'info');
            setTimeout(() => {
                this.showNotification('数据恢复完成', 'success');
            }, 5000);
        }
    }

    // 下载备份
    downloadBackup(backupId) {
        const backup = this.backupRecords.find(b => b.id === backupId);
        if (backup) {
            this.showNotification(`开始下载备份: ${backup.name}`, 'info');
        }
    }

    // 删除备份
    deleteBackup(backupId) {
        const backup = this.backupRecords.find(b => b.id === backupId);
        if (backup && confirm(`确定要删除备份 "${backup.name}" 吗？`)) {
            this.backupRecords = this.backupRecords.filter(b => b.id !== backupId);
            this.renderBackupTable();
            this.showNotification(`备份已删除: ${backup.name}`, 'success');
        }
    }

    // 获取设备类型图标
    getDeviceTypeIcon(type) {
        const iconMap = {
            local: 'ri-hard-drive-2-line',
            cloud: 'ri-cloud-line',
            nvr: 'ri-server-line'
        };
        return iconMap[type] || 'ri-database-2-line';
    }

    // 获取设备类型颜色
    getDeviceTypeColor(type) {
        const colorMap = {
            local: 'bg-blue-600',
            cloud: 'bg-green-600',
            nvr: 'bg-purple-600'
        };
        return colorMap[type] || 'bg-gray-600';
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            online: '在线',
            offline: '离线',
            error: '错误'
        };
        return statusMap[status] || '未知';
    }

    // 获取状态颜色
    getStatusColor(status) {
        const colorMap = {
            online: 'bg-green-100 text-green-800',
            offline: 'bg-red-100 text-red-800',
            error: 'bg-red-100 text-red-800'
        };
        return colorMap[status] || 'bg-gray-100 text-gray-800';
    }

    // 获取健康状态文本
    getHealthText(health) {
        const healthMap = {
            good: '良好',
            warning: '警告',
            error: '错误'
        };
        return healthMap[health] || '未知';
    }

    // 获取健康状态颜色
    getHealthColor(health) {
        const colorMap = {
            good: 'bg-green-100 text-green-800',
            warning: 'bg-yellow-100 text-yellow-800',
            error: 'bg-red-100 text-red-800'
        };
        return colorMap[health] || 'bg-gray-100 text-gray-800';
    }

    // 获取备份类型文本
    getBackupTypeText(type) {
        const typeMap = {
            full: '全量备份',
            incremental: '增量备份',
            differential: '差异备份',
            manual: '手动备份'
        };
        return typeMap[type] || '未知';
    }

    // 获取备份类型颜色
    getBackupTypeColor(type) {
        const colorMap = {
            full: 'bg-blue-100 text-blue-800',
            incremental: 'bg-green-100 text-green-800',
            differential: 'bg-yellow-100 text-yellow-800',
            manual: 'bg-purple-100 text-purple-800'
        };
        return colorMap[type] || 'bg-gray-100 text-gray-800';
    }

    // 获取备份状态文本
    getBackupStatusText(status) {
        const statusMap = {
            completed: '已完成',
            in_progress: '进行中',
            failed: '失败',
            cancelled: '已取消'
        };
        return statusMap[status] || '未知';
    }

    // 获取备份状态颜色
    getBackupStatusColor(status) {
        const colorMap = {
            completed: 'bg-green-100 text-green-800',
            in_progress: 'bg-blue-100 text-blue-800',
            failed: 'bg-red-100 text-red-800',
            cancelled: 'bg-gray-100 text-gray-800'
        };
        return colorMap[status] || 'bg-gray-100 text-gray-800';
    }

    // 格式化日期时间
    formatDateTime(date) {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局实例
let storageManager;

// 初始化函数
function initStorage() {
    storageManager = new StorageManager();
}
