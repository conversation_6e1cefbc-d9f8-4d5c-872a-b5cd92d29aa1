// AI智能分析功能
class AIAnalysis {
    constructor() {
        this.currentAnalysisType = 'face';
        this.isAnalyzing = false;
        this.detectionResults = [];
        this.analysisHistory = [];
        this.detectionCount = 0;
        this.alertCount = 2;
        this.init();
    }

    init() {
        this.initAnalysisHistory();
        this.initEventListeners();
        this.renderAnalysisConfig();
        this.renderTodayStats();
        this.renderHistoryTable();
        this.startMockDetection();
    }

    // 初始化分析历史数据
    initAnalysisHistory() {
        const now = new Date();
        this.analysisHistory = [
            {
                id: 1,
                time: new Date(now.getTime() - 30 * 60 * 1000),
                camera: '大门入口',
                type: 'face',
                result: '识别到张三',
                confidence: 0.95,
                image: 'face1.jpg'
            },
            {
                id: 2,
                time: new Date(now.getTime() - 45 * 60 * 1000),
                camera: '停车场A区',
                type: 'vehicle',
                result: '车牌: 京A12345',
                confidence: 0.92,
                image: 'vehicle1.jpg'
            },
            {
                id: 3,
                time: new Date(now.getTime() - 60 * 60 * 1000),
                camera: '办公区走廊',
                type: 'behavior',
                result: '检测到奔跑行为',
                confidence: 0.88,
                image: 'behavior1.jpg'
            },
            {
                id: 4,
                time: new Date(now.getTime() - 90 * 60 * 1000),
                camera: '大门入口',
                type: 'intrusion',
                result: '区域入侵检测',
                confidence: 0.91,
                image: 'intrusion1.jpg'
            },
            {
                id: 5,
                time: new Date(now.getTime() - 120 * 60 * 1000),
                camera: '停车场A区',
                type: 'object',
                result: '检测到遗留物品',
                confidence: 0.87,
                image: 'object1.jpg'
            }
        ];
    }

    // 初始化事件监听器
    initEventListeners() {
        // 分析类型切换
        document.querySelectorAll('.analysis-type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchAnalysisType(e.target.closest('.analysis-type-btn').dataset.type);
            });
        });

        // 开始/停止分析
        document.getElementById('startAnalysisBtn').addEventListener('click', () => {
            this.toggleAnalysis();
        });

        // 摄像头切换
        document.getElementById('analysisCamera').addEventListener('change', () => {
            this.updateAnalysisCamera();
        });

        // 历史记录筛选
        document.getElementById('historyFilter').addEventListener('change', () => {
            this.filterHistory();
        });

        // 导出报告
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportReport();
        });
    }

    // 切换分析类型
    switchAnalysisType(type) {
        // 更新按钮状态
        document.querySelectorAll('.analysis-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-type="${type}"]`).classList.add('active');

        this.currentAnalysisType = type;
        this.renderAnalysisConfig();
        this.showNotification(`切换到${this.getAnalysisTypeName(type)}`, 'info');
    }

    // 渲染分析配置
    renderAnalysisConfig() {
        const configContainer = document.getElementById('analysisConfig');
        
        const configs = {
            face: `
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">识别模式</label>
                        <select class="w-full px-3 py-2 border rounded-lg text-sm">
                            <option>1:1比对</option>
                            <option>1:N识别</option>
                            <option>活体检测</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">置信度阈值</label>
                        <input type="range" class="w-full" min="0.5" max="1" step="0.05" value="0.8">
                        <div class="text-xs text-gray-500 mt-1">当前: 80%</div>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="faceAlert" class="mr-2" checked>
                        <label for="faceAlert" class="text-sm">陌生人报警</label>
                    </div>
                </div>
            `,
            behavior: `
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">检测行为</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 奔跑检测
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 徘徊检测
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 聚集检测
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 摔倒检测
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">敏感度</label>
                        <input type="range" class="w-full" min="1" max="10" value="7">
                        <div class="text-xs text-gray-500 mt-1">当前: 7/10</div>
                    </div>
                </div>
            `,
            vehicle: `
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">识别内容</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 车牌识别
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 车型识别
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 车身颜色
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 违停检测
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">车牌类型</label>
                        <select class="w-full px-3 py-2 border rounded-lg text-sm">
                            <option>普通车牌</option>
                            <option>新能源车牌</option>
                            <option>全部类型</option>
                        </select>
                    </div>
                </div>
            `,
            object: `
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">检测对象</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 遗留物检测
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 物品丢失
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 危险物品
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 垃圾检测
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">检测区域</label>
                        <button class="w-full px-3 py-2 border border-dashed rounded-lg text-sm text-gray-500 hover:border-blue-500">
                            点击设置检测区域
                        </button>
                    </div>
                </div>
            `,
            intrusion: `
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">检测类型</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 越界检测
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked> 区域入侵
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 围界检测
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> 禁入区域
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">警戒线设置</label>
                        <button class="w-full px-3 py-2 border border-dashed rounded-lg text-sm text-gray-500 hover:border-blue-500">
                            点击绘制警戒线
                        </button>
                    </div>
                </div>
            `
        };

        configContainer.innerHTML = configs[this.currentAnalysisType] || '';
    }

    // 切换分析状态
    toggleAnalysis() {
        const btn = document.getElementById('startAnalysisBtn');
        
        if (this.isAnalyzing) {
            this.stopAnalysis();
            btn.textContent = '开始分析';
            btn.className = 'px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700';
        } else {
            this.startAnalysis();
            btn.textContent = '停止分析';
            btn.className = 'px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700';
        }
    }

    // 开始分析
    startAnalysis() {
        this.isAnalyzing = true;
        this.updateAnalysisVideo();
        this.showNotification('开始AI智能分析', 'success');
    }

    // 停止分析
    stopAnalysis() {
        this.isAnalyzing = false;
        this.showNotification('停止AI智能分析', 'info');
    }

    // 更新分析视频
    updateAnalysisVideo() {
        const videoContainer = document.getElementById('analysisVideo');
        const cameraSelect = document.getElementById('analysisCamera');
        const cameraName = cameraSelect.options[cameraSelect.selectedIndex].text;
        
        if (this.isAnalyzing) {
            videoContainer.innerHTML = `
                <div class="w-full h-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center text-white relative">
                    <div class="text-center">
                        <i class="ri-brain-line text-6xl mb-4"></i>
                        <div class="text-lg">正在分析: ${cameraName}</div>
                        <div class="text-sm opacity-75">${this.getAnalysisTypeName(this.currentAnalysisType)}</div>
                    </div>
                    <div class="absolute top-4 right-4 bg-green-500 px-2 py-1 rounded text-xs">
                        <i class="ri-record-circle-fill mr-1"></i>分析中
                    </div>
                </div>
                <div id="analysisOverlay" class="absolute inset-0 pointer-events-none">
                    <!-- 检测框将通过JavaScript动态添加 -->
                </div>
            `;
        }
    }

    // 开始模拟检测
    startMockDetection() {
        setInterval(() => {
            if (this.isAnalyzing) {
                this.simulateDetection();
            }
        }, 3000);
    }

    // 模拟检测结果
    simulateDetection() {
        const detectionTypes = {
            face: ['张三', '李四', '陌生人A', '陌生人B'],
            behavior: ['奔跑行为', '徘徊行为', '聚集行为', '异常行为'],
            vehicle: ['京A12345', '京B67890', '沪C11111', '粤D22222'],
            object: ['遗留包裹', '遗留手机', '危险物品', '垃圾堆积'],
            intrusion: ['区域入侵', '越界行为', '围界报警', '禁入检测']
        };

        const results = detectionTypes[this.currentAnalysisType];
        const result = results[Math.floor(Math.random() * results.length)];
        const confidence = (0.8 + Math.random() * 0.2).toFixed(2);

        this.detectionCount++;
        document.getElementById('detectionCount').textContent = this.detectionCount;

        // 添加检测结果
        this.addDetectionResult({
            time: new Date(),
            type: this.currentAnalysisType,
            result: result,
            confidence: parseFloat(confidence)
        });

        // 更新处理时间
        const processingTime = Math.floor(80 + Math.random() * 80);
        document.getElementById('processingTime').textContent = `${processingTime}ms`;

        // 模拟检测框
        this.showDetectionBox();
    }

    // 显示检测框
    showDetectionBox() {
        const overlay = document.getElementById('analysisOverlay');
        if (!overlay) return;

        const box = document.createElement('div');
        box.className = 'absolute border-2 border-red-500 bg-red-500 bg-opacity-20';
        
        // 随机位置和大小
        const x = Math.random() * 60 + 10; // 10-70%
        const y = Math.random() * 60 + 10; // 10-70%
        const w = Math.random() * 20 + 10; // 10-30%
        const h = Math.random() * 20 + 10; // 10-30%
        
        box.style.left = `${x}%`;
        box.style.top = `${y}%`;
        box.style.width = `${w}%`;
        box.style.height = `${h}%`;
        
        overlay.appendChild(box);
        
        // 3秒后移除
        setTimeout(() => {
            if (box.parentNode) {
                box.parentNode.removeChild(box);
            }
        }, 3000);
    }

    // 添加检测结果
    addDetectionResult(result) {
        this.detectionResults.unshift(result);
        if (this.detectionResults.length > 10) {
            this.detectionResults.pop();
        }
        this.renderDetectionResults();
    }

    // 渲染检测结果
    renderDetectionResults() {
        const container = document.getElementById('detectionResults');
        
        if (this.detectionResults.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-gray-500">
                    <i class="ri-search-line text-2xl mb-2"></i>
                    <div class="text-sm">暂无检测结果</div>
                </div>
            `;
            return;
        }

        container.innerHTML = this.detectionResults.map(result => `
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div class="flex-1">
                    <div class="text-sm font-medium">${result.result}</div>
                    <div class="text-xs text-gray-500">${this.formatTime(result.time)}</div>
                </div>
                <div class="text-xs text-gray-600">
                    ${(result.confidence * 100).toFixed(0)}%
                </div>
            </div>
        `).join('');
    }

    // 渲染今日统计
    renderTodayStats() {
        const container = document.getElementById('todayStats');
        const stats = {
            face: { count: 156, alerts: 3 },
            behavior: { count: 23, alerts: 5 },
            vehicle: { count: 89, alerts: 1 },
            object: { count: 12, alerts: 2 },
            intrusion: { count: 7, alerts: 4 }
        };

        container.innerHTML = Object.entries(stats).map(([type, data]) => `
            <div class="flex items-center justify-between">
                <span class="text-sm">${this.getAnalysisTypeName(type)}</span>
                <div class="text-sm">
                    <span class="text-blue-600">${data.count}</span> / 
                    <span class="text-red-600">${data.alerts}</span>
                </div>
            </div>
        `).join('');
    }

    // 渲染历史记录表格
    renderHistoryTable() {
        const tbody = document.getElementById('historyTable');
        
        tbody.innerHTML = this.analysisHistory.map(record => `
            <tr class="border-b hover:bg-gray-50">
                <td class="py-3 px-4">${this.formatDateTime(record.time)}</td>
                <td class="py-3 px-4">${record.camera}</td>
                <td class="py-3 px-4">${this.getAnalysisTypeName(record.type)}</td>
                <td class="py-3 px-4">${record.result}</td>
                <td class="py-3 px-4">${(record.confidence * 100).toFixed(0)}%</td>
                <td class="py-3 px-4">
                    <button class="text-blue-600 hover:text-blue-800 text-sm mr-2">查看</button>
                    <button class="text-green-600 hover:text-green-800 text-sm">下载</button>
                </td>
            </tr>
        `).join('');
    }

    // 筛选历史记录
    filterHistory() {
        const filter = document.getElementById('historyFilter').value;
        console.log(`筛选历史记录: ${filter}`);
        this.showNotification(`已切换到${filter === 'today' ? '今天' : filter === 'week' ? '本周' : '本月'}的记录`, 'info');
    }

    // 导出报告
    exportReport() {
        this.showNotification('正在生成分析报告...', 'info');
        setTimeout(() => {
            this.showNotification('报告导出成功', 'success');
        }, 2000);
    }

    // 获取分析类型名称
    getAnalysisTypeName(type) {
        const names = {
            face: '人脸识别',
            behavior: '行为分析',
            vehicle: '车辆识别',
            object: '物体识别',
            intrusion: '入侵检测'
        };
        return names[type] || '未知';
    }

    // 格式化时间
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化日期时间
    formatDateTime(date) {
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局实例
let aiAnalysis;

// 初始化函数
function initAIAnalysis() {
    aiAnalysis = new AIAnalysis();
}
