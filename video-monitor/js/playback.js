// 录像回放功能
class VideoPlayback {
    constructor() {
        this.recordings = [];
        this.currentRecording = null;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        this.playSpeed = 1;
        this.viewMode = 'timeline';
        this.init();
    }

    init() {
        this.initRecordings();
        this.initEventListeners();
        this.setDefaultDateTime();
        this.renderRecordingsList();
    }

    // 初始化录像数据
    initRecordings() {
        const now = new Date();
        this.recordings = [
            {
                id: 1,
                cameraId: 1,
                cameraName: '大门入口',
                startTime: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2小时前
                endTime: new Date(now.getTime() - 1 * 60 * 60 * 1000), // 1小时前
                duration: 3600, // 1小时
                size: '2.1 GB',
                type: 'motion',
                thumbnail: 'https://via.placeholder.com/160x90/3b82f6/ffffff?text=录像1'
            },
            {
                id: 2,
                cameraId: 2,
                cameraName: '停车场A区',
                startTime: new Date(now.getTime() - 4 * 60 * 60 * 1000), // 4小时前
                endTime: new Date(now.getTime() - 3 * 60 * 60 * 1000), // 3小时前
                duration: 3600,
                size: '1.8 GB',
                type: 'alarm',
                thumbnail: 'https://via.placeholder.com/160x90/10b981/ffffff?text=录像2'
            },
            {
                id: 3,
                cameraId: 1,
                cameraName: '大门入口',
                startTime: new Date(now.getTime() - 6 * 60 * 60 * 1000), // 6小时前
                endTime: new Date(now.getTime() - 5 * 60 * 60 * 1000), // 5小时前
                duration: 3600,
                size: '2.3 GB',
                type: 'manual',
                thumbnail: 'https://via.placeholder.com/160x90/f59e0b/ffffff?text=录像3'
            },
            {
                id: 4,
                cameraId: 4,
                cameraName: '办公区走廊',
                startTime: new Date(now.getTime() - 8 * 60 * 60 * 1000), // 8小时前
                endTime: new Date(now.getTime() - 7 * 60 * 60 * 1000), // 7小时前
                duration: 3600,
                size: '1.9 GB',
                type: 'motion',
                thumbnail: 'https://via.placeholder.com/160x90/ef4444/ffffff?text=录像4'
            },
            {
                id: 5,
                cameraId: 3,
                cameraName: '电梯厅1F',
                startTime: new Date(now.getTime() - 12 * 60 * 60 * 1000), // 12小时前
                endTime: new Date(now.getTime() - 11 * 60 * 60 * 1000), // 11小时前
                duration: 3600,
                size: '2.0 GB',
                type: 'alarm',
                thumbnail: 'https://via.placeholder.com/160x90/8b5cf6/ffffff?text=录像5'
            }
        ];
    }

    // 初始化事件监听器
    initEventListeners() {
        // 查询按钮
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.searchRecordings();
        });

        // 重置按钮
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetSearchConditions();
        });

        // 显示方式切换
        document.getElementById('viewMode').addEventListener('change', (e) => {
            this.viewMode = e.target.value;
            this.renderRecordingsList();
        });

        // 播放控制
        document.getElementById('playBtn').addEventListener('click', () => {
            this.play();
        });

        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.pause();
        });

        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stop();
        });

        // 播放速度
        document.getElementById('playSpeed').addEventListener('change', (e) => {
            this.playSpeed = parseFloat(e.target.value);
            this.updatePlaySpeed();
        });

        // 进度条
        document.getElementById('progressSlider').addEventListener('input', (e) => {
            this.seekTo(parseFloat(e.target.value));
        });
    }

    // 设置默认日期时间
    setDefaultDateTime() {
        const now = new Date();
        const today = now.toISOString().slice(0, 16);
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString().slice(0, 16);
        
        document.getElementById('startTime').value = yesterday;
        document.getElementById('endTime').value = today;
    }

    // 搜索录像
    searchRecordings() {
        const cameraId = document.getElementById('cameraSelect').value;
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        const eventType = document.getElementById('eventType').value;

        console.log('搜索条件:', { cameraId, startTime, endTime, eventType });

        // 模拟搜索过程
        this.showLoading();
        
        setTimeout(() => {
            // 根据条件过滤录像
            let filteredRecordings = this.recordings;
            
            if (cameraId) {
                filteredRecordings = filteredRecordings.filter(r => r.cameraId.toString() === cameraId);
            }
            
            if (eventType) {
                filteredRecordings = filteredRecordings.filter(r => r.type === eventType);
            }

            this.recordings = filteredRecordings;
            this.renderRecordingsList();
            this.hideLoading();
            
            this.showNotification(`找到 ${filteredRecordings.length} 个录像文件`, 'success');
        }, 1000);
    }

    // 重置搜索条件
    resetSearchConditions() {
        document.getElementById('cameraSelect').value = '';
        document.getElementById('eventType').value = '';
        this.setDefaultDateTime();
        this.initRecordings();
        this.renderRecordingsList();
        this.showNotification('搜索条件已重置', 'info');
    }

    // 渲染录像列表
    renderRecordingsList() {
        const recordingsList = document.getElementById('recordingsList');
        
        if (this.recordings.length === 0) {
            recordingsList.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="ri-file-list-line text-4xl mb-2"></i>
                    <div>暂无录像文件</div>
                </div>
            `;
            return;
        }

        recordingsList.innerHTML = this.recordings.map(recording => {
            const typeText = this.getEventTypeText(recording.type);
            const typeColor = this.getEventTypeColor(recording.type);
            
            if (this.viewMode === 'thumbnail') {
                return `
                    <div class="recording-item border rounded-lg p-3 cursor-pointer hover:border-blue-500 transition-colors" 
                         onclick="playback.playRecording(${recording.id})">
                        <img src="${recording.thumbnail}" alt="录像缩略图" class="w-full h-20 object-cover rounded mb-2">
                        <div class="text-sm font-medium">${recording.cameraName}</div>
                        <div class="text-xs text-gray-500">${this.formatDateTime(recording.startTime)}</div>
                        <div class="text-xs text-gray-500">${this.formatDuration(recording.duration)}</div>
                        <span class="inline-block px-2 py-1 text-xs rounded ${typeColor} mt-1">${typeText}</span>
                    </div>
                `;
            } else {
                return `
                    <div class="recording-item border rounded-lg p-3 cursor-pointer hover:border-blue-500 transition-colors" 
                         onclick="playback.playRecording(${recording.id})">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="font-medium">${recording.cameraName}</div>
                                <div class="text-sm text-gray-500">
                                    ${this.formatDateTime(recording.startTime)} - ${this.formatDateTime(recording.endTime)}
                                </div>
                                <div class="text-xs text-gray-400">
                                    时长: ${this.formatDuration(recording.duration)} | 大小: ${recording.size}
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 text-xs rounded ${typeColor}">${typeText}</span>
                                <button onclick="event.stopPropagation(); playback.downloadRecording(${recording.id})" 
                                        class="p-1 text-gray-400 hover:text-blue-600">
                                    <i class="ri-download-line"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }).join('');
    }

    // 播放录像
    playRecording(recordingId) {
        const recording = this.recordings.find(r => r.id === recordingId);
        if (!recording) return;

        this.currentRecording = recording;
        this.duration = recording.duration;
        this.currentTime = 0;

        // 更新播放器显示
        const videoPlayer = document.getElementById('videoPlayer');
        videoPlayer.innerHTML = `
            <div class="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white">
                <div class="text-center">
                    <i class="ri-play-circle-line text-6xl mb-4"></i>
                    <div class="text-lg">正在播放: ${recording.cameraName}</div>
                    <div class="text-sm opacity-75">${this.formatDateTime(recording.startTime)}</div>
                </div>
            </div>
        `;

        // 更新当前播放信息
        document.getElementById('currentPlayback').textContent = 
            `${recording.cameraName} - ${this.formatDateTime(recording.startTime)}`;

        // 更新时间显示
        this.updateTimeDisplay();

        this.showNotification(`开始播放: ${recording.cameraName}`, 'success');
    }

    // 播放控制
    play() {
        if (!this.currentRecording) {
            this.showNotification('请先选择录像文件', 'warning');
            return;
        }

        this.isPlaying = true;
        this.startPlaybackTimer();
        this.showNotification('开始播放', 'info');
    }

    pause() {
        this.isPlaying = false;
        this.stopPlaybackTimer();
        this.showNotification('暂停播放', 'info');
    }

    stop() {
        this.isPlaying = false;
        this.currentTime = 0;
        this.stopPlaybackTimer();
        this.updateTimeDisplay();
        this.updateProgressBar();
        this.showNotification('停止播放', 'info');
    }

    // 跳转到指定位置
    seekTo(percentage) {
        if (!this.currentRecording) return;
        
        this.currentTime = (percentage / 100) * this.duration;
        this.updateTimeDisplay();
        this.updateProgressBar();
    }

    // 更新播放速度
    updatePlaySpeed() {
        this.showNotification(`播放速度: ${this.playSpeed}x`, 'info');
    }

    // 开始播放计时器
    startPlaybackTimer() {
        this.stopPlaybackTimer();
        this.playbackTimer = setInterval(() => {
            if (this.isPlaying && this.currentTime < this.duration) {
                this.currentTime += this.playSpeed;
                this.updateTimeDisplay();
                this.updateProgressBar();
            } else if (this.currentTime >= this.duration) {
                this.stop();
                this.showNotification('播放完成', 'success');
            }
        }, 1000);
    }

    // 停止播放计时器
    stopPlaybackTimer() {
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
            this.playbackTimer = null;
        }
    }

    // 更新时间显示
    updateTimeDisplay() {
        document.getElementById('currentTime').textContent = this.formatTime(this.currentTime);
        document.getElementById('totalTime').textContent = this.formatTime(this.duration);
    }

    // 更新进度条
    updateProgressBar() {
        const percentage = this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0;
        document.getElementById('progressBar').style.width = `${percentage}%`;
        document.getElementById('progressSlider').value = percentage;
    }

    // 下载录像
    downloadRecording(recordingId) {
        const recording = this.recordings.find(r => r.id === recordingId);
        if (!recording) return;

        this.showNotification(`开始下载: ${recording.cameraName}`, 'info');
        
        // 模拟下载过程
        setTimeout(() => {
            this.showNotification(`下载完成: ${recording.cameraName}`, 'success');
        }, 2000);
    }

    // 获取事件类型文本
    getEventTypeText(type) {
        const typeMap = {
            'motion': '移动检测',
            'alarm': '报警事件',
            'manual': '手动录制'
        };
        return typeMap[type] || '未知';
    }

    // 获取事件类型颜色
    getEventTypeColor(type) {
        const colorMap = {
            'motion': 'bg-blue-100 text-blue-800',
            'alarm': 'bg-red-100 text-red-800',
            'manual': 'bg-green-100 text-green-800'
        };
        return colorMap[type] || 'bg-gray-100 text-gray-800';
    }

    // 格式化日期时间
    formatDateTime(date) {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化时长
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // 格式化时间
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // 显示加载状态
    showLoading() {
        const recordingsList = document.getElementById('recordingsList');
        recordingsList.innerHTML = `
            <div class="text-center py-8">
                <div class="loading-spinner mx-auto mb-2"></div>
                <div class="text-gray-500">正在搜索录像...</div>
            </div>
        `;
    }

    // 隐藏加载状态
    hideLoading() {
        // 加载状态会在renderRecordingsList中被替换
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局实例
let playback;

// 初始化函数
function initPlayback() {
    playback = new VideoPlayback();
}
