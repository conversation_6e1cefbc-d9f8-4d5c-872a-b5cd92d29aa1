// 报警联动功能
class AlertSystem {
    constructor() {
        this.alerts = [];
        this.alertHistory = [];
        this.autoRefresh = true;
        this.refreshInterval = null;
        this.todayStats = {
            total: 12,
            pending: 3,
            resolved: 9,
            linkage: 8
        };
        this.init();
    }

    init() {
        this.initAlertHistory();
        this.initEventListeners();
        this.updateStats();
        this.renderAlertHistory();
        this.startAutoRefresh();
        this.simulateRealTimeAlerts();
    }

    // 初始化报警历史数据
    initAlertHistory() {
        const now = new Date();
        this.alertHistory = [
            {
                id: 1,
                time: new Date(now.getTime() - 15 * 60 * 1000),
                type: 'intrusion',
                camera: '大门入口',
                content: '检测到区域入侵',
                status: 'pending',
                linkage: ['录像', '门禁锁定', '短信通知'],
                severity: 'high'
            },
            {
                id: 2,
                time: new Date(now.getTime() - 30 * 60 * 1000),
                type: 'face',
                camera: '办公区走廊',
                content: '检测到黑名单人员',
                status: 'processing',
                linkage: ['录像', 'App推送'],
                severity: 'high'
            },
            {
                id: 3,
                time: new Date(now.getTime() - 45 * 60 * 1000),
                type: 'behavior',
                camera: '停车场A区',
                content: '检测到异常行为',
                status: 'resolved',
                linkage: ['录像', '截图'],
                severity: 'medium'
            },
            {
                id: 4,
                time: new Date(now.getTime() - 60 * 60 * 1000),
                type: 'video',
                camera: '电梯厅1F',
                content: '视频信号丢失',
                status: 'resolved',
                linkage: ['短信通知', '邮件通知'],
                severity: 'low'
            },
            {
                id: 5,
                time: new Date(now.getTime() - 90 * 60 * 1000),
                type: 'intrusion',
                camera: '楼顶天台',
                content: '越界检测报警',
                status: 'resolved',
                linkage: ['录像', '门禁锁定', 'Web弹窗'],
                severity: 'high'
            }
        ];
    }

    // 初始化事件监听器
    initEventListeners() {
        // 自动刷新开关
        document.getElementById('autoRefresh').addEventListener('change', (e) => {
            this.autoRefresh = e.target.checked;
            if (this.autoRefresh) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });

        // 筛选器
        document.getElementById('alertFilter').addEventListener('change', () => {
            this.filterAlerts();
        });

        document.getElementById('statusFilter').addEventListener('change', () => {
            this.filterAlerts();
        });

        // 导出按钮
        document.getElementById('exportAlertsBtn').addEventListener('click', () => {
            this.exportAlerts();
        });

        // 报警弹窗按钮
        document.getElementById('ignoreAlertBtn').addEventListener('click', () => {
            this.ignoreAlert();
        });

        document.getElementById('handleAlertBtn').addEventListener('click', () => {
            this.handleAlert();
        });

        document.getElementById('closeAlertBtn').addEventListener('click', () => {
            this.closeAlertModal();
        });
    }

    // 更新统计数据
    updateStats() {
        document.getElementById('todayAlerts').textContent = this.todayStats.total;
        document.getElementById('pendingAlerts').textContent = this.todayStats.pending;
        document.getElementById('resolvedAlerts').textContent = this.todayStats.resolved;
        document.getElementById('linkageCount').textContent = this.todayStats.linkage;
    }

    // 开始自动刷新
    startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshInterval = setInterval(() => {
            this.refreshRealTimeAlerts();
        }, 5000);
    }

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // 刷新实时报警
    refreshRealTimeAlerts() {
        this.renderRealTimeAlerts();
    }

    // 渲染实时报警
    renderRealTimeAlerts() {
        const container = document.getElementById('realTimeAlerts');
        
        if (this.alerts.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="ri-shield-check-line text-4xl mb-2"></i>
                    <div class="text-sm">暂无实时报警</div>
                </div>
            `;
            return;
        }

        container.innerHTML = this.alerts.map(alert => `
            <div class="alert-item border-l-4 ${this.getAlertBorderColor(alert.severity)} bg-gray-50 p-3 rounded-r-lg cursor-pointer hover:bg-gray-100" 
                 onclick="alertSystem.showAlertDetail(${alert.id})">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-800">${alert.content}</span>
                            <span class="px-2 py-1 text-xs rounded ${this.getSeverityColor(alert.severity)}">
                                ${this.getSeverityText(alert.severity)}
                            </span>
                        </div>
                        <div class="text-sm text-gray-600 mt-1">
                            ${alert.camera} • ${this.formatTime(alert.time)}
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="ri-alarm-warning-line text-red-500"></i>
                        <span class="text-xs text-gray-500">${alert.linkage.length}个联动</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // 渲染报警历史
    renderAlertHistory() {
        const tbody = document.getElementById('alertHistoryTable');
        
        tbody.innerHTML = this.alertHistory.map(alert => `
            <tr class="border-b hover:bg-gray-50">
                <td class="py-3 px-4">${this.formatDateTime(alert.time)}</td>
                <td class="py-3 px-4">
                    <span class="px-2 py-1 text-xs rounded ${this.getTypeColor(alert.type)}">
                        ${this.getTypeText(alert.type)}
                    </span>
                </td>
                <td class="py-3 px-4">${alert.camera}</td>
                <td class="py-3 px-4">${alert.content}</td>
                <td class="py-3 px-4">
                    <span class="px-2 py-1 text-xs rounded ${this.getStatusColor(alert.status)}">
                        ${this.getStatusText(alert.status)}
                    </span>
                </td>
                <td class="py-3 px-4">
                    <div class="flex flex-wrap gap-1">
                        ${alert.linkage.map(action => `
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">${action}</span>
                        `).join('')}
                    </div>
                </td>
                <td class="py-3 px-4">
                    <button onclick="alertSystem.viewAlertDetail(${alert.id})" 
                            class="text-blue-600 hover:text-blue-800 text-sm mr-2">查看</button>
                    <button onclick="alertSystem.replayAlert(${alert.id})" 
                            class="text-green-600 hover:text-green-800 text-sm">回放</button>
                </td>
            </tr>
        `).join('');
    }

    // 模拟实时报警
    simulateRealTimeAlerts() {
        // 添加一些初始报警
        this.alerts = [
            {
                id: Date.now(),
                time: new Date(),
                type: 'intrusion',
                camera: '大门入口',
                content: '检测到区域入侵',
                severity: 'high',
                linkage: ['录像', '门禁锁定', '短信通知']
            }
        ];

        // 定期生成新报警
        setInterval(() => {
            if (Math.random() < 0.3) { // 30%概率生成新报警
                this.generateRandomAlert();
            }
        }, 10000);

        this.renderRealTimeAlerts();
    }

    // 生成随机报警
    generateRandomAlert() {
        const types = ['intrusion', 'face', 'behavior', 'video'];
        const cameras = ['大门入口', '停车场A区', '办公区走廊', '电梯厅1F', '楼顶天台'];
        const contents = {
            intrusion: ['区域入侵检测', '越界报警', '围界检测'],
            face: ['检测到黑名单人员', '陌生人报警', '人脸识别失败'],
            behavior: ['检测到异常行为', '奔跑检测', '徘徊报警'],
            video: ['视频信号丢失', '画面遮挡', '设备离线']
        };

        const type = types[Math.floor(Math.random() * types.length)];
        const camera = cameras[Math.floor(Math.random() * cameras.length)];
        const content = contents[type][Math.floor(Math.random() * contents[type].length)];

        const newAlert = {
            id: Date.now(),
            time: new Date(),
            type: type,
            camera: camera,
            content: content,
            severity: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
            linkage: ['录像', '短信通知']
        };

        this.alerts.unshift(newAlert);
        if (this.alerts.length > 5) {
            this.alerts.pop();
        }

        this.todayStats.total++;
        this.todayStats.pending++;
        this.updateStats();
        this.renderRealTimeAlerts();

        // 显示报警弹窗
        if (newAlert.severity === 'high') {
            this.showAlertModal(newAlert);
        }

        this.showNotification(`新报警: ${newAlert.content}`, 'warning');
    }

    // 显示报警详情
    showAlertDetail(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            this.showAlertModal(alert);
        }
    }

    // 显示报警弹窗
    showAlertModal(alert) {
        const modal = document.getElementById('alertModal');
        const content = document.getElementById('alertModalContent');
        
        content.innerHTML = `
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">报警时间</label>
                        <div class="text-sm text-gray-900">${this.formatDateTime(alert.time)}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">报警类型</label>
                        <div class="text-sm text-gray-900">${this.getTypeText(alert.type)}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">摄像头</label>
                        <div class="text-sm text-gray-900">${alert.camera}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">严重程度</label>
                        <span class="px-2 py-1 text-xs rounded ${this.getSeverityColor(alert.severity)}">
                            ${this.getSeverityText(alert.severity)}
                        </span>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">报警内容</label>
                    <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded">${alert.content}</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">联动动作</label>
                    <div class="flex flex-wrap gap-2">
                        ${alert.linkage.map(action => `
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">${action}</span>
                        `).join('')}
                    </div>
                </div>
                <div class="bg-yellow-50 p-3 rounded">
                    <div class="text-sm text-yellow-800">
                        <i class="ri-information-line mr-1"></i>
                        请及时处理此报警，避免安全风险。
                    </div>
                </div>
            </div>
        `;
        
        modal.classList.remove('hidden');
    }

    // 关闭报警弹窗
    closeAlertModal() {
        document.getElementById('alertModal').classList.add('hidden');
    }

    // 忽略报警
    ignoreAlert() {
        this.showNotification('报警已忽略', 'info');
        this.closeAlertModal();
    }

    // 处理报警
    handleAlert() {
        this.todayStats.pending--;
        this.todayStats.resolved++;
        this.updateStats();
        this.showNotification('报警已标记为处理', 'success');
        this.closeAlertModal();
    }

    // 查看报警详情
    viewAlertDetail(alertId) {
        const alert = this.alertHistory.find(a => a.id === alertId);
        if (alert) {
            this.showAlertModal(alert);
        }
    }

    // 回放报警
    replayAlert(alertId) {
        this.showNotification('正在加载报警回放...', 'info');
        setTimeout(() => {
            this.showNotification('回放加载完成', 'success');
        }, 2000);
    }

    // 筛选报警
    filterAlerts() {
        const typeFilter = document.getElementById('alertFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        
        console.log(`筛选条件: 类型=${typeFilter}, 状态=${statusFilter}`);
        this.showNotification('筛选条件已应用', 'info');
    }

    // 导出报警记录
    exportAlerts() {
        this.showNotification('正在导出报警记录...', 'info');
        setTimeout(() => {
            this.showNotification('报警记录导出成功', 'success');
        }, 2000);
    }

    // 获取报警类型文本
    getTypeText(type) {
        const typeMap = {
            intrusion: '入侵报警',
            face: '人脸报警',
            behavior: '行为报警',
            video: '视频报警'
        };
        return typeMap[type] || '未知';
    }

    // 获取报警类型颜色
    getTypeColor(type) {
        const colorMap = {
            intrusion: 'bg-red-100 text-red-800',
            face: 'bg-orange-100 text-orange-800',
            behavior: 'bg-yellow-100 text-yellow-800',
            video: 'bg-blue-100 text-blue-800'
        };
        return colorMap[type] || 'bg-gray-100 text-gray-800';
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            pending: '未处理',
            processing: '处理中',
            resolved: '已处理'
        };
        return statusMap[status] || '未知';
    }

    // 获取状态颜色
    getStatusColor(status) {
        const colorMap = {
            pending: 'bg-red-100 text-red-800',
            processing: 'bg-yellow-100 text-yellow-800',
            resolved: 'bg-green-100 text-green-800'
        };
        return colorMap[status] || 'bg-gray-100 text-gray-800';
    }

    // 获取严重程度文本
    getSeverityText(severity) {
        const severityMap = {
            high: '高',
            medium: '中',
            low: '低'
        };
        return severityMap[severity] || '未知';
    }

    // 获取严重程度颜色
    getSeverityColor(severity) {
        const colorMap = {
            high: 'bg-red-100 text-red-800',
            medium: 'bg-yellow-100 text-yellow-800',
            low: 'bg-green-100 text-green-800'
        };
        return colorMap[severity] || 'bg-gray-100 text-gray-800';
    }

    // 获取报警边框颜色
    getAlertBorderColor(severity) {
        const colorMap = {
            high: 'border-red-500',
            medium: 'border-yellow-500',
            low: 'border-green-500'
        };
        return colorMap[severity] || 'border-gray-500';
    }

    // 格式化时间
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化日期时间
    formatDateTime(date) {
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局实例
let alertSystem;

// 初始化函数
function initAlerts() {
    alertSystem = new AlertSystem();
}
