// 视频监控主要功能
class VideoMonitor {
    constructor() {
        this.cameras = [];
        this.currentDisplayMode = 4;
        this.currentQuality = 'sd';
        this.selectedCamera = null;
        this.isVideoWallMode = false;
        this.init();
    }

    init() {
        this.initCameras();
        this.initEventListeners();
        this.renderVideoGrid();
        this.renderCameraList();
        this.renderPTZControl();
    }

    // 初始化摄像头数据
    initCameras() {
        this.cameras = [
            { id: 1, name: '大门入口', location: '1号楼大门', status: 'online', recording: true },
            { id: 2, name: '停车场A区', location: '地下停车场', status: 'online', recording: false },
            { id: 3, name: '电梯厅1F', location: '1楼电梯厅', status: 'offline', recording: false },
            { id: 4, name: '办公区走廊', location: '3楼走廊', status: 'online', recording: true },
            { id: 5, name: '会议室A', location: '5楼会议室', status: 'online', recording: false },
            { id: 6, name: '楼顶天台', location: '楼顶', status: 'online', recording: false },
            { id: 7, name: '后门出口', location: '后门', status: 'online', recording: true },
            { id: 8, name: '消防通道', location: '安全出口', status: 'offline', recording: false }
        ];
    }

    // 初始化事件监听器
    initEventListeners() {
        // 显示模式切换
        document.getElementById('displayMode').addEventListener('change', (e) => {
            this.currentDisplayMode = parseInt(e.target.value);
            this.renderVideoGrid();
        });

        // 清晰度切换
        document.getElementById('quality').addEventListener('change', (e) => {
            this.currentQuality = e.target.value;
            this.updateVideoQuality();
        });

        // 视频墙模式
        document.getElementById('videoWallBtn').addEventListener('click', () => {
            this.toggleVideoWallMode();
        });

        // 全屏模式
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });
    }

    // 渲染视频网格
    renderVideoGrid() {
        const videoGrid = document.getElementById('videoGrid');
        const cols = Math.ceil(Math.sqrt(this.currentDisplayMode));
        
        videoGrid.className = `grid gap-2 aspect-video bg-gray-900 rounded-lg overflow-hidden`;
        videoGrid.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
        
        videoGrid.innerHTML = '';
        
        for (let i = 0; i < this.currentDisplayMode; i++) {
            const camera = this.cameras[i];
            const videoContainer = this.createVideoContainer(camera, i);
            videoGrid.appendChild(videoContainer);
        }
    }

    // 创建视频容器
    createVideoContainer(camera, index) {
        const container = document.createElement('div');
        container.className = 'video-container';
        container.dataset.cameraId = camera ? camera.id : '';
        
        if (camera) {
            container.innerHTML = `
                <div class="video-status ${camera.status}">
                    ${camera.status === 'online' ? '在线' : '离线'}
                </div>
                ${camera.recording ? '<div class="recording-indicator"></div>' : ''}
                <div class="video-overlay">
                    <div class="video-controls">
                        <button onclick="videoMonitor.playVideo(${camera.id})" title="播放">
                            <i class="ri-play-line"></i>
                        </button>
                        <button onclick="videoMonitor.pauseVideo(${camera.id})" title="暂停">
                            <i class="ri-pause-line"></i>
                        </button>
                        <button onclick="videoMonitor.captureSnapshot(${camera.id})" title="截图">
                            <i class="ri-camera-line"></i>
                        </button>
                        <button onclick="videoMonitor.toggleRecording(${camera.id})" title="录制">
                            <i class="ri-record-circle-line"></i>
                        </button>
                    </div>
                </div>
                <div class="video-info">
                    <div class="font-medium">${camera.name}</div>
                    <div class="text-xs opacity-75">${camera.location}</div>
                </div>
            `;
            
            // 模拟视频流
            if (camera.status === 'online') {
                this.simulateVideoStream(container, camera);
            } else {
                container.style.background = 'linear-gradient(45deg, #374151, #4b5563)';
                const noSignal = document.createElement('div');
                noSignal.className = 'absolute inset-0 flex items-center justify-center text-white';
                noSignal.innerHTML = '<div class="text-center"><i class="ri-signal-wifi-off-line text-4xl mb-2"></i><div>无信号</div></div>';
                container.appendChild(noSignal);
            }
        } else {
            container.innerHTML = `
                <div class="absolute inset-0 flex items-center justify-center text-gray-400">
                    <div class="text-center">
                        <i class="ri-add-line text-4xl mb-2"></i>
                        <div>点击添加摄像头</div>
                    </div>
                </div>
            `;
            container.style.background = '#1f2937';
            container.style.cursor = 'pointer';
            container.onclick = () => this.addCamera(index);
        }
        
        return container;
    }

    // 模拟视频流
    simulateVideoStream(container, camera) {
        // 创建模拟的视频背景
        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
        const color = colors[camera.id % colors.length];
        container.style.background = `linear-gradient(45deg, ${color}20, ${color}40)`;
        
        // 添加模拟的视频内容
        const videoContent = document.createElement('div');
        videoContent.className = 'absolute inset-0 flex items-center justify-center text-white text-6xl opacity-20';
        videoContent.innerHTML = `<i class="ri-camera-line"></i>`;
        container.appendChild(videoContent);
    }

    // 渲染摄像头列表
    renderCameraList() {
        const cameraList = document.getElementById('cameraList');
        cameraList.innerHTML = '';
        
        this.cameras.forEach(camera => {
            const item = document.createElement('div');
            item.className = `camera-item ${this.selectedCamera === camera.id ? 'active' : ''}`;
            item.onclick = () => this.selectCamera(camera.id);
            
            item.innerHTML = `
                <div class="camera-name">${camera.name}</div>
                <div class="camera-status">
                    <span class="status-dot ${camera.status}"></span>
                    <span>${camera.status === 'online' ? '在线' : '离线'}</span>
                    ${camera.recording ? '<i class="ri-record-circle-fill text-red-500 ml-2"></i>' : ''}
                </div>
                <div class="text-xs text-gray-500 mt-1">${camera.location}</div>
            `;
            
            cameraList.appendChild(item);
        });
    }

    // 渲染PTZ控制面板
    renderPTZControl() {
        const ptzControl = document.getElementById('ptzControl');
        ptzControl.innerHTML = `
            <div class="text-sm font-medium text-gray-700 mb-3">方向控制</div>
            <div class="ptz-direction-pad">
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('up-left')">
                    <i class="ri-arrow-left-up-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('up')">
                    <i class="ri-arrow-up-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('up-right')">
                    <i class="ri-arrow-right-up-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('left')">
                    <i class="ri-arrow-left-line"></i>
                </button>
                <button class="ptz-btn center" onclick="videoMonitor.ptzControl('center')">
                    <i class="ri-focus-3-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('right')">
                    <i class="ri-arrow-right-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('down-left')">
                    <i class="ri-arrow-left-down-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('down')">
                    <i class="ri-arrow-down-line"></i>
                </button>
                <button class="ptz-btn" onclick="videoMonitor.ptzControl('down-right')">
                    <i class="ri-arrow-right-down-line"></i>
                </button>
            </div>
            
            <div class="text-sm font-medium text-gray-700 mb-3 mt-6">缩放控制</div>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="videoMonitor.ptzControl('zoom-in')">
                    <i class="ri-zoom-in-line"></i>
                </button>
                <button class="zoom-btn" onclick="videoMonitor.ptzControl('zoom-out')">
                    <i class="ri-zoom-out-line"></i>
                </button>
            </div>
            
            <div class="text-sm font-medium text-gray-700 mb-3 mt-6">光圈控制</div>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="videoMonitor.ptzControl('iris-open')">
                    <i class="ri-add-line"></i>
                </button>
                <button class="zoom-btn" onclick="videoMonitor.ptzControl('iris-close')">
                    <i class="ri-subtract-line"></i>
                </button>
            </div>
        `;
    }

    // 选择摄像头
    selectCamera(cameraId) {
        this.selectedCamera = cameraId;
        this.renderCameraList();
        console.log(`选择摄像头: ${cameraId}`);
    }

    // PTZ控制
    ptzControl(action) {
        if (!this.selectedCamera) {
            alert('请先选择一个摄像头');
            return;
        }
        
        console.log(`PTZ控制: ${action}, 摄像头: ${this.selectedCamera}`);
        // 这里可以添加实际的PTZ控制API调用
    }

    // 播放视频
    playVideo(cameraId) {
        console.log(`播放视频: ${cameraId}`);
    }

    // 暂停视频
    pauseVideo(cameraId) {
        console.log(`暂停视频: ${cameraId}`);
    }

    // 截图
    captureSnapshot(cameraId) {
        console.log(`截图: ${cameraId}`);
        // 模拟截图成功
        this.showNotification('截图成功', 'success');
    }

    // 切换录制
    toggleRecording(cameraId) {
        const camera = this.cameras.find(c => c.id === cameraId);
        if (camera) {
            camera.recording = !camera.recording;
            this.renderVideoGrid();
            this.renderCameraList();
            this.showNotification(camera.recording ? '开始录制' : '停止录制', 'info');
        }
    }

    // 更新视频质量
    updateVideoQuality() {
        console.log(`切换视频质量: ${this.currentQuality}`);
        this.showNotification(`视频质量已切换为${this.getQualityText()}`, 'info');
    }

    getQualityText() {
        const qualityMap = { 'hd': '高清', 'sd': '标清', 'smooth': '流畅' };
        return qualityMap[this.currentQuality] || '标清';
    }

    // 切换视频墙模式
    toggleVideoWallMode() {
        this.isVideoWallMode = !this.isVideoWallMode;
        if (this.isVideoWallMode) {
            this.enterVideoWallMode();
        } else {
            this.exitVideoWallMode();
        }
    }

    // 进入视频墙模式
    enterVideoWallMode() {
        const videoWall = document.createElement('div');
        videoWall.id = 'videoWallMode';
        videoWall.className = 'video-wall-mode active';
        
        videoWall.innerHTML = `
            <button class="video-wall-exit" onclick="videoMonitor.toggleVideoWallMode()">
                <i class="ri-close-line text-xl"></i>
            </button>
            <div class="video-wall-grid" style="grid-template-columns: repeat(4, 1fr);">
                ${this.cameras.map(camera => `
                    <div class="video-container">
                        <div class="video-status ${camera.status}">
                            ${camera.status === 'online' ? '在线' : '离线'}
                        </div>
                        ${camera.recording ? '<div class="recording-indicator"></div>' : ''}
                        <div class="video-info">
                            <div class="font-medium">${camera.name}</div>
                            <div class="text-xs opacity-75">${camera.location}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        
        document.body.appendChild(videoWall);
    }

    // 退出视频墙模式
    exitVideoWallMode() {
        const videoWall = document.getElementById('videoWallMode');
        if (videoWall) {
            videoWall.remove();
        }
        this.isVideoWallMode = false;
    }

    // 全屏切换
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    // 添加摄像头
    addCamera(index) {
        console.log(`添加摄像头到位置: ${index}`);
        // 这里可以打开添加摄像头的对话框
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局实例
let videoMonitor;

// 初始化函数
function initVideoMonitor() {
    videoMonitor = new VideoMonitor();
}
