<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控 - 智能分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/common.css" rel="stylesheet">
    <link href="./css/video-monitor.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 返回首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="../index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 返回首页
                        </a>
                    </li>
                    
                    <!-- 视频监控菜单 -->
                    <li class="nav-item mt-4">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="ri-camera-line mr-2"></i>
                                <span>视频监控</span>
                            </div>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1 max-h-none">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="index.html" class="flex items-center w-full">
                                    <i class="ri-live-line mr-2"></i> 实时监控
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="playback.html" class="flex items-center w-full">
                                    <i class="ri-play-circle-line mr-2"></i> 录像回放
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="ai-analysis.html" class="flex items-center w-full">
                                    <i class="ri-brain-line mr-2"></i> 智能分析
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alerts.html" class="flex items-center w-full">
                                    <i class="ri-alarm-warning-line mr-2"></i> 报警联动
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="storage.html" class="flex items-center w-full">
                                    <i class="ri-database-2-line mr-2"></i> 存储管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600 space-x-2">
                            <i class="ri-camera-line mr-2"></i>
                            <span>视频监控</span>
                            <i class="ri-arrow-right-s-line text-gray-400"></i>
                            <span class="text-blue-600 font-medium">智能分析</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能分析内容 -->
            <div class="p-6">
                <!-- 分析功能选择 -->
                <div class="bg-white rounded-xl shadow-sm mb-6">
                    <div class="p-6 border-b">
                        <h3 class="text-xl font-bold text-gray-800 flex items-center">
                            <i class="ri-brain-line mr-2 text-blue-600"></i>
                            AI智能分析
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                            <button id="faceRecognitionBtn" class="analysis-type-btn active" data-type="face">
                                <i class="ri-user-smile-line text-2xl mb-2"></i>
                                <span>人脸识别</span>
                            </button>
                            <button id="behaviorAnalysisBtn" class="analysis-type-btn" data-type="behavior">
                                <i class="ri-run-line text-2xl mb-2"></i>
                                <span>行为分析</span>
                            </button>
                            <button id="vehicleRecognitionBtn" class="analysis-type-btn" data-type="vehicle">
                                <i class="ri-car-line text-2xl mb-2"></i>
                                <span>车辆识别</span>
                            </button>
                            <button id="objectDetectionBtn" class="analysis-type-btn" data-type="object">
                                <i class="ri-scan-line text-2xl mb-2"></i>
                                <span>物体识别</span>
                            </button>
                            <button id="intrusionDetectionBtn" class="analysis-type-btn" data-type="intrusion">
                                <i class="ri-shield-line text-2xl mb-2"></i>
                                <span>入侵检测</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分析结果区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 实时分析画面 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b flex justify-between items-center">
                                <h4 class="font-semibold text-gray-800">实时分析</h4>
                                <div class="flex items-center space-x-2">
                                    <select id="analysisCamera" class="px-3 py-1 border rounded text-sm">
                                        <option value="1">大门入口</option>
                                        <option value="2">停车场A区</option>
                                        <option value="4">办公区走廊</option>
                                    </select>
                                    <button id="startAnalysisBtn" class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700">
                                        开始分析
                                    </button>
                                </div>
                            </div>
                            <div class="p-4">
                                <div id="analysisVideo" class="aspect-video bg-gray-900 rounded-lg relative overflow-hidden">
                                    <div class="absolute inset-0 flex items-center justify-center text-white">
                                        <div class="text-center">
                                            <i class="ri-brain-line text-6xl mb-4 opacity-50"></i>
                                            <div class="text-lg">AI智能分析</div>
                                            <div class="text-sm opacity-75">选择摄像头开始分析</div>
                                        </div>
                                    </div>
                                    <!-- 分析结果覆盖层 -->
                                    <div id="analysisOverlay" class="absolute inset-0 pointer-events-none">
                                        <!-- 检测框和标签将通过JavaScript动态添加 -->
                                    </div>
                                </div>
                                
                                <!-- 分析统计 -->
                                <div class="mt-4 grid grid-cols-4 gap-4">
                                    <div class="bg-blue-50 p-3 rounded-lg text-center">
                                        <div class="text-2xl font-bold text-blue-600" id="detectionCount">0</div>
                                        <div class="text-sm text-gray-600">检测数量</div>
                                    </div>
                                    <div class="bg-green-50 p-3 rounded-lg text-center">
                                        <div class="text-2xl font-bold text-green-600" id="accuracyRate">95%</div>
                                        <div class="text-sm text-gray-600">准确率</div>
                                    </div>
                                    <div class="bg-yellow-50 p-3 rounded-lg text-center">
                                        <div class="text-2xl font-bold text-yellow-600" id="processingTime">120ms</div>
                                        <div class="text-sm text-gray-600">处理时间</div>
                                    </div>
                                    <div class="bg-purple-50 p-3 rounded-lg text-center">
                                        <div class="text-2xl font-bold text-purple-600" id="alertCount">2</div>
                                        <div class="text-sm text-gray-600">报警次数</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析配置和结果 -->
                    <div class="space-y-6">
                        <!-- 分析配置 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">分析配置</h4>
                            </div>
                            <div class="p-4">
                                <div id="analysisConfig" class="space-y-4">
                                    <!-- 配置项将根据选择的分析类型动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 检测结果列表 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">检测结果</h4>
                            </div>
                            <div class="p-4">
                                <div id="detectionResults" class="space-y-2 max-h-64 overflow-y-auto">
                                    <!-- 检测结果将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 历史统计 -->
                        <div class="bg-white rounded-xl shadow-sm">
                            <div class="p-4 border-b">
                                <h4 class="font-semibold text-gray-800">今日统计</h4>
                            </div>
                            <div class="p-4">
                                <div id="todayStats" class="space-y-3">
                                    <!-- 统计数据将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析历史记录 -->
                <div class="bg-white rounded-xl shadow-sm mt-6">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-xl font-bold text-gray-800">分析历史</h3>
                        <div class="flex items-center space-x-2">
                            <select id="historyFilter" class="px-3 py-2 border rounded-lg text-sm">
                                <option value="today">今天</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                            </select>
                            <button id="exportBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                                <i class="ri-download-line mr-1"></i>
                                导出报告
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b">
                                        <th class="text-left py-3 px-4">时间</th>
                                        <th class="text-left py-3 px-4">摄像头</th>
                                        <th class="text-left py-3 px-4">分析类型</th>
                                        <th class="text-left py-3 px-4">检测结果</th>
                                        <th class="text-left py-3 px-4">置信度</th>
                                        <th class="text-left py-3 px-4">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTable">
                                    <!-- 历史记录将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="../js/common.js"></script>
    <script src="./js/ai-analysis.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initAIAnalysis();
        });
    </script>
</body>
</html>
