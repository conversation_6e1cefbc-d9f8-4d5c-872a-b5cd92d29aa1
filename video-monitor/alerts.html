<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控 - 报警联动</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/common.css" rel="stylesheet">
    <link href="./css/video-monitor.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 返回首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="../index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 返回首页
                        </a>
                    </li>
                    
                    <!-- 视频监控菜单 -->
                    <li class="nav-item mt-4">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="ri-camera-line mr-2"></i>
                                <span>视频监控</span>
                            </div>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1 max-h-none">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="index.html" class="flex items-center w-full">
                                    <i class="ri-live-line mr-2"></i> 实时监控
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="playback.html" class="flex items-center w-full">
                                    <i class="ri-play-circle-line mr-2"></i> 录像回放
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ai-analysis.html" class="flex items-center w-full">
                                    <i class="ri-brain-line mr-2"></i> 智能分析
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="alerts.html" class="flex items-center w-full">
                                    <i class="ri-alarm-warning-line mr-2"></i> 报警联动
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="storage.html" class="flex items-center w-full">
                                    <i class="ri-database-2-line mr-2"></i> 存储管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600 space-x-2">
                            <i class="ri-camera-line mr-2"></i>
                            <span>视频监控</span>
                            <i class="ri-arrow-right-s-line text-gray-400"></i>
                            <span class="text-blue-600 font-medium">报警联动</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报警联动内容 -->
            <div class="p-6">
                <!-- 报警状态概览 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">今日报警</p>
                                <p class="text-2xl font-bold text-red-600" id="todayAlerts">12</p>
                            </div>
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="ri-alarm-warning-line text-2xl text-red-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">未处理</p>
                                <p class="text-2xl font-bold text-orange-600" id="pendingAlerts">3</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="ri-time-line text-2xl text-orange-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">已处理</p>
                                <p class="text-2xl font-bold text-green-600" id="resolvedAlerts">9</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="ri-check-line text-2xl text-green-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">联动次数</p>
                                <p class="text-2xl font-bold text-blue-600" id="linkageCount">8</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="ri-links-line text-2xl text-blue-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时报警和联动配置 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- 实时报警 -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b flex justify-between items-center">
                            <h3 class="text-xl font-bold text-gray-800 flex items-center">
                                <i class="ri-alarm-warning-line mr-2 text-red-600"></i>
                                实时报警
                            </h3>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-500">自动刷新</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="autoRefresh" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                        <div class="p-6">
                            <div id="realTimeAlerts" class="space-y-3 max-h-96 overflow-y-auto">
                                <!-- 实时报警列表将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 联动配置 -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-xl font-bold text-gray-800 flex items-center">
                                <i class="ri-settings-3-line mr-2 text-blue-600"></i>
                                联动配置
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <!-- 视频联动 -->
                                <div class="border rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-gray-800">视频联动</h4>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer" checked>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                    <div class="text-sm text-gray-600 space-y-2">
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> 自动弹出报警画面
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> 自动开始录像
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2"> 自动截图保存
                                        </div>
                                    </div>
                                </div>

                                <!-- 门禁联动 -->
                                <div class="border rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-gray-800">门禁联动</h4>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer" checked>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                    <div class="text-sm text-gray-600 space-y-2">
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> 自动锁定门禁
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2"> 触发警报声
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> 通知安保人员
                                        </div>
                                    </div>
                                </div>

                                <!-- 消息推送 -->
                                <div class="border rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-gray-800">消息推送</h4>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer" checked>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                    <div class="text-sm text-gray-600 space-y-2">
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> 短信通知
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> App推送
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2" checked> Web端弹窗
                                        </div>
                                        <div class="flex items-center">
                                            <input type="checkbox" class="mr-2"> 邮件通知
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报警历史记录 -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-xl font-bold text-gray-800">报警历史</h3>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-600">筛选:</label>
                                <select id="alertFilter" class="px-3 py-2 border rounded-lg text-sm">
                                    <option value="all">全部报警</option>
                                    <option value="video">视频报警</option>
                                    <option value="intrusion">入侵报警</option>
                                    <option value="face">人脸报警</option>
                                    <option value="behavior">行为报警</option>
                                </select>
                            </div>
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-600">状态:</label>
                                <select id="statusFilter" class="px-3 py-2 border rounded-lg text-sm">
                                    <option value="all">全部状态</option>
                                    <option value="pending">未处理</option>
                                    <option value="processing">处理中</option>
                                    <option value="resolved">已处理</option>
                                </select>
                            </div>
                            <button id="exportAlertsBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                                <i class="ri-download-line mr-1"></i>
                                导出记录
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b">
                                        <th class="text-left py-3 px-4">时间</th>
                                        <th class="text-left py-3 px-4">报警类型</th>
                                        <th class="text-left py-3 px-4">摄像头</th>
                                        <th class="text-left py-3 px-4">报警内容</th>
                                        <th class="text-left py-3 px-4">处理状态</th>
                                        <th class="text-left py-3 px-4">联动动作</th>
                                        <th class="text-left py-3 px-4">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="alertHistoryTable">
                                    <!-- 报警历史记录将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 报警弹窗 -->
    <div id="alertModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full">
                <div class="p-6 border-b">
                    <h3 class="text-xl font-bold text-red-600 flex items-center">
                        <i class="ri-alarm-warning-line mr-2"></i>
                        紧急报警
                    </h3>
                </div>
                <div class="p-6">
                    <div id="alertModalContent">
                        <!-- 报警详情内容 -->
                    </div>
                </div>
                <div class="p-6 border-t flex justify-end space-x-3">
                    <button id="ignoreAlertBtn" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                        忽略
                    </button>
                    <button id="handleAlertBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        处理
                    </button>
                    <button id="closeAlertBtn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="../js/common.js"></script>
    <script src="./js/alerts.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initAlerts();
        });
    </script>
</body>
</html>
