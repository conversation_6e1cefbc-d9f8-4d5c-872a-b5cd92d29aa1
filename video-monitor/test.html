<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="../styles/common.css" rel="stylesheet">
    <link href="./css/video-monitor.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-6">
        <div class="bg-white rounded-xl shadow-sm mb-6">
            <div class="p-6 border-b">
                <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                    <i class="ri-test-tube-line mr-2 text-blue-600"></i>
                    视频监控功能测试
                </h1>
                <p class="text-gray-600 mt-2">测试所有视频监控模块的功能是否正常工作</p>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 实时监控测试 -->
                    <div class="test-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                <i class="ri-live-line mr-2 text-green-600"></i>
                                实时监控
                            </h3>
                            <span id="liveStatus" class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">未测试</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>多画面显示:</span>
                                <span id="multiViewTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>PTZ控制:</span>
                                <span id="ptzTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>清晰度切换:</span>
                                <span id="qualityTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>视频墙模式:</span>
                                <span id="videoWallTest" class="text-gray-500">-</span>
                            </div>
                        </div>
                        <button onclick="testLiveMonitor()" class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            开始测试
                        </button>
                        <a href="index.html" class="block w-full mt-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-center">
                            访问页面
                        </a>
                    </div>

                    <!-- 录像回放测试 -->
                    <div class="test-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                <i class="ri-play-circle-line mr-2 text-purple-600"></i>
                                录像回放
                            </h3>
                            <span id="playbackStatus" class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">未测试</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>录像查询:</span>
                                <span id="searchTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>播放控制:</span>
                                <span id="playControlTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>倍速回放:</span>
                                <span id="speedTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>下载功能:</span>
                                <span id="downloadTest" class="text-gray-500">-</span>
                            </div>
                        </div>
                        <button onclick="testPlayback()" class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            开始测试
                        </button>
                        <a href="playback.html" class="block w-full mt-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-center">
                            访问页面
                        </a>
                    </div>

                    <!-- 智能分析测试 -->
                    <div class="test-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                <i class="ri-brain-line mr-2 text-orange-600"></i>
                                智能分析
                            </h3>
                            <span id="aiStatus" class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">未测试</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>人脸识别:</span>
                                <span id="faceTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>行为分析:</span>
                                <span id="behaviorTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>车辆识别:</span>
                                <span id="vehicleTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>入侵检测:</span>
                                <span id="intrusionTest" class="text-gray-500">-</span>
                            </div>
                        </div>
                        <button onclick="testAIAnalysis()" class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            开始测试
                        </button>
                        <a href="ai-analysis.html" class="block w-full mt-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-center">
                            访问页面
                        </a>
                    </div>

                    <!-- 报警联动测试 -->
                    <div class="test-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                <i class="ri-alarm-warning-line mr-2 text-red-600"></i>
                                报警联动
                            </h3>
                            <span id="alertStatus" class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">未测试</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>实时报警:</span>
                                <span id="realTimeAlertTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>联动配置:</span>
                                <span id="linkageTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>消息推送:</span>
                                <span id="pushTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>事件回溯:</span>
                                <span id="tracebackTest" class="text-gray-500">-</span>
                            </div>
                        </div>
                        <button onclick="testAlerts()" class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            开始测试
                        </button>
                        <a href="alerts.html" class="block w-full mt-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-center">
                            访问页面
                        </a>
                    </div>

                    <!-- 存储管理测试 -->
                    <div class="test-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                <i class="ri-database-2-line mr-2 text-indigo-600"></i>
                                存储管理
                            </h3>
                            <span id="storageStatus" class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">未测试</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>存储设备:</span>
                                <span id="deviceTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>存储策略:</span>
                                <span id="strategyTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>备份管理:</span>
                                <span id="backupTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>容量监控:</span>
                                <span id="capacityTest" class="text-gray-500">-</span>
                            </div>
                        </div>
                        <button onclick="testStorage()" class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            开始测试
                        </button>
                        <a href="storage.html" class="block w-full mt-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-center">
                            访问页面
                        </a>
                    </div>

                    <!-- 整体测试 -->
                    <div class="test-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                <i class="ri-check-double-line mr-2 text-green-600"></i>
                                整体测试
                            </h3>
                            <span id="overallStatus" class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800">未测试</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>导航功能:</span>
                                <span id="navigationTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>响应式设计:</span>
                                <span id="responsiveTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>性能测试:</span>
                                <span id="performanceTest" class="text-gray-500">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span>兼容性测试:</span>
                                <span id="compatibilityTest" class="text-gray-500">-</span>
                            </div>
                        </div>
                        <button onclick="testOverall()" class="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                            开始测试
                        </button>
                        <button onclick="runAllTests()" class="w-full mt-2 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                            运行全部测试
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试结果汇总 -->
        <div class="bg-white rounded-xl shadow-sm">
            <div class="p-6 border-b">
                <h2 class="text-xl font-bold text-gray-800">测试结果汇总</h2>
            </div>
            <div class="p-6">
                <div id="testResults" class="space-y-4">
                    <div class="text-center text-gray-500 py-8">
                        <i class="ri-test-tube-line text-4xl mb-2"></i>
                        <div>点击上方按钮开始测试</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="../js/common.js"></script>
    <script src="./js/test.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initTest();
        });
    </script>
</body>
</html>
