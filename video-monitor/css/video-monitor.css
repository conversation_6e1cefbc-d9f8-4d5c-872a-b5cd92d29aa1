/* 视频监控专用样式 */

/* 视频画面容器 */
.video-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 16/9;
}

.video-container:hover .video-overlay {
    opacity: 1;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.video-controls {
    display: flex;
    gap: 8px;
}

.video-controls button {
    padding: 8px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.video-controls button:hover {
    background: rgba(255, 255, 255, 1);
}

/* 视频状态指示器 */
.video-status {
    position: absolute;
    top: 8px;
    left: 8px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    z-index: 20;
}

.video-status.online {
    background: rgba(34, 197, 94, 0.8);
}

.video-status.offline {
    background: rgba(239, 68, 68, 0.8);
}

/* 视频信息 */
.video-info {
    position: absolute;
    bottom: 8px;
    left: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 20;
}

/* PTZ控制面板 */
.ptz-direction-pad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    max-width: 120px;
    margin: 0 auto;
}

.ptz-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.ptz-btn:hover {
    background: #d1d5db;
    transform: scale(1.05);
}

.ptz-btn:active {
    background: #9ca3af;
    transform: scale(0.95);
}

.ptz-btn.center {
    background: #3b82f6;
    color: white;
}

.ptz-btn.center:hover {
    background: #2563eb;
}

/* 缩放控制 */
.zoom-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: #3b82f6;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background: #2563eb;
    transform: scale(1.05);
}

/* 摄像头列表项 */
.camera-item {
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.camera-item:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.camera-item.active {
    border-color: #3b82f6;
    background: #eff6ff;
}

.camera-item .camera-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4px;
}

.camera-item .camera-status {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.camera-item .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.camera-item .status-dot.online {
    background: #10b981;
}

.camera-item .status-dot.offline {
    background: #ef4444;
}

/* 视频墙模式 */
.video-wall-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
    z-index: 1000;
    display: none;
}

.video-wall-mode.active {
    display: block;
}

.video-wall-grid {
    width: 100%;
    height: 100%;
    display: grid;
    gap: 2px;
    padding: 2px;
}

.video-wall-exit {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    z-index: 1001;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .ptz-direction-pad {
        max-width: 100px;
    }
    
    .ptz-btn {
        width: 30px;
        height: 30px;
    }
    
    .zoom-btn {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 768px) {
    .video-info {
        font-size: 10px;
        padding: 6px;
    }
    
    .video-status {
        font-size: 10px;
        padding: 2px 6px;
    }
    
    .camera-item {
        padding: 8px;
    }
}

/* 分析类型按钮 */
.analysis-type-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 100px;
}

.analysis-type-btn:hover {
    border-color: #3b82f6;
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.analysis-type-btn.active {
    border-color: #3b82f6;
    background: #eff6ff;
    color: #3b82f6;
}

.analysis-type-btn i {
    color: #6b7280;
    transition: color 0.3s ease;
}

.analysis-type-btn:hover i,
.analysis-type-btn.active i {
    color: #3b82f6;
}

.analysis-type-btn span {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    transition: color 0.3s ease;
}

.analysis-type-btn:hover span,
.analysis-type-btn.active span {
    color: #3b82f6;
}

/* 加载动画 */
.loading-spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 录制指示器 */
.recording-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 2s infinite;
    z-index: 20;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}
