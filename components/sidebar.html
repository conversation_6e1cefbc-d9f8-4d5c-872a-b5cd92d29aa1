<!-- 侧边栏 -->
<aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
    <div class="flex items-center space-x-2 mb-6">
        <i class="ri-cloud-line text-2xl text-blue-400"></i>
        <h2 class="text-xl font-bold">智慧物联网平台</h2>
    </div>
    <nav class="flex-1">
        <ul class="space-y-1">
            <!-- 首页 -->
            <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                <i class="ri-home-line mr-2"></i> 首页
            </li>
            
            <!-- 网关配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-gateway-line mr-2"></i>
                        <span>网关配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-settings-line mr-2"></i> 网关管理
                    </li>
                </ul>
            </li>

            <!-- 设备配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-device-line mr-2"></i>
                        <span>设备配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-folder-line mr-2"></i> 设备分组管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-computer-line mr-2"></i> 设备管理
                    </li>
                </ul>
            </li>

            <!-- 空间配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-building-line mr-2"></i>
                        <span>空间配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-layout-line mr-2"></i> 空间类型管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-building-2-line mr-2"></i> 空间管理
                    </li>
                </ul>
            </li>

            <!-- 报警配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-alarm-warning-line mr-2"></i>
                        <span>报警配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-team-line mr-2"></i> 通知组管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-alert-line mr-2"></i> 报警规则管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-history-line mr-2"></i> 报警记录管理
                    </li>
                </ul>
            </li>

            <!-- 工单配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-file-list-line mr-2"></i>
                        <span>工单配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-file-list-line mr-2"></i> 工单管理
                    </li>
                </ul>
            </li>

            <!-- 规则管理 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-git-branch-line mr-2"></i>
                        <span>规则管理</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-file-list-line mr-2"></i> 规则日志
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-message-2-line mr-2"></i> 消息规则
                    </li>
                </ul>
            </li>

            <!-- 任务管理 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-task-line mr-2"></i>
                        <span>任务管理</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-list-check mr-2"></i> 任务列表
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-file-list-line mr-2"></i> 任务日志
                    </li>
                </ul>
            </li>

            <!-- 可视化大屏配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-dashboard-3-line mr-2"></i>
                        <span>可视化大屏配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                    </li>
                </ul>
            </li>

            <!-- 系统配置 -->
            <li class="nav-item mt-2">
                <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                    <div class="flex items-center">
                        <i class="ri-settings-3-line mr-2"></i>
                        <span>系统配置</span>
                    </div>
                    <i class="ri-arrow-right-s-line nav-arrow"></i>
                </div>
                <ul class="nav-submenu pl-8 mt-1">
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-user-settings-line mr-2"></i> 用户管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-team-line mr-2"></i> 部门管理
                    </li>
                    <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                        <i class="ri-shield-user-line mr-2"></i> 角色管理
                    </li>
                </ul>
            </li>
        </ul>
    </nav>
</aside> 