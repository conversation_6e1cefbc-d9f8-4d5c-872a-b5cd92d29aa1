# 智慧物联网平台功能操作手册

## 📖 目录

### 第一章 平台概述
- 1.1 平台介绍
- 1.2 功能概述
- 1.3 系统架构
- 1.4 技术特点
- 1.5 适用场景

### 第二章 用户登录和导航
- 2.1 系统登录
- 2.2 界面布局
- 2.3 导航使用
- 2.4 基本操作
- 2.5 快捷键说明

### 第三章 网关配置
- 3.1 网关管理概述
- 3.2 网关列表查看
- 3.3 新增网关
- 3.4 编辑网关
- 3.5 删除网关
- 3.6 网关状态管理

### 第四章 设备配置
- 4.1 设备管理概述
- 4.2 设备类型管理
- 4.3 设备分组管理
- 4.4 设备管理
- 4.5 设备监控

### 第五章 空间配置
- 5.1 空间管理概述
- 5.2 空间类型管理
- 5.3 空间管理
- 5.4 空间层级结构

### 第六章 台账管理
- 6.1 台账管理概述
- 6.2 台账记录管理
- 6.3 资产类型管理
- 6.4 台账导入导出
- 6.5 到期管理

### 第七章 报警配置
- 7.1 报警系统概述
- 7.2 通知组管理
- 7.3 报警规则管理
- 7.4 报警记录管理
- 7.5 报警处理流程

### 第八章 工单配置
- 8.1 工单系统概述
- 8.2 工单管理
- 8.3 工单流程
- 8.4 工单状态管理

### 第九章 规则管理
- 9.1 规则系统概述
- 9.2 规则日志管理
- 9.3 消息规则管理
- 9.4 规则配置

### 第十章 任务管理
- 10.1 任务系统概述
- 10.2 定时任务管理
- 10.3 任务日志管理
- 10.4 任务调度

### 第十一章 可视化大屏配置
- 11.1 大屏系统概述
- 11.2 组态管理
- 11.3 大屏配置
- 11.4 数据展示

### 第十二章 系统配置
- 12.1 系统管理概述
- 12.2 用户管理
- 12.3 部门管理
- 12.4 角色管理
- 12.5 操作日志管理

### 第十三章 个人配置
- 13.1 个人设置概述
- 13.2 个人信息管理
- 13.3 密码修改
- 13.4 个性化设置

### 第十四章 常见问题和故障排查
- 14.1 常见问题FAQ
- 14.2 故障排查指南
- 14.3 错误代码说明
- 14.4 技术支持

### 第十五章 附录
- 15.1 术语表
- 15.2 参考资料
- 15.3 版本更新记录
- 15.4 联系方式

---

## 📋 手册使用说明

### 阅读指南
- **新用户**：建议从第一章开始，按顺序阅读前三章
- **管理员**：重点关注第四章到第十二章的配置管理
- **普通用户**：重点关注第二章、第六章和第十三章
- **技术人员**：重点关注第九章到第十一章的高级功能

### 符号说明
- 📋 **操作步骤**：具体的操作指导
- ⚠️ **注意事项**：需要特别注意的内容
- 💡 **提示**：有用的小技巧和建议
- 🔧 **技术说明**：技术相关的详细说明

### 版本信息
- **手册版本**：v1.0
- **平台版本**：v1.0
- **更新日期**：2024年12月
- **适用范围**：智慧物联网平台全功能版本

---

## 第一章 平台概述

### 1.1 平台介绍

智慧物联网平台是一个集设备管理、数据采集、实时监控、报警处理、资产管理于一体的综合性物联网管理系统。平台采用现代化的Web技术架构，提供直观易用的操作界面，支持多种设备接入和数据处理方式。

#### 核心价值
- **统一管理**：集中管理所有物联网设备和资产
- **实时监控**：实时监控设备状态和数据变化
- **智能报警**：智能化的报警规则和通知机制
- **数据分析**：强大的数据分析和可视化能力
- **流程管理**：完整的工单和任务管理流程

#### 应用场景
- **智慧楼宇**：办公楼宇的设备和环境监控
- **工业园区**：工业设备的集中监控和管理
- **智慧校园**：学校设施的智能化管理
- **医疗机构**：医疗设备的监控和维护管理
- **商业综合体**：商业设施的统一管理

### 1.2 功能概述

#### 设备管理
- **设备接入**：支持多种协议的设备接入
- **设备分类**：灵活的设备类型和分组管理
- **状态监控**：实时监控设备运行状态
- **远程控制**：支持设备的远程控制操作

#### 资产管理
- **台账管理**：完整的资产台账记录
- **生命周期**：资产从采购到报废的全生命周期管理
- **到期提醒**：智能的到期提醒和续期管理
- **资产盘点**：支持资产的定期盘点

#### 报警管理
- **规则配置**：灵活的报警规则配置
- **多级报警**：支持多级报警和升级机制
- **通知方式**：多种通知方式（邮件、短信、系统通知）
- **处理流程**：完整的报警处理和跟踪流程

#### 数据分析
- **实时数据**：实时数据采集和展示
- **历史数据**：历史数据存储和查询
- **统计分析**：多维度的数据统计分析
- **可视化**：丰富的图表和大屏展示

### 1.3 系统架构

#### 技术架构
- **前端技术**：HTML5 + CSS3 + JavaScript
- **UI框架**：Tailwind CSS
- **图标库**：Remix Icon
- **响应式设计**：支持多种设备和屏幕尺寸

#### 功能架构
```
智慧物联网平台
├── 设备管理层
│   ├── 网关配置
│   ├── 设备配置
│   └── 空间配置
├── 业务管理层
│   ├── 台账管理
│   ├── 报警配置
│   ├── 工单配置
│   └── 规则管理
├── 任务调度层
│   ├── 任务管理
│   └── 可视化大屏
└── 系统管理层
    ├── 系统配置
    └── 个人配置
```

#### 数据流架构
1. **数据采集**：通过网关采集设备数据
2. **数据处理**：实时处理和存储数据
3. **规则引擎**：根据规则触发报警和任务
4. **业务处理**：处理工单、台账等业务流程
5. **数据展示**：通过界面和大屏展示数据

### 1.4 技术特点

#### 用户体验
- **直观界面**：简洁直观的用户界面设计
- **响应式**：支持PC、平板、手机等多种设备
- **快捷操作**：丰富的快捷键和批量操作
- **个性化**：支持个性化设置和主题

#### 性能特点
- **高并发**：支持大量设备的并发接入
- **实时性**：毫秒级的数据处理和响应
- **稳定性**：7×24小时稳定运行
- **扩展性**：模块化设计，支持功能扩展

#### 安全特性
- **权限控制**：基于角色的权限管理
- **操作审计**：完整的操作日志记录
- **数据安全**：数据加密和备份机制
- **访问控制**：IP白名单和访问限制

### 1.5 适用场景

#### 智慧楼宇
- **环境监控**：温湿度、空气质量监控
- **能耗管理**：电力、水务等能耗监控
- **安防系统**：门禁、监控、报警系统
- **设备维护**：电梯、空调等设备维护

#### 工业园区
- **生产监控**：生产设备状态监控
- **安全管理**：安全设备和环境监控
- **能源管理**：能源消耗和优化管理
- **资产管理**：工业设备资产管理

#### 智慧校园
- **教学设备**：多媒体设备管理
- **安全监控**：校园安全监控系统
- **环境管理**：教室、宿舍环境监控
- **资产管理**：教学资产管理

---

## 第二章 用户登录和导航

### 2.1 系统登录

#### 登录步骤
1. **打开浏览器**：推荐使用Chrome、Firefox、Safari等现代浏览器
2. **输入地址**：在地址栏输入平台访问地址
3. **输入凭据**：输入用户名和密码
4. **点击登录**：点击"登录"按钮进入系统

#### 登录要求
- **浏览器版本**：建议使用最新版本的主流浏览器
- **网络连接**：确保网络连接稳定
- **JavaScript**：确保浏览器启用JavaScript
- **Cookie**：确保浏览器允许Cookie

⚠️ **注意事项**
- 首次登录可能需要修改初始密码
- 连续登录失败可能导致账户锁定
- 建议定期修改密码以确保安全

### 2.2 界面布局

#### 整体布局
平台采用经典的左右布局结构：
- **左侧导航栏**：功能菜单和导航
- **右侧内容区**：具体功能页面
- **顶部导航**：面包屑导航和用户信息

#### 左侧导航栏
```
智慧物联网平台
├── 首页
├── 网关配置
│   └── 网关管理
├── 设备配置
│   ├── 设备类型管理
│   ├── 设备分组管理
│   └── 设备管理
├── 空间配置
│   ├── 空间类型管理
│   └── 空间管理
├── 台账管理
│   ├── 台账记录
│   └── 资产类型管理
├── 报警配置
│   ├── 通知组管理
│   ├── 报警规则管理
│   └── 报警记录管理
├── 工单配置
│   └── 工单管理
├── 规则管理
│   ├── 规则日志
│   └── 消息规则
├── 任务管理
│   ├── 定时任务
│   └── 任务日志
├── 可视化大屏配置
│   └── 组态管理
├── 系统配置
│   ├── 用户管理
│   ├── 部门管理
│   ├── 角色管理
│   └── 操作日志
└── 个人配置
```

#### 右侧内容区
- **顶部操作栏**：页面标题、操作按钮、搜索筛选
- **主体内容区**：数据列表、表单、图表等
- **底部分页**：分页控件和统计信息

### 2.3 导航使用

#### 菜单展开/收起
- **点击菜单项**：点击带有箭头的菜单项可展开/收起子菜单
- **当前页面高亮**：当前所在页面的菜单项会高亮显示
- **面包屑导航**：顶部显示当前位置的层级路径

#### 页面跳转
- **点击菜单**：点击左侧菜单项跳转到对应页面
- **面包屑链接**：点击面包屑中的链接快速跳转
- **浏览器前进后退**：支持浏览器的前进后退功能

#### 快速导航
💡 **提示**：
- 使用Ctrl+F可在页面内快速搜索
- 大部分页面支持键盘快捷键操作
- 收藏常用页面到浏览器书签

### 2.4 基本操作

#### 数据列表操作
- **查看数据**：列表展示所有数据记录
- **搜索筛选**：使用搜索框和筛选条件查找数据
- **排序**：点击表头可对数据进行排序
- **分页**：使用底部分页控件浏览大量数据

#### 数据编辑操作
- **新增**：点击"新增"按钮添加新记录
- **编辑**：点击"编辑"按钮修改现有记录
- **删除**：点击"删除"按钮删除记录（需确认）
- **批量操作**：选择多条记录进行批量操作

#### 表单操作
- **必填字段**：标有红色星号(*)的字段为必填
- **数据验证**：系统会自动验证输入数据的格式
- **保存**：点击"确认"或"保存"按钮提交数据
- **取消**：点击"取消"按钮放弃修改

#### 导入导出操作
- **导入数据**：支持Excel文件的数据导入
- **导出数据**：支持将数据导出为Excel文件
- **模板下载**：提供标准的导入模板下载

### 2.5 快捷键说明

#### 全局快捷键
- **Ctrl + N**：新增记录（在支持的页面）
- **Escape**：关闭弹窗或抽屉
- **Ctrl + S**：保存当前编辑（在表单页面）
- **Ctrl + F**：页面内搜索

#### 列表页面快捷键
- **↑/↓**：上下选择记录
- **Enter**：查看选中记录详情
- **Delete**：删除选中记录

#### 表单页面快捷键
- **Tab**：切换到下一个输入框
- **Shift + Tab**：切换到上一个输入框
- **Enter**：提交表单（在最后一个输入框时）

⚠️ **注意事项**
- 快捷键在不同页面可能有所差异
- 某些快捷键可能与浏览器快捷键冲突
- 建议熟悉常用快捷键以提高操作效率

## 第三章 网关配置

### 3.1 网关管理概述

网关是物联网设备接入平台的重要桥梁，负责设备数据的采集、协议转换和数据传输。通过网关管理功能，可以统一管理所有接入的网关设备，监控其运行状态，配置相关参数。

#### 网关功能
- **设备接入**：支持多种协议的设备接入
- **数据采集**：实时采集设备数据
- **协议转换**：将设备协议转换为平台标准协议
- **数据传输**：将采集的数据传输到平台
- **状态监控**：监控网关运行状态

#### 网关类型
- **有线网关**：通过以太网连接的网关
- **无线网关**：通过WiFi、4G等无线方式连接的网关
- **边缘网关**：具备边缘计算能力的智能网关
- **协议网关**：专门用于协议转换的网关

### 3.2 网关列表查看

#### 访问路径
📋 **操作步骤**：
1. 登录系统后，点击左侧导航栏"网关配置"
2. 点击"网关管理"进入网关列表页面

#### 列表信息
网关列表显示以下信息：
- **序号**：记录序号
- **网关编号**：网关的唯一标识
- **网关名称**：网关的中文名称
- **网关类型**：网关的类型分类
- **连接状态**：在线/离线状态
- **IP地址**：网关的IP地址
- **最后通信时间**：最近一次通信的时间
- **操作**：编辑、删除等操作按钮

#### 状态说明
- **🟢 在线**：网关正常连接，数据传输正常
- **🔴 离线**：网关断开连接，无法通信
- **🟡 异常**：网关连接不稳定或数据异常

#### 搜索筛选
- **关键词搜索**：输入网关名称或编号进行搜索
- **状态筛选**：按连接状态筛选网关
- **类型筛选**：按网关类型筛选
- **时间筛选**：按最后通信时间筛选

### 3.3 新增网关

#### 操作步骤
📋 **操作步骤**：
1. 在网关列表页面，点击"新增网关"按钮
2. 在弹出的表单中填写网关信息
3. 点击"确认"按钮保存网关信息

#### 必填字段
- **网关编号** *：网关的唯一标识，不可重复
- **网关名称** *：网关的中文名称
- **网关类型** *：选择网关类型
- **IP地址** *：网关的IP地址
- **端口号** *：网关的通信端口

#### 可选字段
- **MAC地址**：网关的MAC地址
- **固件版本**：网关的固件版本号
- **安装位置**：网关的物理安装位置
- **负责人**：网关的维护负责人
- **备注信息**：其他相关说明

#### 验证规则
- **网关编号**：只能包含字母、数字和下划线
- **IP地址**：必须是有效的IP地址格式
- **端口号**：必须是1-65535之间的数字
- **MAC地址**：必须是有效的MAC地址格式

⚠️ **注意事项**
- 网关编号一旦保存不可修改
- IP地址不能与现有网关重复
- 建议使用有意义的网关名称便于管理

### 3.4 编辑网关

#### 操作步骤
📋 **操作步骤**：
1. 在网关列表中找到要编辑的网关
2. 点击该网关行的"编辑"按钮
3. 在弹出的表单中修改网关信息
4. 点击"确认"按钮保存修改

#### 可编辑字段
- **网关名称**：可以修改网关名称
- **网关类型**：可以修改网关类型
- **IP地址**：可以修改IP地址
- **端口号**：可以修改端口号
- **MAC地址**：可以修改MAC地址
- **固件版本**：可以更新固件版本
- **安装位置**：可以修改安装位置
- **负责人**：可以修改负责人
- **备注信息**：可以修改备注

#### 不可编辑字段
- **网关编号**：网关编号不可修改
- **创建时间**：创建时间不可修改
- **最后通信时间**：由系统自动更新

💡 **提示**
- 修改IP地址后需要重新配置网关
- 建议在维护窗口期间进行重要参数修改
- 修改后系统会自动记录操作日志

### 3.5 删除网关

#### 操作步骤
📋 **操作步骤**：
1. 在网关列表中找到要删除的网关
2. 点击该网关行的"删除"按钮
3. 在确认对话框中点击"确定"删除

#### 删除条件
- **离线状态**：只能删除离线状态的网关
- **无关联设备**：网关下不能有关联的设备
- **无历史数据**：建议清理历史数据后再删除

#### 删除影响
- **数据清理**：网关相关的配置数据将被清理
- **历史记录**：历史通信记录将被保留
- **设备影响**：关联设备需要重新配置网关

⚠️ **注意事项**
- 删除操作不可恢复，请谨慎操作
- 删除前请确认网关确实不再使用
- 建议先备份相关配置信息

### 3.6 网关状态管理

#### 状态监控
- **实时状态**：实时显示网关的连接状态
- **通信质量**：显示网关的通信质量指标
- **数据流量**：显示网关的数据传输量
- **错误统计**：显示网关的错误次数

#### 状态操作
- **重启网关**：远程重启网关设备
- **重置配置**：重置网关配置参数
- **固件升级**：远程升级网关固件
- **诊断测试**：执行网关诊断测试

#### 报警设置
- **离线报警**：网关离线时自动报警
- **异常报警**：网关异常时自动报警
- **通信报警**：通信质量差时报警
- **流量报警**：数据流量异常时报警

💡 **提示**
- 定期检查网关状态确保系统正常运行
- 设置合适的报警阈值避免误报
- 建立网关维护计划定期维护

## 第四章 设备配置

### 4.1 设备管理概述

设备管理是物联网平台的核心功能，通过设备类型管理、设备分组管理和设备管理三个层次，实现对所有接入设备的统一管理和监控。

#### 管理层次
- **设备类型管理**：定义设备的分类和属性模板
- **设备分组管理**：按业务需求对设备进行分组
- **设备管理**：管理具体的设备实例

#### 设备生命周期
1. **设备注册**：将设备信息录入系统
2. **设备配置**：配置设备参数和属性
3. **设备监控**：实时监控设备状态和数据
4. **设备维护**：设备故障处理和维护
5. **设备退役**：设备报废和数据清理

### 4.2 设备类型管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"设备配置"
2. 点击"设备类型管理"进入设备类型列表

#### 设备类型作用
- **分类管理**：对设备进行分类管理
- **属性模板**：定义设备的标准属性
- **配置模板**：提供设备配置模板
- **数据模型**：定义设备数据模型

#### 新增设备类型
📋 **操作步骤**：
1. 点击"新增设备类型"按钮
2. 填写设备类型信息：
   - **类型编码** *：唯一标识，如"SENSOR_TEMP"
   - **类型名称** *：中文名称，如"温度传感器"
   - **设备厂商**：设备制造商
   - **通信协议**：设备使用的通信协议
   - **数据格式**：设备数据格式
   - **属性定义**：定义设备属性列表
3. 点击"确认"保存

#### 编辑设备类型
- 可修改类型名称、厂商、协议等信息
- 可添加或修改设备属性定义
- 不可修改类型编码

#### 删除设备类型
⚠️ **注意事项**：
- 只能删除未被使用的设备类型
- 删除前需确认没有设备使用该类型
- 删除操作不可恢复

### 4.3 设备分组管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"设备配置"
2. 点击"设备分组管理"进入分组列表

#### 分组作用
- **业务分类**：按业务功能对设备分组
- **权限管理**：基于分组的权限控制
- **批量操作**：对分组内设备进行批量操作
- **统计分析**：按分组进行数据统计

#### 新增设备分组
📋 **操作步骤**：
1. 点击"新增分组"按钮
2. 填写分组信息：
   - **分组编码** *：唯一标识
   - **分组名称** *：中文名称
   - **上级分组**：选择上级分组（可选）
   - **负责人**：分组负责人
   - **描述**：分组描述
3. 点击"确认"保存

#### 分组层级
- 支持多级分组结构
- 最多支持5级分组
- 子分组继承父分组的权限

#### 分组操作
- **编辑分组**：修改分组信息
- **删除分组**：删除空分组
- **移动设备**：在分组间移动设备
- **权限设置**：设置分组访问权限

### 4.4 设备管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"设备配置"
2. 点击"设备管理"进入设备列表

#### 设备列表信息
- **设备编号**：设备唯一标识
- **设备名称**：设备中文名称
- **设备类型**：所属设备类型
- **所属分组**：设备所在分组
- **连接状态**：在线/离线状态
- **最后上报时间**：最近数据上报时间
- **操作**：查看、编辑、删除等操作

#### 新增设备
📋 **操作步骤**：
1. 点击"新增设备"按钮
2. 填写设备基本信息：
   - **设备编号** *：设备唯一标识
   - **设备名称** *：设备中文名称
   - **设备类型** *：选择设备类型
   - **所属分组** *：选择设备分组
   - **所属网关**：选择接入网关
3. 填写设备详细信息：
   - **设备型号**：设备具体型号
   - **序列号**：设备序列号
   - **安装位置**：设备安装位置
   - **负责人**：设备维护负责人
4. 配置设备参数：
   - **通信参数**：IP地址、端口等
   - **采集参数**：采集频率、数据格式等
   - **报警参数**：报警阈值、报警方式等
5. 点击"确认"保存

#### 设备状态
- **🟢 在线**：设备正常连接，数据正常
- **🔴 离线**：设备断开连接
- **🟡 异常**：设备连接异常或数据异常
- **🔵 维护**：设备处于维护状态

#### 设备操作
- **查看详情**：查看设备详细信息和实时数据
- **编辑设备**：修改设备配置信息
- **删除设备**：删除设备记录
- **远程控制**：发送控制指令到设备
- **参数配置**：修改设备运行参数
- **固件升级**：远程升级设备固件

### 4.5 设备监控

#### 实时监控
- **状态监控**：实时显示设备连接状态
- **数据监控**：实时显示设备上报的数据
- **性能监控**：监控设备性能指标
- **报警监控**：显示设备报警信息

#### 历史数据
- **数据查询**：查询设备历史数据
- **数据导出**：导出历史数据到Excel
- **数据分析**：对历史数据进行统计分析
- **趋势分析**：分析数据变化趋势

#### 设备控制
- **远程开关**：远程控制设备开关
- **参数设置**：远程设置设备参数
- **指令下发**：向设备下发控制指令
- **批量控制**：对多个设备进行批量控制

💡 **提示**
- 定期检查设备状态确保正常运行
- 合理设置数据采集频率避免网络拥塞
- 建立设备维护计划定期维护设备

## 第六章 台账管理

### 6.1 台账管理概述

台账管理是资产管理的核心功能，通过台账记录管理和资产类型管理，实现对所有资产的全生命周期管理。系统支持资产的新增、编辑、查看、删除，以及导入导出、到期提醒等功能。

#### 台账管理价值
- **资产追踪**：完整记录资产从采购到报废的全过程
- **成本控制**：准确掌握资产成本和折旧情况
- **合规管理**：满足财务和审计的合规要求
- **决策支持**：为资产采购和更新提供数据支持

#### 管理功能
- **台账记录管理**：管理具体的资产台账记录
- **资产类型管理**：管理资产分类和类型定义
- **导入导出**：支持Excel格式的批量导入导出
- **到期管理**：智能的到期提醒和续期管理

### 6.2 台账记录管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"台账管理"
2. 点击"台账记录"进入台账列表页面

#### 台账列表信息
台账列表显示以下关键信息：
- **序号**：记录序号
- **资产编号**：资产的唯一标识
- **资产名称**：资产的中文名称
- **资产类型**：资产所属类型
- **状态**：正常/维修/报废
- **所属部门**：资产归属部门
- **到期日期**：资产到期时间（重要！）
- **操作**：查看、编辑、删除等操作

#### 到期日期显示说明
系统会根据到期时间智能显示状态：
- **🔴 已过期/今天到期**：红色显示，需要立即处理
- **🟠 7天内到期**：橙色显示，需要尽快处理
- **🟡 30天内到期**：黄色显示，需要关注
- **⚫ 正常状态**：灰色显示，暂无需处理
- **⚪ 未设置**：浅灰色显示，需要补充信息

#### 新增台账记录
📋 **操作步骤**：
1. 点击"新增台账"按钮
2. 填写基本信息：
   - **资产编号** *：唯一标识，如"AS2024001"
   - **资产名称** *：中文名称，如"服务器-Dell R740"
   - **资产类型** *：选择资产类型
   - **状态** *：选择资产状态
   - **所属部门** *：选择归属部门
3. 填写归属信息：
   - **存放位置**：具体存放位置
   - **责任人**：资产负责人
4. 填写采购信息：
   - **采购日期**：采购时间
   - **采购价格**：采购金额
   - **供应商**：供应商名称
5. 填写到期信息：
   - **到期时间** *：资产到期日期（重要！）
   - **到期提前通知** *：提前几天通知（1-30天）
6. 填写备注信息
7. 点击"确认"保存

⚠️ **注意事项**
- 资产编号一旦保存不可修改
- 到期时间和提前通知天数是必填项
- 到期提前通知天数必须在1-30天之间

#### 编辑台账记录
📋 **操作步骤**：
1. 在台账列表中找到要编辑的记录
2. 点击"编辑"按钮
3. 修改相关信息（资产编号不可修改）
4. 点击"确认"保存修改

#### 查看台账详情
📋 **操作步骤**：
1. 在台账列表中点击"查看"按钮
2. 在详情抽屉中查看完整信息：
   - **基本信息**：资产编号、名称、类型、状态
   - **归属信息**：部门、位置、责任人
   - **采购信息**：采购日期、价格、供应商、到期时间
   - **时间信息**：创建时间、更新时间
   - **备注信息**：相关说明
3. 可直接从详情页面进入编辑模式
4. 支持打印详情功能

#### 删除台账记录
📋 **操作步骤**：
1. 在台账列表中找到要删除的记录
2. 点击"删除"按钮
3. 在确认对话框中确认删除

⚠️ **注意事项**
- 删除操作不可恢复
- 建议先导出备份再删除
- 重要资产删除需要审批

#### 搜索和筛选
- **关键词搜索**：按资产编号、名称搜索
- **类型筛选**：按资产类型筛选
- **状态筛选**：按资产状态筛选
- **部门筛选**：按所属部门筛选
- **到期筛选**：按到期状态筛选

### 6.3 资产类型管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"台账管理"
2. 点击"资产类型管理"进入类型管理页面

#### 资产类型作用
- **分类管理**：对资产进行标准化分类
- **数据规范**：统一资产数据标准
- **报表统计**：按类型进行统计分析
- **权限控制**：基于类型的权限管理

#### 预置资产类型
系统预置了常用的资产类型：
- **服务器设备** (SERVER)：各类服务器、存储设备
- **网络设备** (NETWORK)：交换机、路由器、防火墙
- **办公设备** (OFFICE)：电脑、打印机、投影仪
- **安防设备** (SECURITY)：监控摄像头、门禁系统
- **其他设备** (OTHER)：其他类型设备

#### 新增资产类型
📋 **操作步骤**：
1. 点击"新增类型"按钮
2. 填写类型信息：
   - **类型编码** *：唯一标识，如"FURNITURE"
   - **类型名称** *：中文名称，如"办公家具"
   - **状态** *：启用/禁用
   - **描述**：类型说明
3. 点击"确认"保存

#### 编辑资产类型
- 可修改类型名称、状态、描述
- 不可修改类型编码
- 可启用/禁用类型

#### 删除资产类型
⚠️ **注意事项**
- 只能删除未被使用的类型
- 删除前需确认没有台账使用该类型
- 建议先禁用再删除

### 6.4 台账导入导出

#### 导入功能
📋 **操作步骤**：
1. 点击"导入Excel"按钮
2. 选择要导入的Excel文件（支持.xlsx、.xls、.csv格式）
3. 系统解析文件并显示导入预览
4. 确认导入数据无误后点击"确认导入"
5. 系统显示导入结果

#### 导入要求
- **文件大小**：不超过5MB
- **数据格式**：按照标准模板格式
- **必填字段**：资产编号、名称、类型等必填字段不能为空
- **数据验证**：系统会验证数据格式和重复性

#### 导出功能
📋 **操作步骤**：
1. 在台账列表页面点击"导出Excel"按钮
2. 系统生成包含所有字段的Excel文件
3. 文件自动下载到本地

#### 导出内容
导出的Excel文件包含以下字段：
- 序号、资产编号、资产名称、资产类型、状态
- 所属部门、存放位置、责任人、采购日期、采购价格
- 供应商、到期日期、到期提前通知(天)、创建时间、更新时间、备注

### 6.5 到期管理

#### 到期提醒机制
系统提供智能的到期提醒功能：
- **自动计算**：自动计算距离到期的天数
- **分级提醒**：根据紧急程度使用不同颜色提醒
- **提前通知**：按设置的提前天数进行通知
- **状态跟踪**：跟踪到期处理状态

#### 到期处理流程
1. **提前通知**：系统按设置的天数提前通知
2. **状态标识**：在列表中用颜色标识到期状态
3. **处理跟进**：负责人处理到期事项
4. **状态更新**：更新处理结果和新的到期时间

#### 到期统计
- **即将到期**：统计即将到期的资产数量
- **已过期**：统计已过期的资产数量
- **处理率**：统计到期处理的完成率
- **趋势分析**：分析到期趋势变化

💡 **提示**
- 定期检查到期提醒确保及时处理
- 合理设置提前通知天数
- 建立到期处理标准流程
- 定期更新资产到期时间

## 第十二章 系统配置

### 12.1 系统管理概述

系统配置是平台管理的核心模块，包括用户管理、部门管理、角色管理和操作日志管理。通过系统配置，管理员可以控制用户权限、组织架构、系统安全和操作审计。

#### 管理功能
- **用户管理**：管理系统用户账户和基本信息
- **部门管理**：管理组织架构和部门层级
- **角色管理**：管理用户角色和权限分配
- **操作日志**：记录和查询系统操作日志

#### 权限体系
系统采用基于角色的权限控制（RBAC）：
- **用户**：系统的使用者
- **角色**：权限的集合
- **权限**：具体的操作权限
- **部门**：组织架构单元

### 12.2 用户管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"用户管理"进入用户列表页面

#### 用户列表信息
- **用户编号**：用户唯一标识
- **用户名**：登录用户名
- **姓名**：用户真实姓名
- **所属部门**：用户归属部门
- **角色**：用户角色
- **状态**：启用/禁用状态
- **最后登录时间**：最近登录时间
- **操作**：编辑、删除、重置密码等

#### 新增用户
📋 **操作步骤**：
1. 点击"新增用户"按钮
2. 填写基本信息：
   - **用户编号** *：唯一标识
   - **用户名** *：登录用户名
   - **姓名** *：真实姓名
   - **密码** *：登录密码
   - **确认密码** *：再次输入密码
3. 填写详细信息：
   - **所属部门** *：选择部门
   - **角色** *：选择用户角色
   - **邮箱**：邮箱地址
   - **手机号**：手机号码
   - **状态** *：启用/禁用
4. 点击"确认"保存

#### 密码要求
- **长度**：8-20位字符
- **复杂度**：包含大小写字母、数字
- **特殊字符**：建议包含特殊字符
- **有效期**：密码有效期90天

#### 用户操作
- **编辑用户**：修改用户基本信息
- **重置密码**：管理员重置用户密码
- **启用/禁用**：控制用户账户状态
- **删除用户**：删除用户账户（谨慎操作）

⚠️ **注意事项**
- 用户名一旦创建不可修改
- 删除用户会影响相关操作记录
- 建议先禁用用户再考虑删除

### 12.3 部门管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"部门管理"进入部门管理页面

#### 部门层级结构
系统支持多级部门结构：
- **总公司**
  - **分公司A**
    - **技术部**
    - **市场部**
    - **财务部**
  - **分公司B**
    - **运营部**
    - **客服部**

#### 新增部门
📋 **操作步骤**：
1. 点击"新增部门"按钮
2. 填写部门信息：
   - **部门编码** *：唯一标识
   - **部门名称** *：中文名称
   - **上级部门**：选择上级部门（可选）
   - **部门负责人**：选择负责人
   - **联系电话**：部门电话
   - **部门描述**：部门说明
3. 点击"确认"保存

#### 部门操作
- **编辑部门**：修改部门信息
- **删除部门**：删除空部门
- **移动部门**：调整部门层级关系
- **设置负责人**：指定部门负责人

### 12.4 角色管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"角色管理"进入角色管理页面

#### 预置角色
系统预置了常用角色：
- **系统管理员**：拥有所有权限
- **设备管理员**：设备相关权限
- **资产管理员**：台账相关权限
- **普通用户**：基本查看权限

#### 新增角色
📋 **操作步骤**：
1. 点击"新增角色"按钮
2. 填写角色信息：
   - **角色编码** *：唯一标识
   - **角色名称** *：中文名称
   - **角色描述**：角色说明
3. 配置权限：
   - **功能权限**：选择可访问的功能模块
   - **数据权限**：选择可访问的数据范围
   - **操作权限**：选择可执行的操作类型
4. 点击"确认"保存

#### 权限分类
- **查看权限**：查看数据的权限
- **新增权限**：添加数据的权限
- **编辑权限**：修改数据的权限
- **删除权限**：删除数据的权限
- **导入权限**：导入数据的权限
- **导出权限**：导出数据的权限

### 12.5 操作日志管理

#### 访问路径
📋 **操作步骤**：
1. 点击左侧导航栏"系统配置"
2. 点击"操作日志"进入日志查询页面

#### 日志记录内容
系统自动记录以下操作：
- **新增操作**：创建新记录的操作
- **修改操作**：修改现有记录的操作
- **删除操作**：删除记录的操作
- **导入操作**：批量导入数据的操作
- **导出操作**：导出数据的操作

#### 日志信息字段
- **操作人**：执行操作的用户
- **操作类型**：操作的类型分类
- **操作对象**：操作的目标对象
- **操作时间**：操作执行的时间
- **操作详情**：操作的详细描述

#### 日志查询
📋 **操作步骤**：
1. 设置查询条件：
   - **操作人**：选择操作用户
   - **操作类型**：选择操作类型
   - **时间范围**：设置开始和结束时间
2. 点击"查询"按钮
3. 查看查询结果

#### 日志分析
- **操作统计**：统计各类操作的数量
- **用户行为**：分析用户的操作行为
- **异常检测**：检测异常的操作行为
- **审计报告**：生成操作审计报告

#### 日志导出
- 支持将查询结果导出为Excel文件
- 包含完整的日志信息
- 支持按时间范围批量导出

💡 **提示**
- 定期检查操作日志确保系统安全
- 关注异常操作和失败操作
- 建立日志分析和审计制度
- 定期备份重要操作日志

⚠️ **安全建议**
- 定期修改管理员密码
- 合理分配用户权限
- 监控异常登录行为
- 建立安全操作规范

## 第十四章 常见问题和故障排查

### 14.1 常见问题FAQ

#### 登录相关问题

**Q1: 无法登录系统，提示用户名或密码错误？**
A: 请检查以下几点：
- 确认用户名和密码输入正确
- 检查大小写是否正确
- 确认账户未被锁定或禁用
- 联系管理员重置密码

**Q2: 登录后页面显示异常或功能无法使用？**
A: 请尝试以下解决方案：
- 清除浏览器缓存和Cookie
- 使用Chrome、Firefox等主流浏览器
- 检查网络连接是否稳定
- 确认浏览器启用了JavaScript

**Q3: 忘记密码怎么办？**
A: 请联系系统管理员重置密码，或使用密码重置功能（如果已配置）。

#### 数据操作问题

**Q4: 新增数据时提示字段验证错误？**
A: 请检查：
- 必填字段是否已填写（标有红色*号）
- 数据格式是否正确（如邮箱、电话号码格式）
- 唯一字段是否重复（如编号、用户名等）
- 数字字段是否在有效范围内

**Q5: 导入Excel文件失败？**
A: 请确认：
- 文件格式为.xlsx、.xls或.csv
- 文件大小不超过5MB
- 数据格式符合模板要求
- 必填字段不能为空
- 编号等唯一字段不能重复

**Q6: 删除数据时提示无法删除？**
A: 可能原因：
- 数据被其他记录引用
- 没有删除权限
- 数据处于特殊状态（如正在使用中）

#### 设备管理问题

**Q7: 设备显示离线状态？**
A: 请检查：
- 设备电源是否正常
- 网络连接是否正常
- 网关是否在线
- 设备配置是否正确

**Q8: 设备数据不更新？**
A: 可能原因：
- 设备故障或离线
- 网关通信异常
- 数据采集配置错误
- 网络延迟或中断

#### 权限相关问题

**Q9: 提示没有权限访问某个功能？**
A: 请联系管理员：
- 确认用户角色是否正确
- 检查角色权限配置
- 确认用户账户状态正常

### 14.2 故障排查指南

#### 系统性能问题

**症状：页面加载缓慢**
排查步骤：
1. 检查网络连接速度
2. 清除浏览器缓存
3. 检查服务器负载
4. 优化数据查询条件

**症状：操作响应慢**
排查步骤：
1. 检查数据量是否过大
2. 优化搜索和筛选条件
3. 分批处理大量数据
4. 联系技术支持

#### 数据同步问题

**症状：数据显示不一致**
排查步骤：
1. 刷新页面重新加载数据
2. 检查数据更新时间
3. 确认操作权限
4. 检查网络连接稳定性

**症状：实时数据延迟**
排查步骤：
1. 检查设备连接状态
2. 确认网关通信正常
3. 检查数据采集频率设置
4. 排查网络延迟问题

#### 浏览器兼容性问题

**推荐浏览器版本：**
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

**不支持的浏览器：**
- Internet Explorer（所有版本）
- 过旧版本的浏览器

### 14.3 错误代码说明

#### 系统错误代码

| 错误代码 | 错误描述 | 解决方案 |
|---------|---------|---------|
| 1001 | 用户名或密码错误 | 检查登录凭据 |
| 1002 | 账户被锁定 | 联系管理员解锁 |
| 1003 | 会话超时 | 重新登录 |
| 2001 | 数据验证失败 | 检查输入数据格式 |
| 2002 | 数据重复 | 修改重复的字段值 |
| 2003 | 数据不存在 | 确认数据是否已删除 |
| 3001 | 权限不足 | 联系管理员分配权限 |
| 3002 | 操作被拒绝 | 检查操作权限 |
| 4001 | 网络连接错误 | 检查网络连接 |
| 4002 | 服务器错误 | 联系技术支持 |
| 5001 | 文件格式错误 | 使用正确的文件格式 |
| 5002 | 文件过大 | 减小文件大小 |

### 14.4 技术支持

#### 联系方式
- **技术支持热线**：400-XXX-XXXX
- **技术支持邮箱**：<EMAIL>
- **在线客服**：工作日 9:00-18:00
- **远程支持**：预约远程协助

#### 支持服务
- **问题咨询**：功能使用咨询
- **故障排查**：技术故障诊断
- **培训服务**：用户培训和指导
- **定制开发**：个性化功能开发

#### 服务时间
- **标准支持**：工作日 9:00-18:00
- **紧急支持**：7×24小时（重要故障）
- **响应时间**：一般问题4小时内响应
- **解决时间**：根据问题复杂度确定

---

## 第十五章 附录

### 15.1 术语表

| 术语 | 英文 | 说明 |
|------|------|------|
| 物联网 | IoT | Internet of Things，物联网 |
| 网关 | Gateway | 设备接入的桥梁设备 |
| 传感器 | Sensor | 数据采集设备 |
| 执行器 | Actuator | 控制执行设备 |
| 协议 | Protocol | 通信协议标准 |
| 台账 | Ledger | 资产管理记录 |
| 工单 | Work Order | 工作任务单据 |
| 报警 | Alarm | 异常情况通知 |
| 权限 | Permission | 操作授权 |
| 角色 | Role | 权限集合 |

### 15.2 参考资料

#### 技术文档
- 《物联网平台技术架构说明》
- 《设备接入协议规范》
- 《数据格式标准文档》
- 《API接口文档》

#### 相关标准
- GB/T 33745-2017 物联网术语
- GB/T 33474-2016 物联网参考体系结构
- ISO/IEC 30141:2018 IoT Reference Architecture

#### 学习资源
- 官方培训视频
- 在线帮助文档
- 用户社区论坛
- 技术博客文章

### 15.3 版本更新记录

#### v1.0 (2024-12-01)
- 初始版本发布
- 完整的功能模块
- 基础操作手册

#### 后续版本规划
- v1.1：增加移动端支持
- v1.2：增强数据分析功能
- v1.3：集成更多设备协议
- v2.0：大数据和AI功能

### 15.4 联系方式

#### 公司信息
- **公司名称**：智慧物联科技有限公司
- **公司地址**：北京市海淀区中关村大街XX号
- **邮政编码**：100080
- **公司网站**：www.company.com

#### 联系方式
- **销售咨询**：<EMAIL>
- **技术支持**：<EMAIL>
- **商务合作**：<EMAIL>
- **客服热线**：400-XXX-XXXX

#### 社交媒体
- **官方微信**：智慧物联平台
- **技术QQ群**：XXXXXXXXX
- **官方微博**：@智慧物联平台
- **LinkedIn**：Company IoT Platform

---

## 📝 手册结语

感谢您使用智慧物联网平台！本手册详细介绍了平台的各项功能和操作方法，希望能够帮助您快速掌握平台的使用技巧，提高工作效率。

如果您在使用过程中遇到任何问题，请随时联系我们的技术支持团队。我们将持续改进产品功能和用户体验，为您提供更好的服务。

**祝您使用愉快！**

---

*本手册版权归智慧物联科技有限公司所有，未经许可不得复制或传播。*
*手册内容如有更新，请以最新版本为准。*
*最后更新时间：2024年12月*
