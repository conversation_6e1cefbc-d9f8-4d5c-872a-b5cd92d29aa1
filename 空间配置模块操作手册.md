# 空间配置模块操作手册

## 目录

### 第一章 空间配置概述
- 1.1 空间配置介绍
- 1.2 功能说明
- 1.3 使用场景

### 第二章 空间类型管理
- 2.1 空间类型概述
- 2.2 空间类型列表查看
- 2.3 新增空间类型
- 2.4 编辑空间类型
- 2.5 删除空间类型

### 第三章 空间管理
- 3.1 空间管理概述
- 3.2 空间列表查看
- 3.3 新增空间
- 3.4 编辑空间
- 3.5 删除空间

### 第四章 空间层级管理
- 4.1 层级结构说明
- 4.2 层级关系管理
- 4.3 层级权限控制

### 第五章 空间设备关联
- 5.1 设备关联说明
- 5.2 设备分配操作
- 5.3 关联关系管理

### 第六章 常见问题和注意事项
- 6.1 常见问题
- 6.2 注意事项
- 6.3 最佳实践

---

## 第一章 空间配置概述

### 1.1 空间配置介绍

空间配置是物联网平台的基础功能模块，用于管理物理空间的层级结构和逻辑关系。通过空间配置，可以建立完整的空间层级体系，实现设备的空间定位和管理。

#### 主要功能
- **空间类型管理**：定义和管理各种空间类型
- **空间管理**：创建和管理具体的空间实例  
- **层级结构**：建立多级空间层级关系
- **设备关联**：管理空间与设备的关联关系

#### 空间类型
- **楼栋**：建筑物主体结构
- **楼层**：建筑物内的楼层
- **房间**：具体的房间空间
- **区域**：功能性区域划分
- **车间**：工业生产车间
- **办公室**：办公工作空间

### 1.2 功能说明

#### 空间类型管理
- **类型定义**：定义空间类型和基本属性
- **类型编辑**：修改空间类型信息
- **类型删除**：删除不需要的空间类型
- **状态管理**：启用或禁用空间类型

#### 空间管理
- **空间创建**：基于类型创建具体空间
- **空间编辑**：修改空间基本信息
- **空间删除**：删除不需要的空间
- **层级管理**：管理空间的层级关系

### 1.3 使用场景

#### 智慧楼宇
- **层级结构**：楼栋 → 楼层 → 房间 → 区域
- **应用场景**：办公楼、商业楼宇、住宅楼等
- **设备管理**：按空间位置管理各类设备

#### 工业园区  
- **层级结构**：园区 → 厂区 → 车间 → 工位
- **应用场景**：制造工厂、物流园区等
- **生产管理**：按空间区域管理生产设备

#### 医疗机构
- **层级结构**：院区 → 楼栋 → 科室 → 病房
- **应用场景**：医院、诊所、康复中心等
- **设备配置**：按科室管理医疗设备

---

## 第二章 空间类型管理

### 2.1 空间类型概述

空间类型管理是空间配置的基础功能，用于定义和管理各种空间类型。通过空间类型，可以为不同用途的空间定义标准的属性和配置，确保空间数据的规范性和一致性。

#### 空间类型功能
- **分类管理**：对空间进行标准化分类
- **属性定义**：定义空间的标准属性
- **状态管理**：启用或禁用空间类型
- **应用管理**：管理类型的使用情况

#### 预置类型
系统预置了常用的空间类型：
- **楼栋** (BUILDING)：建筑物主体结构
- **楼层** (FLOOR)：建筑物内的楼层
- **房间** (ROOM)：具体的房间空间
- **区域** (AREA)：功能性区域划分
- **车间** (WORKSHOP)：工业生产车间
- **办公室** (OFFICE)：办公工作空间

### 2.2 空间类型列表查看

#### 访问路径
📋 操作步骤：
1. 登录系统后，点击左侧导航栏"空间配置"
2. 点击"空间类型管理"进入空间类型列表页面

#### 列表信息
空间类型列表显示以下信息：
- **序号**：记录序号
- **类型编码**：空间类型的唯一标识
- **类型名称**：空间类型的中文名称
- **类型图标**：空间类型的图标标识
- **状态**：启用/禁用状态
- **使用数量**：使用该类型的空间数量
- **创建时间**：类型的创建时间
- **操作**：编辑、删除等操作按钮

#### 状态说明
- **🟢 启用**：类型已启用，可以创建该类型的空间
- **🔴 禁用**：类型已禁用，暂停使用
- **📊 使用中**：有空间实例正在使用该类型
- **📝 未使用**：暂无空间实例使用该类型

#### 搜索筛选
- **关键词搜索**：输入类型名称或编码进行搜索
- **状态筛选**：按启用状态筛选类型
- **使用情况筛选**：按使用情况筛选类型
- **时间筛选**：按创建时间筛选

### 2.3 新增空间类型

#### 操作步骤
📋 操作步骤：
1. 在空间类型列表页面，点击"新增类型"按钮
2. 在弹出的表单中填写类型信息
3. 点击"确认"按钮保存类型信息

#### 必填字段
- **类型编码** *：空间类型的唯一标识，不可重复
- **类型名称** *：空间类型的中文名称
- **类型图标** *：选择代表该类型的图标
- **状态** *：设置类型的启用状态

#### 可选字段
- **上级类型**：选择上级空间类型（建立层级关系）
- **类型描述**：详细描述该类型空间的用途和特点
- **排序权重**：设置类型在列表中的排序权重
- **颜色标识**：设置类型的颜色标识

#### 验证规则
- **类型编码**：只能包含字母、数字和下划线
- **类型名称**：不能与现有类型重复
- **图标选择**：必须选择一个有效的图标
- **状态设置**：必须选择启用或禁用状态

⚠️ 注意事项
- 类型编码一旦保存不可修改
- 类型名称不能与现有类型重复
- 建议使用有意义的类型名称便于管理

### 2.4 编辑空间类型

#### 操作步骤
📋 操作步骤：
1. 在空间类型列表中找到要编辑的类型
2. 点击该类型行的"编辑"按钮
3. 在弹出的表单中修改类型信息
4. 点击"确认"按钮保存修改

#### 可编辑字段
- **类型名称**：可以修改类型名称
- **类型图标**：可以更换类型图标
- **类型描述**：可以更新类型描述
- **状态**：可以启用或禁用类型
- **上级类型**：可以调整上级类型关系
- **排序权重**：可以修改排序权重
- **颜色标识**：可以修改颜色标识

#### 不可编辑字段
- **类型编码**：类型编码不可修改
- **创建时间**：创建时间不可修改
- **使用数量**：由系统自动统计

💡 提示
- 修改类型信息可能影响已创建的空间
- 禁用类型会影响该类型空间的创建
- 修改后系统会自动记录操作日志

### 2.5 删除空间类型

#### 操作步骤
📋 操作步骤：
1. 在空间类型列表中找到要删除的类型
2. 点击该类型行的"删除"按钮
3. 在确认对话框中点击"确定"删除

#### 删除条件
- **未使用状态**：该类型没有被任何空间使用
- **无子类型**：该类型没有子类型依赖
- **管理权限**：用户必须有删除权限

#### 删除影响
- **配置清理**：类型相关的配置数据将被清理
- **历史记录**：类型的历史记录将被保留
- **关联检查**：系统会检查是否有关联的空间

⚠️ 注意事项
- 删除操作不可恢复，请谨慎操作
- 删除前请确认类型确实不再使用
- 建议先禁用类型观察一段时间再删除

---

## 第三章 空间管理

### 3.1 空间管理概述

空间管理是空间配置模块的核心功能，用于创建和管理具体的空间实例。基于已定义的空间类型，可以创建实际的空间对象，建立空间层级关系，并为空间分配设备和资源。

#### 空间管理功能
- **空间创建**：基于空间类型创建具体空间实例
- **空间编辑**：修改空间的基本信息和配置
- **空间删除**：删除不再需要的空间实例
- **状态管理**：管理空间的使用状态
- **层级管理**：管理空间间的层级关系

#### 空间状态
- **🟢 使用中**：空间正常使用，有设备或人员
- **🟡 空闲**：空间暂时空闲，可以分配使用
- **🔵 维护**：空间正在维护，暂停使用
- **🔴 停用**：空间已停用，不可使用

### 3.2 空间列表查看

#### 访问路径
📋 操作步骤：
1. 登录系统后，点击左侧导航栏"空间配置"
2. 点击"空间管理"进入空间管理页面

#### 列表信息
空间列表显示以下信息：
- **序号**：记录序号
- **空间编码**：空间的唯一标识
- **空间名称**：空间的中文名称
- **空间类型**：所属的空间类型
- **上级空间**：父级空间名称
- **状态**：使用中/空闲/维护/停用
- **面积**：空间的面积信息
- **设备数量**：关联的设备数量
- **负责人**：空间的管理负责人
- **操作**：查看、编辑、删除等操作

#### 视图模式
- **树形视图**：以树形结构展示空间层级关系
- **列表视图**：以表格形式展示空间详细信息
- **卡片视图**：以卡片形式展示空间概要信息

#### 搜索筛选
- **关键词搜索**：输入空间名称或编码进行搜索
- **类型筛选**：按空间类型筛选
- **状态筛选**：按使用状态筛选
- **层级筛选**：按空间层级筛选
- **负责人筛选**：按负责人筛选

### 3.3 新增空间

#### 操作步骤
📋 操作步骤：
1. 在空间管理页面，点击"新增空间"按钮
2. 在弹出的表单中填写空间信息
3. 点击"确认"按钮保存空间信息

#### 必填字段
- **空间编码** *：空间的唯一标识，不可重复
- **空间名称** *：空间的中文名称
- **空间类型** *：选择预定义的空间类型
- **上级空间** *：选择父级空间（根空间可不选）
- **状态** *：设置空间的初始状态

#### 可选字段
- **空间描述**：详细描述空间的用途和特点
- **物理位置**：空间的具体物理位置描述
- **面积信息**：空间的面积（平方米）
- **容量信息**：空间的人员或设备容量
- **负责人**：空间的管理负责人
- **联系方式**：负责人的联系方式

#### 验证规则
- **空间编码**：只能包含字母、数字和下划线
- **空间名称**：不能与现有空间重复
- **上级空间**：不能选择自己作为上级空间
- **面积信息**：必须是正数

⚠️ 注意事项
- 空间编码一旦保存不可修改
- 上级空间关系要符合实际层级结构
- 建议使用有意义的空间名称便于管理

### 3.4 编辑空间

#### 操作步骤
📋 操作步骤：
1. 在空间列表中找到要编辑的空间
2. 点击该空间行的"编辑"按钮
3. 在弹出的表单中修改空间信息
4. 点击"确认"按钮保存修改

#### 可编辑字段
- **空间名称**：可以修改空间名称
- **空间描述**：可以更新空间描述
- **物理位置**：可以调整物理位置信息
- **面积容量**：可以修改面积和容量信息
- **负责人**：可以更换负责人
- **状态**：可以更改空间状态
- **联系方式**：可以修改联系方式

#### 不可编辑字段
- **空间编码**：空间编码不可修改
- **空间类型**：空间类型不可修改
- **创建时间**：创建时间不可修改

#### 空间移动
如需调整空间的上级关系：
📋 操作步骤：
1. 选择要移动的空间
2. 点击"移动空间"按钮
3. 选择新的上级空间
4. 确认移动操作

💡 提示
- 移动空间可能影响权限继承
- 移动空间可能影响设备关联关系
- 不能移动到自己的子空间

### 3.5 删除空间

#### 操作步骤
📋 操作步骤：
1. 在空间列表中找到要删除的空间
2. 点击该空间行的"删除"按钮
3. 在确认对话框中点击"确定"删除

#### 删除条件
- **无子空间**：空间没有子空间
- **无关联设备**：空间内没有关联的设备
- **无业务流程**：空间没有正在进行的业务流程
- **管理权限**：用户必须有删除权限

#### 删除影响
- **数据清理**：空间相关的配置数据将被清理
- **历史记录**：空间的历史记录将被保留
- **权限影响**：删除会影响基于该空间的权限

⚠️ 注意事项
- 删除操作不可恢复，请谨慎操作
- 删除前请确认空间确实不再使用
- 建议先清理空间内的设备和子空间

---

## 第四章 空间层级管理

### 4.1 层级结构说明

空间层级结构是空间配置的核心特性，通过建立空间间的父子关系，形成树形的层级体系。合理的层级结构能够清晰地反映物理空间的包含关系，便于空间管理、权限控制和数据统计。

#### 层级结构特点
- **树形结构**：采用树形结构组织空间关系
- **单一父级**：每个空间只能有一个直接父级
- **多级嵌套**：支持多级空间嵌套关系
- **动态调整**：支持层级关系的动态调整

#### 典型层级结构
**智慧楼宇层级**
```
园区
├── A栋楼宇
│   ├── 1楼
│   │   ├── 大厅
│   │   └── 办公区A
│   └── 2楼
│       ├── 办公区B
│       └── 会议室B
└── B栋楼宇
    ├── 1楼
    └── 2楼
```

**工业园区层级**
```
工业园区
├── 生产区
│   ├── 车间A
│   │   ├── 生产线1
│   │   └── 生产线2
│   └── 车间B
│       ├── 装配区
│       └── 包装区
├── 办公区
└── 辅助区
    ├── 食堂
    └── 停车场
```

#### 层级设计原则
- **逻辑性**：层级关系要符合实际的物理包含关系
- **一致性**：同级空间的类型和属性要保持一致
- **完整性**：层级结构要完整覆盖所有空间
- **简洁性**：避免过深的层级嵌套

### 4.2 层级关系管理

#### 建立层级关系
📋 操作步骤：
1. 创建根级空间（顶级空间）
2. 在根级空间下创建子空间
3. 为子空间指定正确的上级空间
4. 逐级创建完整的层级结构
5. 验证层级关系的正确性

#### 修改层级关系
📋 操作步骤：
1. 选择要调整的空间
2. 点击"移动空间"功能
3. 选择新的上级空间
4. 确认移动操作
5. 系统自动更新层级关系

#### 层级关系验证
系统自动进行以下验证：
- **循环检查**：防止形成循环引用
- **深度检查**：控制层级深度不超过限制
- **类型检查**：验证父子空间类型的合理性
- **权限检查**：验证用户的操作权限

#### 层级关系展示
- **树形视图**：以树形结构展示完整层级
- **面包屑导航**：显示当前空间的层级路径
- **层级统计**：统计各级空间的数量
- **关系图谱**：可视化展示空间关系

### 4.3 层级权限控制

#### 权限继承机制
- **向下继承**：父空间的权限自动传递给子空间
- **权限叠加**：子空间可以在继承基础上增加权限
- **权限限制**：子空间的权限不能超过父空间
- **权限覆盖**：特殊情况下可以覆盖继承的权限

#### 权限类型
- **访问权限**：进入和查看空间的权限
- **管理权限**：管理空间配置的权限
- **设备权限**：操作空间内设备的权限
- **数据权限**：访问空间数据的权限

#### 权限配置
📋 操作步骤：
1. 在根级空间设置基础权限
2. 各级子空间继承上级权限
3. 根据需要在子空间增加特殊权限
4. 设置权限的有效期和条件
5. 定期审查和调整权限配置

#### 权限冲突处理
- **优先级规则**：明确权限的优先级规则
- **冲突检测**：自动检测权限冲突
- **冲突解决**：提供冲突解决方案
- **权限审计**：记录权限变更和冲突处理

💡 层级管理最佳实践
- 根据实际物理结构设计层级
- 保持层级结构的逻辑性和一致性
- 合理设置权限继承规则
- 定期检查和优化层级结构

⚠️ 注意事项
- 避免过深的层级嵌套（建议不超过6级）
- 防止形成循环引用关系
- 层级调整可能影响权限和数据
- 重要层级变更建议先备份

---

## 第五章 空间设备关联

### 5.1 设备关联说明

空间设备关联是将设备分配到具体空间的功能，通过建立空间与设备的关联关系，可以实现设备的空间定位、按空间管理设备、空间设备统计等功能。

#### 关联功能
- **设备分配**：将设备分配到指定空间
- **关联查看**：查看空间内的设备列表
- **关联修改**：修改设备的空间归属
- **关联删除**：解除设备与空间的关联
- **批量操作**：批量管理设备空间关联

#### 关联类型
- **固定关联**：设备固定安装在某个空间
- **临时关联**：设备临时放置在某个空间
- **移动关联**：设备可以在多个空间间移动
- **虚拟关联**：逻辑上归属某个空间的设备

### 5.2 设备分配操作

#### 单个设备分配
📋 操作步骤：
1. 进入设备管理页面
2. 选择要分配的设备
3. 点击"分配空间"按钮
4. 选择目标空间
5. 确认分配操作

#### 批量设备分配
📋 操作步骤：
1. 在设备列表中勾选多个设备
2. 点击"批量分配空间"按钮
3. 选择目标空间
4. 确认批量分配操作

#### 空间设备查看
📋 操作步骤：
1. 进入空间管理页面
2. 选择要查看的空间
3. 点击"查看设备"按钮
4. 查看空间内的设备列表

#### 设备信息显示
空间设备列表显示以下信息：
- **设备编码**：设备的唯一标识
- **设备名称**：设备的中文名称
- **设备类型**：设备的类型分类
- **设备状态**：在线/离线状态
- **关联时间**：设备分配到空间的时间
- **关联类型**：固定/临时/移动/虚拟
- **操作**：移动、解除关联等操作

### 5.3 关联关系管理

#### 修改设备空间
📋 操作步骤：
1. 在设备列表中找到要移动的设备
2. 点击"移动设备"按钮
3. 选择新的目标空间
4. 填写移动原因
5. 确认移动操作

#### 解除设备关联
📋 操作步骤：
1. 在空间设备列表中找到要解除的设备
2. 点击"解除关联"按钮
3. 填写解除原因
4. 确认解除操作

#### 关联历史记录
系统自动记录设备空间关联的历史：
- **关联时间**：设备分配到空间的时间
- **解除时间**：设备从空间移除的时间
- **操作人员**：执行操作的用户
- **操作原因**：关联或解除的原因
- **关联类型**：关联的类型分类

#### 关联统计分析
- **空间设备统计**：统计各空间的设备数量
- **设备分布分析**：分析设备在空间中的分布
- **关联变更统计**：统计设备空间变更情况
- **空间利用率**：分析空间的设备利用率

💡 设备关联最佳实践
- 及时更新设备的空间位置信息
- 建立设备移动的审批流程
- 定期检查设备空间关联的准确性
- 合理设置设备关联类型

⚠️ 注意事项
- 设备只能关联到一个空间
- 移动设备前确认新空间的容量
- 重要设备移动建议先备案
- 定期核对设备实际位置与系统记录

---

## 第六章 常见问题和注意事项

### 6.1 常见问题

#### 空间类型相关问题

**Q1：为什么无法删除空间类型？**
A：可能的原因：
- 该类型正在被空间使用
- 该类型有子类型依赖
- 用户没有删除权限
- 解决方法：先处理依赖关系，确认权限后再删除

**Q2：空间类型编码可以修改吗？**
A：不可以。空间类型编码一旦创建就不能修改，如需修改请重新创建类型。

**Q3：如何批量创建空间类型？**
A：目前系统不支持批量创建，需要逐个创建空间类型。

#### 空间管理相关问题

**Q4：为什么无法创建子空间？**
A：可能的原因：
- 上级空间不存在或已删除
- 空间编码重复
- 用户没有创建权限
- 解决方法：检查上级空间状态，确认编码唯一性

**Q5：空间移动后权限会变化吗？**
A：会的。空间移动后会继承新上级空间的权限，原有的特殊权限可能会失效。

**Q6：如何快速查找某个空间？**
A：可以使用搜索功能，输入空间名称或编码进行查找，也可以使用筛选功能。

#### 层级结构相关问题

**Q7：层级深度有限制吗？**
A：建议层级深度不超过6级，过深的层级会影响系统性能和用户体验。

**Q8：如何调整整个层级结构？**
A：需要逐个调整空间的上级关系，建议先规划好新的层级结构再进行调整。

#### 设备关联相关问题

**Q9：设备可以关联到多个空间吗？**
A：不可以。一个设备只能关联到一个空间，如需移动请先解除原关联。

**Q10：如何批量移动设备到新空间？**
A：在设备管理页面选择多个设备，使用批量分配空间功能。

### 6.2 注意事项

#### 规划设计注意事项
- **提前规划**：在创建空间前要充分规划层级结构
- **命名规范**：建立统一的空间命名规范，便于管理
- **编码规则**：制定清晰的空间编码规则，确保唯一性
- **权限设计**：合理设计权限继承规则

#### 操作使用注意事项
- **数据备份**：重要操作前建议先备份相关数据
- **权限确认**：确认用户有足够的操作权限
- **依赖检查**：删除操作前检查是否有依赖关系
- **操作记录**：重要操作会自动记录日志

#### 性能优化注意事项
- **层级深度**：避免创建过深的层级结构
- **数据量控制**：单个空间下的子空间数量不宜过多
- **定期清理**：定期清理不使用的空间和类型
- **索引优化**：合理使用搜索和筛选功能

#### 安全管理注意事项
- **权限最小化**：按需分配权限，避免权限过大
- **定期审查**：定期审查空间权限配置
- **操作审计**：定期检查操作日志
- **数据保护**：重要空间数据要做好保护

### 6.3 最佳实践

#### 空间规划最佳实践
- **自上而下**：从顶级空间开始，逐级规划下级空间
- **逻辑清晰**：确保空间层级符合实际物理结构
- **预留扩展**：为未来扩展预留空间和编码
- **标准统一**：建立统一的命名和编码标准

#### 权限管理最佳实践
- **分级授权**：根据层级结构分级授权
- **最小权限**：按需分配最小必要权限
- **定期审查**：定期审查和调整权限配置
- **权限文档**：建立权限配置文档

#### 设备关联最佳实践
- **及时更新**：设备位置变化时及时更新关联
- **定期盘点**：定期盘点设备实际位置
- **移动审批**：建立设备移动审批流程
- **记录完整**：完整记录设备移动历史

#### 维护管理最佳实践
- **定期检查**：定期检查空间配置的准确性
- **数据清理**：定期清理无效的空间和类型
- **性能监控**：监控系统性能，及时优化
- **用户培训**：定期培训用户正确使用系统

💡 使用技巧
- 使用树形视图可以更直观地查看层级结构
- 使用面包屑导航可以快速定位当前空间位置
- 使用批量操作可以提高工作效率
- 定期导出空间配置数据作为备份

⚠️ 重要提醒
- 空间编码和类型编码一旦创建不可修改
- 删除操作不可恢复，请谨慎操作
- 层级调整可能影响权限和设备关联
- 重要变更建议在维护窗口期间进行

---

*本手册详细介绍了空间配置模块的各项功能和操作方法，帮助用户更好地使用空间配置功能。*
