<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>超压重控组态大屏设计器</title>
    <style>
        body {
            margin: 0;
            display: flex;
            height: 100vh;
            font-family: Arial, sans-serif;
        }

        /* 左侧组件面板 */
        .left-panel {
            width: 240px;
            background: #f5f5f5;
            padding: 20px;
            border-right: 1px solid #ddd;
        }

        /* 设计画布 */
        .design-canvas {
            flex: 1;
            background: #fff;
            position: relative;
            margin: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* 右侧属性面板 */
        .right-panel {
            width: 300px;
            background: #f5f5f5;
            padding: 20px;
            border-left: 1px solid #ddd;
        }

        .component-list {
            list-style: none;
            padding: 0;
        }
        .component-list li {
            padding: 8px;
            margin: 4px 0;
            background: white;
            border-radius: 4px;
            cursor: move;
        }

        .property-item {
            margin: 12px 0;
        }
        input, select {
            width: 100%;
            padding: 6px;
        }
    </style>
</head>
<body>
    <!-- 左侧组件库 -->
    <div class="left-panel">
        <h3>基本组件</h3>
        <ul class="component-list">
            <li>基础组件</li>
            <li>动态组件</li>
            <li>拓扑组态</li>
            <li>大屏图表</li>
            <li>我的图层</li>
            <li>模板广场</li>
            <li>数字孪生</li>
        </ul>

        <h3>其他组件</h3>
        <ul class="component-list">
            <li>系统操作</li>
            <li>模式</li>
            <li>通用时间</li>
        </ul>
    </div>

    <!-- 设计画布 -->
    <div class="design-canvas" id="mainCanvas" style="width: 1000px; height: 1000px">
        <!-- 这里可以放置拖入的组件 -->
    </div>

    <!-- 右侧属性面板 -->
    <div class="right-panel">
        <h3>样式设置</h3>
        
        <div class="property-item">
            <label>网格类型</label>
            <select id="gridType">
                <option>点阵网格</option>
                <option>线型网格</option>
            </select>
        </div>

        <div class="property-item">
            <label>网格大小</label>
            <input type="number" id="gridSize" value="20">
        </div>

        <div class="property-item">
            <label>背景颜色</label>
            <input type="color" id="bgColor">
        </div>

        <div class="property-item">
            <label>背景图片</label>
            <input type="file" id="bgImage">
        </div>

        <div class="property-item">
            <label>
                <input type="checkbox" id="lockDrag"> 禁止拖拽
            </label>
        </div>
    </div>

    <script>
        // 基础交互逻辑
        document.getElementById('bgColor').addEventListener('input', function(e) {
            document.getElementById('mainCanvas').style.backgroundColor = e.target.value;
        });

        document.getElementById('lockDrag').addEventListener('change', function(e) {
            document.querySelectorAll('.component-list li').forEach(item => {
                item.style.cursor = e.target.checked ? 'not-allowed' : 'move';
            });
        });
    </script>
</body>
</html>