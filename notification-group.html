<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知组管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .drawer {
            transition: transform 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">通知组</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">通知组管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6 space-y-6">
                <!-- 搜索区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6 flex justify-between items-center">
                    <div class="flex space-x-4">
                        <input type="text" placeholder="搜索通知组名称" class="border rounded-lg px-3 py-2 w-64">
                    </div>
                    <div class="flex space-x-4">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">搜索</button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">重置</button>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6 flex justify-between items-center">
                    <button onclick="toggleAddGroupDrawer()" class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                        <i class="ri-add-line text-lg"></i>
                        <span>新增通知组</span>
                    </button>
                    <button class="border border-red-500 text-red-500 h-10 px-4 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2">
                        <i class="ri-delete-bin-line text-lg"></i>
                        <span>批量删除</span>
                    </button>
                </div>

                <!-- 通知组列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知组名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成员</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知方式</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">组1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成员A, 成员B</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">这是一个通知组</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">短信, 邮件</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800">编辑</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                            <!-- 更多通知组记录... -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增通知组的抽屉 -->
    <div id="addGroupDrawer" class="fixed inset-0 bg-gray-800 bg-opacity-50 hidden">
        <div class="drawer absolute right-0 top-0 h-full w-1/3 bg-white transform translate-x-full transition-transform duration-300 overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">新增通知组</h3>
                    <button onclick="toggleAddGroupDrawer()" class="text-gray-400 hover:text-gray-600">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>
                <form id="addGroupForm" class="space-y-6">
                    <!-- 通知组名称 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">通知组名称*</label>
                        <input type="text" name="groupName" class="w-full border rounded-lg px-3 py-2" required>
                    </div>

                    <!-- 通知类型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">通知类型*</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center space-x-2">
                                <input type="radio" name="notifyType" value="member" class="form-radio text-blue-500" checked onchange="toggleNotifyType('member')">
                                <span>成员</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="radio" name="notifyType" value="robot" class="form-radio text-blue-500" onchange="toggleNotifyType('robot')">
                                <span>群组机器人</span>
                            </label>
                        </div>
                    </div>

                    <!-- 成员通知配置 -->
                    <div id="memberNotifyConfig" class="space-y-4">
                        <!-- 成员选择 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">选择成员*</label>
                            <div class="space-y-4">
                                <div>
                                    <label class="text-sm text-gray-600">个人</label>
                                    <select multiple name="individuals" class="w-full border rounded-lg px-3 py-2 h-24">
                                        <option>张三</option>
                                        <option>李四</option>
                                        <option>王五</option>
                                    </select>
                                </div>
        
                            </div>
                        </div>

                        <!-- 通知方式 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">通知方式*</label>
                            <div class="grid grid-cols-2 gap-4">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="notifyMethods" value="sms" class="form-checkbox text-blue-500">
                                    <span>短信</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="notifyMethods" value="phone" class="form-checkbox text-blue-500">
                                    <span>电话</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="notifyMethods" value="site" class="form-checkbox text-blue-500">
                                    <span>站内通知</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="notifyMethods" value="app" class="form-checkbox text-blue-500">
                                    <span>APP通知</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="notifyMethods" value="email" class="form-checkbox text-blue-500">
                                    <span>邮件</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 群组机器人配置 -->
                    <div id="robotNotifyConfig" class="space-y-4 hidden">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">选择群组机器人</label>
                            <div class="space-y-3">
                                <!-- 企业微信群机器人 -->
                                <div class="border rounded-lg p-3">
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" name="robotTypes" value="wework" class="form-checkbox text-blue-500" onchange="toggleRobotConfig(this, 'weworkConfig')">
                                        <span class="font-medium">企业微信群机器人</span>
                                    </div>
                                    <div id="weworkConfig" class="mt-2 hidden">
                                        <input type="text" name="weworkUrl" placeholder="请输入企业微信机器人Webhook URL" class="w-full border rounded-lg px-3 py-2 mt-2">
                                    </div>
                                </div>
                                
                                <!-- 钉钉群机器人 -->
                                <div class="border rounded-lg p-3">
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" name="robotTypes" value="dingtalk" class="form-checkbox text-blue-500" onchange="toggleRobotConfig(this, 'dingtalkConfig')">
                                        <span class="font-medium">钉钉群机器人</span>
                                    </div>
                                    <div id="dingtalkConfig" class="mt-2 hidden">
                                        <input type="text" name="dingtalkUrl" placeholder="请输入钉钉机器人Webhook URL" class="w-full border rounded-lg px-3 py-2 mt-2">
                                    </div>
                                </div>
                                
                                <!-- 飞书群机器人 -->
                                <div class="border rounded-lg p-3">
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" name="robotTypes" value="feishu" class="form-checkbox text-blue-500" onchange="toggleRobotConfig(this, 'feishuConfig')">
                                        <span class="font-medium">飞书群机器人</span>
                                    </div>
                                    <div id="feishuConfig" class="mt-2 hidden">
                                        <input type="text" name="feishuUrl" placeholder="请输入飞书机器人Webhook URL" class="w-full border rounded-lg px-3 py-2 mt-2">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 描述 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                        <textarea name="description" class="w-full border rounded-lg px-3 py-2 h-32 resize-none"></textarea>
                    </div>
                </form>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4 mt-6">
                    <button onclick="toggleAddGroupDrawer()" class="px-4 py-2 border rounded-lg hover:bg-gray-100">取消</button>
                    <button onclick="submitAddGroup()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 切换通知类型
        function toggleNotifyType(type) {
            const memberConfig = document.getElementById('memberNotifyConfig');
            const robotConfig = document.getElementById('robotNotifyConfig');
            
            if (type === 'member') {
                memberConfig.classList.remove('hidden');
                robotConfig.classList.add('hidden');
            } else {
                memberConfig.classList.add('hidden');
                robotConfig.classList.remove('hidden');
            }
        }
        
        // 切换机器人配置
        function toggleRobotConfig(checkbox, configId) {
            const configDiv = document.getElementById(configId);
            if (checkbox.checked) {
                configDiv.classList.remove('hidden');
            } else {
                configDiv.classList.add('hidden');
            }
        }

        // 切换新增通知组的抽屉
        function toggleAddGroupDrawer() {
            const drawer = document.getElementById('addGroupDrawer');
            const drawerContent = drawer.querySelector('.drawer');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置报警配置菜单为展开状态，并设置通知组管理为高亮
            const alarmConfigItem = document.querySelector('.nav-item:nth-child(5)');
            alarmConfigItem.classList.add('active');
            const notificationGroupItem = alarmConfigItem.querySelector('ul li:first-child');
            notificationGroupItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
        });

        // 提交新增通知组
        function submitAddGroup() {
            const form = document.getElementById('addGroupForm');
            const formData = new FormData(form);
            // 这里添加表单提交逻辑
            console.log('新增通知组:', Object.fromEntries(formData));
            toggleAddGroupDrawer();
            form.reset();
        }
    </script>
</body>
</html> 