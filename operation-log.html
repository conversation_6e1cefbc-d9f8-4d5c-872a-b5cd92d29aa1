<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 智慧物联网平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .drawer {
            transition: transform 0.3s ease-in-out;
        }
        
        /* 表格行悬停效果 */
        .table-row:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        /* 状态标签动画 */
        .status-badge {
            transition: all 0.2s ease;
        }
        
        .status-badge:hover {
            transform: scale(1.05);
        }
        
        /* 加载动画 */
        .loading {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 搜索框聚焦效果 */
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        /* 按钮悬停效果 */
        .btn-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        /* 详情抽屉内容样式 */
        .detail-field {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }

        /* 成功提示动画 */
        .toast-enter {
            transform: translateX(100%);
            opacity: 0;
        }

        .toast-show {
            transform: translateX(0);
            opacity: 1;
            transition: all 0.3s ease-out;
        }

        .toast-exit {
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease-in;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 台账管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-3-line mr-2"></i>
                                <span>台账管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="ledger.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 台账记录
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="operation-log.html" class="flex items-center w-full">
                                    <i class="ri-file-text-line mr-2"></i> 操作日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 个人配置 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">系统配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">操作日志</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6 space-y-6">
                <!-- 操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">操作日志</h2>
                        <div class="flex space-x-3">
                            <button onclick="exportOperationLog()" 
                                class="bg-green-500 text-white h-10 px-4 rounded-lg hover:bg-green-600 transition-colors flex items-center space-x-2">
                                <i class="ri-download-line text-lg"></i>
                                <span>导出Excel</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索区域 -->
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="relative">
                            <input id="searchKeyword" type="text" placeholder="搜索用户或设备"
                                class="search-input w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all">
                            <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
                        </div>
                        <div>
                            <select id="operationTypeFilter" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部操作类型</option>
                                <option value="重启">重启</option>
                                <option value="设置温度">设置温度</option>
                                <option value="启用">启用</option>
                                <option value="关闭">关闭</option>
                                <option value="配置">配置</option>
                            </select>
                        </div>
                        <div>
                            <select id="resultFilter" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部结果</option>
                                <option value="成功">成功</option>
                                <option value="失败">失败</option>
                                <option value="超时">超时</option>
                            </select>
                        </div>
                        <div>
                            <input id="startDate" type="date" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex space-x-2">
                            <input id="endDate" type="date" class="flex-1 border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button onclick="searchOperationLog()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                <i class="ri-search-line"></i>
                            </button>
                            <button onclick="resetSearch()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                                <i class="ri-refresh-line"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 日志列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作用户</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标设备</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作结果</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">响应时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="operationLogTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-3 flex items-center justify-between border-t">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                上一页
                            </button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下一页
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示第 <span id="pageStart" class="font-medium">1</span> 到 <span id="pageEnd" class="font-medium">10</span> 条，
                                    共 <span id="totalCount" class="font-medium">50</span> 条记录
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                                    <!-- 分页按钮将通过JavaScript动态生成 -->
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 日志详情抽屉 -->
    <div id="logDetailDrawer" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-[600px] bg-white transform translate-x-full drawer">
            <div class="flex flex-col h-full">
                <!-- 抽屉头部 -->
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium">操作日志详情</h3>
                        <button onclick="toggleLogDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div id="logDetailContent" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">日志ID</label>
                                    <div id="detailLogId" class="detail-field text-sm text-gray-900 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">操作时间</label>
                                    <div id="detailOperationTime" class="detail-field text-sm text-gray-900 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">操作用户</label>
                                    <div id="detailOperationUser" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">用户角色</label>
                                    <div id="detailUserRole" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备信息 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">设备信息</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">目标设备ID</label>
                                    <div id="detailTargetDeviceId" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">设备名称</label>
                                    <div id="detailDeviceName" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作信息 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">操作信息</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">指令类型</label>
                                    <div id="detailCommandType" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">指令参数</label>
                                    <div id="detailCommandParams" class="text-sm text-gray-900 bg-gray-50 p-3 rounded font-mono whitespace-pre-wrap"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 执行结果 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">执行结果</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">操作结果</label>
                                    <div id="detailOperationResult" class="text-sm bg-gray-50 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">响应时间</label>
                                    <div id="detailResponseTime" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 其他信息 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">其他信息</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">客户端IP</label>
                                    <div id="detailClientIp" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">备注信息</label>
                                    <div id="detailRemark" class="text-sm text-gray-900 bg-gray-50 p-3 rounded min-h-[60px]"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex justify-end">
                        <button onclick="toggleLogDetailDrawer()"
                            class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // ==================== 操作日志数据和逻辑 ====================

        // 模拟操作日志数据
        let operationLogData = [
            {
                logId: 'LOG20241201001',
                operationTime: '2024-12-01 14:30:25',
                operationUser: 'admin',
                userRole: '系统管理员',
                targetDeviceId: 'DEV001',
                deviceName: '主楼空调A3',
                commandType: '设置温度',
                commandParams: '{"temperature": 26, "mode": "cooling"}',
                operationResult: '成功',
                responseTime: 1250,
                clientIp: '*************',
                remark: '用户手动调节温度'
            },
            {
                logId: 'LOG20241201002',
                operationTime: '2024-12-01 14:25:10',
                operationUser: 'operator1',
                userRole: '设备操作员',
                targetDeviceId: 'DEV002',
                deviceName: '监控摄像头B1',
                commandType: '重启',
                commandParams: '{}',
                operationResult: '成功',
                responseTime: 3200,
                clientIp: '*************',
                remark: '定期维护重启'
            },
            {
                logId: 'LOG20241201003',
                operationTime: '2024-12-01 14:20:45',
                operationUser: 'admin',
                userRole: '系统管理员',
                targetDeviceId: 'DEV003',
                deviceName: '智能照明C2',
                commandType: '启用',
                commandParams: '{"brightness": 80}',
                operationResult: '失败',
                responseTime: 5000,
                clientIp: '*************',
                remark: '设备离线，操作失败'
            },
            {
                logId: 'LOG20241201004',
                operationTime: '2024-12-01 14:15:30',
                operationUser: 'operator2',
                userRole: '设备操作员',
                targetDeviceId: 'DEV004',
                deviceName: '门禁系统D1',
                commandType: '配置',
                commandParams: '{"access_level": "high", "timeout": 30}',
                operationResult: '成功',
                responseTime: 800,
                clientIp: '*************',
                remark: '更新门禁权限配置'
            },
            {
                logId: 'LOG20241201005',
                operationTime: '2024-12-01 14:10:15',
                operationUser: 'admin',
                userRole: '系统管理员',
                targetDeviceId: 'DEV005',
                deviceName: '环境传感器E1',
                commandType: '关闭',
                commandParams: '{}',
                operationResult: '超时',
                responseTime: 10000,
                clientIp: '*************',
                remark: '设备响应超时'
            },
            {
                logId: 'LOG20241201006',
                operationTime: '2024-12-01 14:05:00',
                operationUser: 'operator1',
                userRole: '设备操作员',
                targetDeviceId: 'DEV006',
                deviceName: '消防报警器F1',
                commandType: '启用',
                commandParams: '{"sensitivity": "high"}',
                operationResult: '成功',
                responseTime: 1100,
                clientIp: '*************',
                remark: '提高报警灵敏度'
            },
            {
                logId: 'LOG20241201007',
                operationTime: '2024-12-01 13:58:42',
                operationUser: 'admin',
                userRole: '系统管理员',
                targetDeviceId: 'DEV007',
                deviceName: '电梯控制器G1',
                commandType: '重启',
                commandParams: '{}',
                operationResult: '成功',
                responseTime: 2800,
                clientIp: '*************',
                remark: '系统升级后重启'
            },
            {
                logId: 'LOG20241201008',
                operationTime: '2024-12-01 13:50:20',
                operationUser: 'operator2',
                userRole: '设备操作员',
                targetDeviceId: 'DEV008',
                deviceName: '停车场闸机H1',
                commandType: '配置',
                commandParams: '{"auto_close_time": 10}',
                operationResult: '失败',
                responseTime: 0,
                clientIp: '*************',
                remark: '网络连接异常'
            }
        ];

        // 分页配置
        let currentPage = 1;
        let pageSize = 10;
        let filteredData = [...operationLogData];

        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置系统配置菜单为展开状态
            const systemConfigItem = document.querySelector('.nav-item.active');
            if (systemConfigItem) {
                systemConfigItem.classList.add('active');
            }

            // 初始化操作日志列表
            initOperationLogList();

            // 绑定搜索事件
            bindSearchEvents();
        });

        // 初始化操作日志列表
        function initOperationLogList() {
            filteredData = [...operationLogData];
            renderOperationLogTable();
            renderPagination();
        }

        // 渲染操作日志表格
        function renderOperationLogTable() {
            const tbody = document.getElementById('operationLogTableBody');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);

            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <div>暂无操作日志记录</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = pageData.map((item, index) => `
                <tr class="table-row cursor-pointer">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${startIndex + index + 1}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.operationTime}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="font-medium">${item.operationUser}</div>
                        <div class="text-xs text-gray-500">${item.userRole}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="font-medium">${item.deviceName}</div>
                        <div class="text-xs text-gray-500">${item.targetDeviceId}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.commandType}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge px-2 py-1 text-xs font-medium rounded-full ${getResultClass(item.operationResult)}">
                            ${item.operationResult}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.responseTime}ms</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button onclick="viewOperationLogDetail('${item.logId}')"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors">
                            <i class="ri-eye-line mr-1"></i>查看详情
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取操作结果样式类
        function getResultClass(result) {
            switch (result) {
                case '成功':
                    return 'bg-green-100 text-green-800';
                case '失败':
                    return 'bg-red-100 text-red-800';
                case '超时':
                    return 'bg-yellow-100 text-yellow-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 渲染分页
        function renderPagination() {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredData.length);

            // 更新统计信息
            document.getElementById('pageStart').textContent = filteredData.length > 0 ? startIndex : 0;
            document.getElementById('pageEnd').textContent = endIndex;
            document.getElementById('totalCount').textContent = filteredData.length;

            // 生成分页按钮
            const pagination = document.getElementById('pagination');
            let paginationHTML = '';

            // 上一页按钮
            paginationHTML += `
                <button onclick="changePage(${currentPage - 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}"
                    ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="ri-arrow-left-s-line"></i>
                </button>
            `;

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button onclick="changePage(${i})"
                            class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                i === currentPage
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += `
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                    `;
                }
            }

            // 下一页按钮
            paginationHTML += `
                <button onclick="changePage(${currentPage + 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}"
                    ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="ri-arrow-right-s-line"></i>
                </button>
            `;

            pagination.innerHTML = paginationHTML;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderOperationLogTable();
                renderPagination();
            }
        }

        // 绑定搜索事件
        function bindSearchEvents() {
            // 实时搜索
            document.getElementById('searchKeyword').addEventListener('input', debounce(searchOperationLog, 300));
            document.getElementById('operationTypeFilter').addEventListener('change', searchOperationLog);
            document.getElementById('resultFilter').addEventListener('change', searchOperationLog);
            document.getElementById('startDate').addEventListener('change', searchOperationLog);
            document.getElementById('endDate').addEventListener('change', searchOperationLog);
        }

        // 搜索操作日志
        function searchOperationLog() {
            const keyword = document.getElementById('searchKeyword').value.toLowerCase();
            const operationType = document.getElementById('operationTypeFilter').value;
            const result = document.getElementById('resultFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            filteredData = operationLogData.filter(item => {
                // 关键词搜索
                const matchKeyword = !keyword ||
                    item.operationUser.toLowerCase().includes(keyword) ||
                    item.deviceName.toLowerCase().includes(keyword) ||
                    item.targetDeviceId.toLowerCase().includes(keyword);

                // 操作类型筛选
                const matchOperationType = !operationType || item.commandType === operationType;

                // 结果筛选
                const matchResult = !result || item.operationResult === result;

                // 日期筛选
                const itemDate = item.operationTime.split(' ')[0];
                const matchStartDate = !startDate || itemDate >= startDate;
                const matchEndDate = !endDate || itemDate <= endDate;

                return matchKeyword && matchOperationType && matchResult && matchStartDate && matchEndDate;
            });

            currentPage = 1;
            renderOperationLogTable();
            renderPagination();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('operationTypeFilter').value = '';
            document.getElementById('resultFilter').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';

            filteredData = [...operationLogData];
            currentPage = 1;
            renderOperationLogTable();
            renderPagination();
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 查看操作日志详情
        function viewOperationLogDetail(logId) {
            const logItem = operationLogData.find(log => log.logId === logId);
            if (!logItem) return;

            // 填充详情信息
            document.getElementById('detailLogId').textContent = logItem.logId;
            document.getElementById('detailOperationTime').textContent = logItem.operationTime;
            document.getElementById('detailOperationUser').textContent = logItem.operationUser;
            document.getElementById('detailUserRole').textContent = logItem.userRole;
            document.getElementById('detailTargetDeviceId').textContent = logItem.targetDeviceId;
            document.getElementById('detailDeviceName').textContent = logItem.deviceName;
            document.getElementById('detailCommandType').textContent = logItem.commandType;

            // 格式化JSON参数
            try {
                const params = JSON.parse(logItem.commandParams);
                document.getElementById('detailCommandParams').textContent = JSON.stringify(params, null, 2);
            } catch (e) {
                document.getElementById('detailCommandParams').textContent = logItem.commandParams;
            }

            // 设置操作结果样式
            const resultElement = document.getElementById('detailOperationResult');
            resultElement.textContent = logItem.operationResult;
            resultElement.className = `text-sm p-2 rounded ${getResultClass(logItem.operationResult)}`;

            document.getElementById('detailResponseTime').textContent = `${logItem.responseTime}ms`;
            document.getElementById('detailClientIp').textContent = logItem.clientIp;
            document.getElementById('detailRemark').textContent = logItem.remark || '无';

            // 打开抽屉
            toggleLogDetailDrawer();
        }

        // 切换日志详情抽屉
        function toggleLogDetailDrawer() {
            const drawer = document.getElementById('logDetailDrawer');
            const drawerContent = drawer.querySelector('.drawer');

            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 导出操作日志
        function exportOperationLog() {
            if (filteredData.length === 0) {
                alert('没有数据可以导出');
                return;
            }

            // 生成CSV内容
            const csvContent = generateOperationLogCSV(filteredData);

            // 下载文件
            downloadFile(csvContent, `操作日志_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv;charset=utf-8;');

            alert(`成功导出 ${filteredData.length} 条操作日志记录`);
        }

        // 生成操作日志CSV内容
        function generateOperationLogCSV(data) {
            // CSV表头
            const headers = [
                '序号', '日志ID', '操作时间', '操作用户', '用户角色',
                '目标设备ID', '设备名称', '指令类型', '指令参数', '操作结果',
                '响应时间(ms)', '客户端IP', '备注信息'
            ];

            // 构建CSV内容
            let csvContent = '\uFEFF'; // 添加BOM以支持中文
            csvContent += headers.join(',') + '\n';

            data.forEach((item, index) => {
                const row = [
                    index + 1,
                    `"${item.logId}"`,
                    `"${item.operationTime}"`,
                    `"${item.operationUser}"`,
                    `"${item.userRole}"`,
                    `"${item.targetDeviceId}"`,
                    `"${item.deviceName}"`,
                    `"${item.commandType}"`,
                    `"${item.commandParams.replace(/"/g, '""')}"`, // 转义双引号
                    `"${item.operationResult}"`,
                    item.responseTime,
                    `"${item.clientIp}"`,
                    `"${item.remark}"`
                ];
                csvContent += row.join(',') + '\n';
            });

            return csvContent;
        }

        // 下载文件
        function downloadFile(content, fileName, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            window.URL.revokeObjectURL(url);
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Escape 关闭抽屉
            if (e.key === 'Escape') {
                const drawer = document.getElementById('logDetailDrawer');
                if (!drawer.classList.contains('hidden')) {
                    toggleLogDetailDrawer();
                }
            }
        });

        // 添加表格行点击事件
        document.addEventListener('click', function(e) {
            const row = e.target.closest('.table-row');
            if (row && !e.target.closest('button')) {
                // 点击表格行时高亮显示
                document.querySelectorAll('.table-row').forEach(r => r.classList.remove('bg-blue-50'));
                row.classList.add('bg-blue-50');
            }
        });

        // 添加加载状态
        function showLoading(message = '加载中...') {
            const tbody = document.getElementById('operationLogTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                        <i class="ri-loader-4-line text-4xl mb-2 loading"></i>
                        <div>${message}</div>
                    </td>
                </tr>
            `;
        }

        // 添加成功提示
        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 toast-enter';
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="ri-check-line mr-2"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.classList.remove('toast-enter');
                toast.classList.add('toast-show');
            }, 100);

            // 隐藏动画
            setTimeout(() => {
                toast.classList.remove('toast-show');
                toast.classList.add('toast-exit');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 添加错误提示
        function showErrorMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 toast-enter';
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="ri-error-warning-line mr-2"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.remove('toast-enter');
                toast.classList.add('toast-show');
            }, 100);

            setTimeout(() => {
                toast.classList.remove('toast-show');
                toast.classList.add('toast-exit');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 更新导出函数以使用新的提示
        const originalExportOperationLog = exportOperationLog;
        exportOperationLog = function() {
            if (filteredData.length === 0) {
                showErrorMessage('没有数据可以导出');
                return;
            }

            // 显示加载状态
            showLoading('正在生成导出文件...');

            // 模拟异步操作
            setTimeout(() => {
                try {
                    // 生成CSV内容
                    const csvContent = generateOperationLogCSV(filteredData);

                    // 下载文件
                    downloadFile(csvContent, `操作日志_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv;charset=utf-8;');

                    // 恢复表格显示
                    renderOperationLogTable();

                    // 显示成功提示
                    showSuccessMessage(`成功导出 ${filteredData.length} 条操作日志记录`);
                } catch (error) {
                    renderOperationLogTable();
                    showErrorMessage('导出失败，请重试');
                }
            }, 800);
        };

        // 添加数据统计信息
        function updateDataStats() {
            const totalLogs = operationLogData.length;
            const successLogs = operationLogData.filter(log => log.operationResult === '成功').length;
            const failedLogs = operationLogData.filter(log => log.operationResult === '失败').length;
            const timeoutLogs = operationLogData.filter(log => log.operationResult === '超时').length;

            console.log(`数据统计: 总计${totalLogs}条, 成功${successLogs}条, 失败${failedLogs}条, 超时${timeoutLogs}条`);
        }

        // 页面加载完成后更新统计
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateDataStats, 1000);
        });
    </script>
</body>
</html>
