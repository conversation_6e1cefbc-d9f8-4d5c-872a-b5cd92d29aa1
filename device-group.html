<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备组管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 text-white bg-blue-600 rounded-md flex items-center cursor-pointer px-2">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 通知组管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-alert-line mr-2"></i> 报警规则管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-history-line mr-2"></i> 报警记录管理
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-organization-chart-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 p-6">
            <!-- 顶部导航 -->
            <div class="mb-4 text-gray-600">
                <span class="text-blue-500">首页</span> / <span class="text-blue-500">设备管理</span> / <span class="font-bold">设备分组</span>
            </div>

            <!-- 搜索和操作区域 -->
            <div class="bg-white p-4 rounded-lg shadow mb-6">
                <div class="flex justify-between items-center">
                    <div class="flex space-x-4 items-center">
                        <input type="text" placeholder="请输入设备组名称" class="border rounded-md px-3 py-2 w-64">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">搜索</button>
                        <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">重置</button>
                    </div>
                    <div class="flex space-x-4 items-center">
                        <button onclick="toggleAddGroupSidebar()" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600">新增设备组</button>
                        <button class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600">批量删除</button>
                    </div>
                </div>
            </div>

            <!-- 设备组树状列表 -->
            <div class="bg-white rounded-lg shadow">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">设备组名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">描述</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <!-- 设备组 A -->
                        <tr>
                            <td class="px-6 py-4">
                                <button onclick="toggleGroup('groupA')" class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 transition-transform" id="arrowGroupA" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span>设备组A</span>
                                </button>
                            </td>
                            <td class="px-6 py-4">主要设备组A的描述信息</td>
                            <td class="px-6 py-4 space-x-2">
                                <button class="text-blue-600 hover:text-blue-800">编辑</button>
                                <button class="text-red-600 hover:text-red-800">删除</button>
                            </td>
                        </tr>
                        <!-- 设备组 A 的子组 -->
                        <tr class="hidden bg-gray-50" id="groupA-children">
                            <td class="px-6 py-4">
                                <div class="ml-6 flex items-center space-x-2">
                                    <span class="text-gray-400">├</span>
                                    <span>设备组A1</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">A1子组的描述信息</td>
                            <td class="px-6 py-4 space-x-2">
                                <button class="text-blue-600 hover:text-blue-800">编辑</button>
                                <button class="text-red-600 hover:text-red-800">删除</button>
                            </td>
                        </tr>
                        <tr class="hidden bg-gray-50" id="groupA-children2">
                            <td class="px-6 py-4">
                                <div class="ml-6 flex items-center space-x-2">
                                    <span class="text-gray-400">└</span>
                                    <span>设备组A2</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">A2子组的描述信息</td>
                            <td class="px-6 py-4 space-x-2">
                                <button class="text-blue-600 hover:text-blue-800">编辑</button>
                                <button class="text-red-600 hover:text-red-800">删除</button>
                            </td>
                        </tr>

                        <!-- 设备组 B -->
                        <tr>
                            <td class="px-6 py-4">
                                <button onclick="toggleGroup('groupB')" class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 transition-transform" id="arrowGroupB" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span>设备组B</span>
                                </button>
                            </td>
                            <td class="px-6 py-4">主要设备组B的描述信息</td>
                            <td class="px-6 py-4 space-x-2">
                                <button class="text-blue-600 hover:text-blue-800">编辑</button>
                                <button class="text-red-600 hover:text-red-800">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- 新增设备组侧边栏 -->
    <div id="addGroupSidebar" class="fixed inset-y-0 right-0 w-96 bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out">
        <div class="h-full flex flex-col">
            <!-- 头部 -->
            <div class="p-6 border-b">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-bold">新增设备组</h2>
                    <button onclick="toggleAddGroupSidebar()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 表单内容 -->
            <div class="flex-1 p-6 overflow-y-auto">
                <form class="space-y-6">
                    <!-- 设备组名称 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备组名称</label>
                        <input type="text" class="w-full border rounded-md px-3 py-2" placeholder="请输入设备组名称">
                    </div>

                    <!-- 上级设备组 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">上级设备组</label>
                        <select class="w-full border rounded-md px-3 py-2">
                            <option value="">无</option>
                            <option value="groupA">设备组A</option>
                            <option value="groupB">设备组B</option>
                        </select>
                    </div>

                    <!-- 设备组描述 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备组描述</label>
                        <textarea name="description" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入设备组描述..."></textarea>
                    </div>


                    
                </form>
            </div>

            <!-- 底部按钮 -->
            <div class="p-6 border-t">
                <div class="flex space-x-4">
                    <button class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">确认</button>
                    <button onclick="toggleAddGroupSidebar()" class="flex-1 bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        function toggleDeviceMenu() {
            const menu = document.getElementById('deviceMenu');
            const arrow = document.querySelector('.device-arrow');
            menu.classList.toggle('hidden');
            arrow.classList.toggle('rotate-180');
        }

        function toggleGroup(groupId) {
            const arrow = document.getElementById(`arrow${groupId.charAt(0).toUpperCase() + groupId.slice(1)}`);
            const children = document.querySelectorAll(`#${groupId}-children, #${groupId}-children2`);
            
            arrow.classList.toggle('rotate-90');
            children.forEach(child => {
                child.classList.toggle('hidden');
            });
        }

        function toggleAddGroupSidebar() {
            const sidebar = document.getElementById('addGroupSidebar');
            sidebar.classList.toggle('translate-x-full');
        }

        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置设备配置菜单为展开状态
            const deviceConfigItem = document.querySelector('.nav-item:nth-child(3)');
            deviceConfigItem.classList.add('active');
        });

        // 添加标签功能
        function addTag() {
            const tagInput = document.getElementById('tagInput');
            const tagContainer = document.getElementById('tagContainer');
            const tagText = tagInput.value.trim();
            
            if (tagText) {
                // 创建标签元素
                const tagElement = document.createElement('div');
                tagElement.className = 'flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm';
                tagElement.innerHTML = `
                    <span>${tagText}</span>
                    <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="removeTag(this)">
                        <i class="ri-close-line"></i>
                    </button>
                    <input type="hidden" name="tags[]" value="${tagText}">
                `;
                
                // 添加到容器
                tagContainer.appendChild(tagElement);
                
                // 清空输入框
                tagInput.value = '';
                tagInput.focus();
            }
        }

        // 删除标签
        function removeTag(button) {
            button.closest('div').remove();
        }

        // 监听回车键添加标签
        document.addEventListener('DOMContentLoaded', function() {
            const tagInput = document.getElementById('tagInput');
            if (tagInput) {
                tagInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        addTag();
                    }
                });
            }
        });
    </script>
</body>
</html> 