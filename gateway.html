<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网关管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 个人配置 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">网关配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">网关管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 搜索和操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center space-x-4">
                        <!-- 网关名称搜索 -->
                        <input type="text" placeholder="网关名称" 
                            class="pl-4 pr-4 py-2 border rounded-lg w-48 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                        
                        <button class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                            <i class="ri-search-line"></i>
                            <span>搜索</span>
                        </button>
                        <button class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                            <i class="ri-refresh-line"></i>
                            <span>重置</span>
                        </button>
                    </div>
                </div>

                <!-- 操作按钮和视图切换 -->
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <button onclick="toggleAddGatewayDrawer()" 
                            class="bg-blue-500 text-white h-10 px-6 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2 shadow-sm">
                            <i class="ri-add-line text-lg"></i>
                            <span>新增网关</span>
                        </button>
                        <button class="bg-white border border-gray-300 text-gray-700 h-10 px-6 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2 shadow-sm">
                            <i class="ri-delete-bin-line text-lg"></i>
                            <span>批量删除</span>
                        </button>
                    </div>
                    <div class="flex bg-white rounded-lg shadow-sm">
                        <button id="cardViewBtn" onclick="toggleView('card')" 
                            class="h-10 px-4 rounded-l-lg flex items-center space-x-1 transition-colors">
                            <i class="ri-grid-fill"></i>
                            <span>卡片</span>
                        </button>
                        <button id="listViewBtn" onclick="toggleView('list')" 
                            class="h-10 px-4 rounded-r-lg flex items-center space-x-1 transition-colors">
                            <i class="ri-list-check"></i>
                            <span>列表</span>
                        </button>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" class="grid grid-cols-3 gap-6">
                    <!-- 网关卡片 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-gateway-line text-2xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">网关-001</h3>
                                        <p class="text-gray-500 text-sm mt-1">GW20240315001</p>
                                    </div>
                                    <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">启用</span>
                                </div>
                                <div class="mt-2">
                                    <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">在线</span>
                                </div>
                                <div class="flex space-x-3 mt-4">
                                    <button class="text-blue-600 hover:text-blue-800 flex items-center space-x-1" onclick="toggleEditGatewayDrawer({
                                        name: '网关-001',
                                        code: 'GW20240315001',
                                        brand: 'brand1',
                                        protocol: 'MQTT',
                                        space: 'space1',
                                        status: 'enabled',
                                        description: '这是一个用于温度监控的网关设备，负责收集和传输温度数据。'
                                    })">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 更多网关卡片... -->
                </div>

                <!-- 列表视图 -->
                <div id="listView" class="hidden">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">网关编码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">网关名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">网关类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在线状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">GW20240315001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">网关-001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">类型一</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">在线</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                        <button class="text-blue-600 hover:text-blue-800" onclick="toggleEditGatewayDrawer({
                                            name: '网关-001',
                                            code: 'GW20240315001',
                                            brand: 'brand1',
                                            protocol: 'MQTT',
                                            space: 'space1',
                                            status: 'enabled',
                                            description: '这是一个用于温度监控的网关设备，负责收集和传输温度数据。'
                                        })">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增网关抽屉 -->
    <div id="addGatewayDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddGatewayDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">新增网关</h3>
                    <button onclick="toggleAddGatewayDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addGatewayForm" class="space-y-6">
                        <!-- 网关名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关名称 <span class="text-red-500">*</span></label>
                            <input type="text" name="name" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" required>
                        </div>

                        <!-- 网关编码 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关编码 <span class="text-red-500">*</span></label>
                            <input type="text" name="code" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" required>
                        </div>

                        <!-- 网关品牌 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关品牌</label>
                            <select name="brand" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                <option value="">请选择品牌</option>
                                <option value="brand1">品牌一</option>
                                <option value="brand2">品牌二</option>
                            </select>
                        </div>

                        <!-- 网关协议 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关协议 <span class="text-red-500">*</span></label>
                            <select name="protocol" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" required>
                                <option value="MQTT">MQTT</option>
                                <option value="HTTP">HTTP</option>
                            </select>
                        </div>

                        <!-- 所属空间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属空间</label>
                            <select name="space" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                <option value="">请选择空间</option>
                                <option value="space1">A栋1层</option>
                                <option value="space2">A栋2层</option>
                            </select>
                        </div>

                        <!-- 状态 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="enabled" class="mr-2" required>
                                    <span>启用</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="disabled" class="mr-2" required>
                                    <span>停用</span>
                                </label>
                            </div>
                        </div>

                        <!-- 网关描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">网关描述</label>
                            <textarea name="description" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入网关描述..."></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleAddGatewayDrawer()" class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitAddGateway()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑网关抽屉 -->
    <div id="editGatewayDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleEditGatewayDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">编辑网关</h3>
                    <button onclick="toggleEditGatewayDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="editGatewayForm" class="space-y-6">
                        <!-- 网关名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关名称 <span class="text-red-500">*</span></label>
                            <input type="text" name="name" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" required>
                        </div>

                        <!-- 网关编码 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关编码 <span class="text-red-500">*</span></label>
                            <input type="text" name="code" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" readonly style="background-color: #f0f0f0;">
                        </div>

                        <!-- 网关品牌 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关品牌</label>
                            <select name="brand" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                <option value="">请选择品牌</option>
                                <option value="brand1">品牌一</option>
                                <option value="brand2">品牌二</option>
                            </select>
                        </div>

                        <!-- 网关协议 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网关协议 <span class="text-red-500">*</span></label>
                            <select name="protocol" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" required>
                                <option value="MQTT">MQTT</option>
                                <option value="HTTP">HTTP</option>
                            </select>
                        </div>

                        <!-- 所属空间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属空间</label>
                            <select name="space" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                <option value="">请选择空间</option>
                                <option value="space1">A栋1层</option>
                                <option value="space2">A栋2层</option>
                            </select>
                        </div>

                        <!-- 状态 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="enabled" class="mr-2" required>
                                    <span>启用</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="disabled" class="mr-2" required>
                                    <span>停用</span>
                                </label>
                            </div>
                        </div>

                        <!-- 网关描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">网关描述</label>
                            <textarea name="description" rows="3" class="w-full px-4 py-2 border rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all" placeholder="请输入网关描述..."></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleEditGatewayDrawer()" class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitEditGateway()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 网关详情抽屉 -->
    <div id="gatewayDetailDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleGatewayDetailDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-2xl w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">网关详情</h3>
                    <button onclick="toggleGatewayDetailDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">基本信息</h4>
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-500">网关名称</p>
                                        <p class="text-gray-900">网关-001</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">网关编码</p>
                                        <p class="text-gray-900">GATEWAY_001</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-500">网关类型</p>
                                        <p class="text-gray-900">MQTT网关</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">状态</p>
                                        <p><span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-sm">在线</span></p>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">描述</p>
                                    <p class="text-gray-900">用于连接温湿度传感器的MQTT网关</p>
                                </div>
                            </div>
                        </div>

                        <!-- 连接信息 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">连接信息</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <!-- 连接类型选择 -->
                                <div class="mb-4">
                                    <div class="flex space-x-4">
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="connectionType" value="mqtt" class="text-blue-500 focus:ring-blue-500" checked onchange="switchConnectionType('mqtt')">
                                            <span class="ml-2">MQTT</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="radio" name="connectionType" value="http" class="text-blue-500 focus:ring-blue-500" onchange="switchConnectionType('http')">
                                            <span class="ml-2">HTTP</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- MQTT 连接信息 -->
                                <div id="mqttConnectionInfo" class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="relative">
                                            <p class="text-sm text-gray-500">主机</p>
                                            <div class="flex items-center">
                                                <p id="mqttHost" class="text-gray-900 mr-2">mqtt.example.com</p>
                                                <button onclick="copyToClipboard('mqttHost')" class="text-blue-500 hover:text-blue-700">
                                                    <i class="ri-file-copy-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <p class="text-sm text-gray-500">端口</p>
                                            <div class="flex items-center">
                                                <p id="mqttPort" class="text-gray-900 mr-2">1883</p>
                                                <button onclick="copyToClipboard('mqttPort')" class="text-blue-500 hover:text-blue-700">
                                                    <i class="ri-file-copy-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="relative">
                                            <p class="text-sm text-gray-500">用户名</p>
                                            <div class="flex items-center">
                                                <p id="mqttUsername" class="text-gray-900 mr-2">gateway_user</p>
                                                <button onclick="copyToClipboard('mqttUsername')" class="text-blue-500 hover:text-blue-700">
                                                    <i class="ri-file-copy-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <p class="text-sm text-gray-500">密码</p>
                                            <div class="flex items-center">
                                                <p id="mqttPassword" class="text-gray-900 mr-2">••••••••</p>
                                                <button onclick="togglePasswordVisibility('mqttPassword', 'gateway_password')" class="text-blue-500 hover:text-blue-700 mr-1">
                                                    <i id="mqttPasswordIcon" class="ri-eye-line"></i>
                                                </button>
                                                <button onclick="copyToClipboard('mqttPassword', 'gateway_password')" class="text-blue-500 hover:text-blue-700">
                                                    <i class="ri-file-copy-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- HTTP 连接信息 -->
                                <div id="httpConnectionInfo" class="space-y-4 hidden">
                                    <div class="relative">
                                        <p class="text-sm text-gray-500">接入点</p>
                                        <div class="flex items-center">
                                            <p id="httpEndpoint" class="text-gray-900 mr-2">https://api.example.com/gateway</p>
                                            <button onclick="copyToClipboard('httpEndpoint')" class="text-blue-500 hover:text-blue-700">
                                                <i class="ri-file-copy-line"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="relative">
                                            <p class="text-sm text-gray-500">用户名</p>
                                            <div class="flex items-center">
                                                <p id="httpUsername" class="text-gray-900 mr-2">api_user</p>
                                                <button onclick="copyToClipboard('httpUsername')" class="text-blue-500 hover:text-blue-700">
                                                    <i class="ri-file-copy-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <p class="text-sm text-gray-500">密码</p>
                                            <div class="flex items-center">
                                                <p id="httpPassword" class="text-gray-900 mr-2">••••••••</p>
                                                <button onclick="togglePasswordVisibility('httpPassword', 'api_password')" class="text-blue-500 hover:text-blue-700 mr-1">
                                                    <i id="httpPasswordIcon" class="ri-eye-line"></i>
                                                </button>
                                                <button onclick="copyToClipboard('httpPassword', 'api_password')" class="text-blue-500 hover:text-blue-700">
                                                    <i class="ri-file-copy-line"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 初始化导航和事件监听
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            document.getElementById('cardViewBtn').classList.add('bg-gray-100', 'text-blue-500');

            // 添加卡片点击事件
            const cardView = document.querySelector('#cardView');
            if (cardView) {
                const cards = cardView.querySelectorAll('.bg-white.rounded-xl');
                cards.forEach(card => {
                    card.addEventListener('click', function(e) {
                        if (!e.target.closest('button')) {
                            toggleGatewayDetailDrawer({
                                name: '网关-001',
                                code: 'GW20240315001',
                                brand: 'brand1',
                                protocol: 'MQTT',
                                space: 'space1',
                                status: 'enabled',
                                description: '这是一个用于温度监控的网关设备，负责收集和传输温度数据。'
                            });
                        }
                    });
                });
            }

            // 添加列表行点击事件
            const listView = document.querySelector('#listView');
            if (listView) {
                const rows = listView.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('click', function(e) {
                        if (!e.target.closest('button')) {
                            toggleGatewayDetailDrawer({
                                name: '网关-001',
                                code: 'GW20240315001',
                                brand: 'brand1',
                                protocol: 'MQTT',
                                space: 'space1',
                                status: 'enabled',
                                description: '这是一个用于温度监控的网关设备，负责收集和传输温度数据。'
                            });
                        }
                    });
                });
            }
        });

        // 切换视图
        function toggleView(type) {
            const cardView = document.getElementById('cardView');
            const listView = document.getElementById('listView');
            const cardViewBtn = document.getElementById('cardViewBtn');
            const listViewBtn = document.getElementById('listViewBtn');
            
            if (type === 'card') {
                cardView.classList.remove('hidden');
                listView.classList.add('hidden');
                cardViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.remove('bg-white');
                listViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.add('bg-white');
            } else {
                cardView.classList.add('hidden');
                listView.classList.remove('hidden');
                listViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.remove('bg-white');
                cardViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.add('bg-white');
            }
        }

        // 切换新增网关抽屉
        function toggleAddGatewayDrawer() {
            const drawer = document.getElementById('addGatewayDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 提交新增网关表单
        function submitAddGateway() {
            const form = document.getElementById('addGatewayForm');
            const formData = new FormData(form);
            
            // 这里添加表单提交逻辑
            console.log('提交网关表单:', Object.fromEntries(formData));
            
            // 关闭抽屉并重置表单
            toggleAddGatewayDrawer();
            form.reset();
        }

        // 切换编辑网关抽屉
        function toggleEditGatewayDrawer(gatewayData) {
            const drawer = document.getElementById('editGatewayDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                // 打开抽屉时，预填充表单数据
                if (gatewayData) {
                    fillEditForm(gatewayData);
                } else {
                    // 如果没有传入数据，使用默认数据（示例）
                    fillEditForm({
                        name: '网关-001',
                        code: 'GW20240315001',
                        brand: 'brand1',
                        protocol: 'MQTT',
                        space: 'space1',
                        status: 'enabled',
                        description: '这是一个用于温度监控的网关设备，负责收集和传输温度数据。'
                    });
                }
                
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }
        
        // 添加填充编辑表单的函数
        function fillEditForm(data) {
            const form = document.getElementById('editGatewayForm');
            
            // 设置文本输入框的值
            form.elements['name'].value = data.name || '';
            form.elements['code'].value = data.code || '';
            
            // 设置选择框的值
            if (form.elements['brand']) {
                form.elements['brand'].value = data.brand || '';
            }
            
            if (form.elements['protocol']) {
                form.elements['protocol'].value = data.protocol || '';
            }
            
            if (form.elements['space']) {
                form.elements['space'].value = data.space || '';
            }
            
            // 设置单选按钮的值
            if (data.status) {
                const statusRadios = form.elements['status'];
                for (let i = 0; i < statusRadios.length; i++) {
                    if (statusRadios[i].value === data.status) {
                        statusRadios[i].checked = true;
                        break;
                    }
                }
            }
            
            // 设置文本区域的值
            if (form.elements['description']) {
                form.elements['description'].value = data.description || '';
            }
        }

        // 提交编辑网关表单
        function submitEditGateway() {
            const form = document.getElementById('editGatewayForm');
            const formData = new FormData(form);
            
            // 这里添加表单提交逻辑
            console.log('提交编辑网关表单:', Object.fromEntries(formData));
            
            // 关闭抽屉并重置表单
            toggleEditGatewayDrawer();
            form.reset();
        }

        // 切换连接类型
        function switchConnectionType(type) {
            const mqttInfo = document.getElementById('mqttConnectionInfo');
            const httpInfo = document.getElementById('httpConnectionInfo');
            
            if (type === 'mqtt') {
                mqttInfo.classList.remove('hidden');
                httpInfo.classList.add('hidden');
            } else if (type === 'http') {
                mqttInfo.classList.add('hidden');
                httpInfo.classList.remove('hidden');
            }
        }

        // 切换密码可见性
        function togglePasswordVisibility(elementId, actualPassword) {
            const passwordElement = document.getElementById(elementId);
            const iconElement = document.getElementById(elementId + 'Icon');
            
            if (passwordElement.textContent === '••••••••') {
                passwordElement.textContent = actualPassword;
                iconElement.classList.remove('ri-eye-line');
                iconElement.classList.add('ri-eye-off-line');
            } else {
                passwordElement.textContent = '••••••••';
                iconElement.classList.remove('ri-eye-off-line');
                iconElement.classList.add('ri-eye-line');
            }
        }

        // 复制到剪贴板
        function copyToClipboard(elementId, actualText) {
            const element = document.getElementById(elementId);
            let textToCopy = element.textContent;
            
            // 如果是密码且当前显示为隐藏状态，则使用实际密码
            if (elementId.includes('Password') && textToCopy === '••••••••' && actualText) {
                textToCopy = actualText;
            }
            
            navigator.clipboard.writeText(textToCopy).then(() => {
                // 显示复制成功提示
                const toast = document.createElement('div');
                toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';
                toast.textContent = '已复制到剪贴板';
                document.body.appendChild(toast);
                
                // 2秒后移除提示
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        // 切换网关详情抽屉
        function toggleGatewayDetailDrawer() {
            const drawer = document.getElementById('gatewayDetailDrawer');
            const drawerContent = drawer.querySelector('.max-w-2xl');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }
    </script>
</body>
</html> 