<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点位详情 - AE86</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space-type.html" class="flex items-center w-full">
                                    <i class="ri-layout-line mr-2"></i> 空间类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-building-2-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-git-commit-line mr-2"></i> 规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-3-line mr-2"></i> 规则日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-calendar-todo-line mr-2"></i>
                                <span>任务配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 任务管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="schedule.html" class="flex items-center w-full">
                                    <i class="ri-time-line mr-2"></i> 定时任务
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <i class="ri-home-line mr-2"></i>
                            <span>点位详情</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <i class="ri-notification-3-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                            <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <i class="ri-fullscreen-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <i class="ri-search-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center space-x-3 ml-6 cursor-pointer group">
                            <img src="https://via.placeholder.com/36" class="w-9 h-9 rounded-full border-2 border-blue-100 group-hover:border-blue-400 transition-colors" alt="用户头像">
                            <span class="text-gray-700 font-medium group-hover:text-blue-600 transition-colors">管理员</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 点位信息卡片 -->
                <div class="bg-white p-6 rounded-lg shadow mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">点位名称</h3>
                            <p class="mt-1 text-lg">温度</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">点位标识</h3>
                            <p class="mt-1 text-lg">temperature</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">点位类型</h3>
                            <p class="mt-1 text-lg">
                                <span class="px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-sm">上报</span>
                            </p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">数据类型</h3>
                            <p class="mt-1 text-lg">数值</p>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="border-b">
                        <div class="flex justify-between items-center pr-6">
                            <div class="flex">
                                <button class="tab-button px-6 py-3 text-blue-600 border-b-2 border-blue-600 font-medium" onclick="showTab('history')">
                                    历史记录
                                </button>
                                <button class="tab-button px-6 py-3 text-gray-500" onclick="showTab('realtime')">
                                    实时数据
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="bg-green-100 text-green-700 px-3 py-1.5 rounded-md hover:bg-green-200 text-sm flex items-center transition-colors" onclick="downloadData()">
                                    <i class="ri-download-2-line mr-1"></i>
                                    下载数据
                                </button>
                                <div class="relative group">
                                    <i class="ri-question-line text-gray-400 cursor-pointer"></i>
                                    <div class="absolute bottom-full mb-2 w-52 bg-gray-800 text-white text-xs rounded py-1.5 px-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none -translate-x-1/2 left-1/2 z-20">
                                        下载当前筛选时间段内的所有上报数据。
                                        <div class="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-gray-800"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录内容 -->
                    <div id="history" class="tab-content p-6">
                        <!-- 历史记录子标签页 -->
                        <div class="mb-4 border-b">
                            <div class="flex">
                                <div class="subtab-button px-4 py-2 text-blue-600 border-b-2 border-blue-600 font-medium flex items-center cursor-pointer" onclick="showSubTab('report-history')">
                                    <span>上报历史记录</span>
                                    <div class="relative group ml-1.5">
                                        <i class="ri-question-line text-gray-400"></i>
                                        <div class="absolute bottom-full mb-2 w-64 bg-gray-800 text-white text-xs rounded py-1.5 px-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none -translate-x-1/2 left-1/2 z-20">
                                            当前默认显示最新1000条数据，如需查询完整历史，请使用上方"下载数据"功能。
                                            <div class="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-gray-800"></div>
                                        </div>
                                    </div>
                                </div>
                                <button class="subtab-button px-4 py-2 text-gray-500" onclick="showSubTab('command-history')">
                                    下发历史记录
                                </button>
                            </div>
                        </div>

                        <!-- 上报历史记录内容 -->
                        <div id="report-history" class="subtab-content">
                            <!-- 搜索和筛选 -->
                            <div class="mb-6 flex justify-between items-center">
                                <div class="flex space-x-4">
                                    <input type="datetime-local" id="startDate" class="border rounded-lg px-3 py-2">
                                    <span class="flex items-center">至</span>
                                    <input type="datetime-local" id="endDate" class="border rounded-lg px-3 py-2">
                                </div>
                                <div class="flex space-x-4">
                                    <!-- 视图切换按钮 -->
                                    <div class="flex rounded-lg overflow-hidden border mr-4">
                                        <button id="tableViewBtn" class="px-3 py-2 bg-blue-500 text-white flex items-center" onclick="switchHistoryView('table')">
                                            <i class="ri-table-line mr-1"></i> 列表
                                        </button>
                                        <button id="chartViewBtn" class="px-3 py-2 bg-white text-gray-700 flex items-center" onclick="switchHistoryView('chart')">
                                            <i class="ri-line-chart-line mr-1"></i> 图表
                                        </button>
                                    </div>
                                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">搜索</button>
                                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">重置</button>
                                </div>
                            </div>

                            <!-- 表格视图 -->
                            <div id="tableView" class="block">
                            <!-- 历史数据表格 -->
                            <table class="min-w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数值</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">26.5°C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:30:00</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">26.8°C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 14:29:00</td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>
                            
                            <!-- 图表视图 -->
                            <div id="chartView" class="hidden">
                                <div id="historyChart" class="bg-white rounded-lg p-6 h-80">
                                    <div class="flex items-center justify-center h-full text-gray-500">
                                        <i class="ri-line-chart-line text-4xl mr-2 text-blue-500"></i>
                                        <span>历史数据趋势图</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 下发历史记录内容 -->
                        <div id="command-history" class="subtab-content hidden">
                            <!-- 搜索和筛选 -->
                            <div class="mb-6 flex justify-between items-center">
                                <div class="flex space-x-4">
                                    <input type="datetime-local" class="border rounded-lg px-3 py-2">
                                    <span class="flex items-center">至</span>
                                    <input type="datetime-local" class="border rounded-lg px-3 py-2">
                                </div>
                                <div class="flex space-x-4">
                                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">搜索</button>
                                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">重置</button>
                                </div>
                            </div>

                            <!-- 下发历史数据表格 -->
                            <table class="min-w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发值</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发时间</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">25.0°C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 13:45:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-xs">成功</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">管理员</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">24.5°C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 10:20:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 py-1 bg-green-50 text-green-600 rounded-full text-xs">成功</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">管理员</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 实时数据内容 -->
                    <div id="realtime" class="tab-content p-6 hidden">
                        <div class="flex items-center justify-between mb-6">
                            <div class="text-2xl font-bold">
                                当前值：<span class="text-blue-600">26.5°C</span>
                            </div>
                            <div class="text-gray-500">
                                更新时间：2024-03-15 14:30:00
                            </div>
                        </div>
                        
                        <!-- 这里可以添加实时数据图表 -->
                        <div class="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
                            实时数据图表区域
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 初始化导航菜单
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            showTab('history');
            showSubTab('report-history');
        });

        // 切换标签页
        function showTab(tabId) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'font-medium');
                button.classList.add('text-gray-500');
            });
            
            // 显示选中的标签页内容
            const selectedContent = document.getElementById(tabId);
            selectedContent.classList.remove('hidden');
            
            // 激活选中的标签按钮
            const selectedButton = document.querySelector(`[onclick="showTab('${tabId}')"]`);
            selectedButton.classList.remove('text-gray-500');
            selectedButton.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'font-medium');
        }

        // 切换子标签页
        function showSubTab(subTabId) {
            // 隐藏所有子标签页内容
            document.querySelectorAll('.subtab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有子标签按钮的激活状态
            document.querySelectorAll('.subtab-button').forEach(button => {
                button.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'font-medium');
                button.classList.add('text-gray-500');
            });
            
            // 显示选中的子标签页内容
            const selectedContent = document.getElementById(subTabId);
            selectedContent.classList.remove('hidden');
            
            // 激活选中的子标签按钮
            const selectedButton = document.querySelector(`[onclick="showSubTab('${subTabId}')"]`);
            selectedButton.classList.remove('text-gray-500');
            selectedButton.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'font-medium');
        }

        // 切换历史记录视图（表格/图表）
        function switchHistoryView(viewType) {
            const chartView = document.getElementById('chartView');
            const tableView = document.getElementById('tableView');
            const chartViewBtn = document.getElementById('chartViewBtn');
            const tableViewBtn = document.getElementById('tableViewBtn');
            
            if (viewType === 'chart') {
                chartView.classList.remove('hidden');
                tableView.classList.add('hidden');
                chartViewBtn.classList.add('bg-blue-500', 'text-white');
                chartViewBtn.classList.remove('bg-white', 'text-gray-700');
                tableViewBtn.classList.remove('bg-blue-500', 'text-white');
                tableViewBtn.classList.add('bg-white', 'text-gray-700');
                
                // 这里可以添加渲染图表的代码
                renderHistoryChart();
            } else {
                chartView.classList.add('hidden');
                tableView.classList.remove('hidden');
                tableViewBtn.classList.add('bg-blue-500', 'text-white');
                tableViewBtn.classList.remove('bg-white', 'text-gray-700');
                chartViewBtn.classList.remove('bg-blue-500', 'text-white');
                chartViewBtn.classList.add('bg-white', 'text-gray-700');
            }
        }
        
        // 渲染历史数据图表
        function renderHistoryChart() {
            // 这里应该使用实际的图表库，如ECharts或Chart.js
            // 这里只是一个示例
            console.log('渲染历史数据图表');
        }

        // 下载数据
        function downloadData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('请先选择要下载数据的时间段。');
                return;
            }

            // 模拟下载
            alert(`准备下载从 ${startDate} 到 ${endDate} 的上报数据...`);
            console.log(`正在下载从 ${startDate} 到 ${endDate} 的数据...`);
            // 实际应用中，这里会向后端发送请求，获取数据并生成文件下载
        }
    </script>
</body>
</html> 