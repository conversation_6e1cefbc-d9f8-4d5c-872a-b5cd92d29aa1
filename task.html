<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 通知组管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-alert-line mr-2"></i> 报警规则管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-history-line mr-2"></i> 报警记录管理
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 text-white bg-blue-600 rounded-md flex items-center cursor-pointer px-2">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-organization-chart-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">任务管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 搜索和操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center space-x-4">
                        <!-- 任务名称搜索 -->
                        <input type="text" placeholder="任务名称" 
                            class="pl-4 pr-4 py-2 border rounded-lg w-48 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                        
                        <!-- 目标类型筛选 -->
                        <select class="pl-4 pr-8 py-2 border rounded-lg w-48 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                            <option value="">目标类型</option>
                            <option value="device">设备</option>
                            <option value="group">设备组</option>
                            <option value="type">设备类型</option>
                        </select>

                        <button class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                            <i class="ri-search-line"></i>
                            <span>搜索</span>
                        </button>
                        <button class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                            <i class="ri-refresh-line"></i>
                            <span>重置</span>
                        </button>
                    </div>
                </div>

                <!-- 操作按钮和视图切换 -->
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <button onclick="toggleAddTaskDrawer()" 
                            class="bg-blue-500 text-white h-10 px-6 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2 shadow-sm">
                            <i class="ri-add-line text-lg"></i>
                            <span>新增任务</span>
                        </button>
                        <button class="bg-white border border-gray-300 text-gray-700 h-10 px-6 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2 shadow-sm">
                            <i class="ri-delete-bin-line text-lg"></i>
                            <span>批量删除</span>
                        </button>
                    </div>
                    <div class="flex bg-white rounded-lg shadow-sm">
                        <button id="cardViewBtn" onclick="toggleView('card')" 
                            class="h-10 px-4 rounded-l-lg flex items-center space-x-1 transition-colors">
                            <i class="ri-grid-fill"></i>
                            <span>卡片</span>
                        </button>
                        <button id="listViewBtn" onclick="toggleView('list')" 
                            class="h-10 px-4 rounded-r-lg flex items-center space-x-1 transition-colors">
                            <i class="ri-list-check"></i>
                            <span>列表</span>
                        </button>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" class="grid grid-cols-3 gap-6">
                    <!-- 任务卡片 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-timer-line text-2xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">温度控制任务</h3>
                                        <p class="text-gray-500 text-sm mt-1">设备类型：温度传感器</p>
                                    </div>
                                    <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">启用</span>
                                </div>
                                <p class="text-gray-600 mt-2 text-sm">每日定时 - 08:00:00</p>
                                <div class="flex space-x-3 mt-4">
                                    <button class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                        <i class="ri-eye-line"></i>
                                        <span>查看</span>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 列表视图 -->
                <div id="listView" class="hidden">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">定时设置</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度控制任务</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">设备</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">属性下发</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">每日定时</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                        <button class="text-blue-600 hover:text-blue-800">查看</button>
                                        <button class="text-green-600 hover:text-green-800">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增任务抽屉 -->
    <div id="addTaskDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddTaskDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-2xl w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">新增任务</h3>
                    <button onclick="toggleAddTaskDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addTaskForm" class="space-y-6">
                        <!-- 任务名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">任务名称</label>
                            <input type="text" name="taskName" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                        </div>

                        <!-- 任务类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">任务类型</label>
                            <div class="space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="taskType" value="condition" class="text-blue-500 focus:ring-blue-500" onchange="handleTaskTypeChange('condition')" checked>
                                    <span class="ml-2">条件任务</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="taskType" value="scheduled" class="text-blue-500 focus:ring-blue-500" onchange="handleTaskTypeChange('scheduled')">
                                    <span class="ml-2">定时任务</span>
                                </label>
                            </div>
                        </div>

                        <!-- 设备源类型 -->
                        <div>
                        <div id="conditionTaskSettings" class="space-y-6">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">条件设置</label>
                                <div class="space-y-4" id="conditionCardList">
                                    <!-- 条件卡片 -->
                                    <div class="condition-card bg-white border rounded-lg p-4 shadow-sm">
                                        <div class="flex justify-between items-center mb-3">
                                            <h4 class="font-medium text-gray-800">条件卡片</h4>
                                            <button type="button" onclick="removeConditionCard(this)" class="text-red-500 hover:text-red-700">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                        <div class="space-y-4">
                                            <!-- 设备源选择 -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-600 mb-1">设备源</label>
                                                <select name="cardDeviceSource" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                                    <option value="">请选择设备</option>
                                                    <option value="device1">温度传感器-01</option>
                                                    <option value="device2">温度传感器-02</option>
                                                    <option value="device3">湿度传感器-01</option>
                                                    <option value="device4">湿度传感器-02</option>
                                                </select>
                                            </div>
                                            
                                            <!-- 点位阈值设置 -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-600 mb-1">点位阈值设置</label>
                                                <div class="space-y-3 thresholdItemList">
                                                    <div class="threshold-item">
                                                        <div class="grid grid-cols-4 gap-2 mb-2">
                                                            <select name="cardLogicalOperator" class="border rounded-lg px-3 py-2">
                                                                <option value="and">AND</option>
                                                                <option value="or">OR</option>
                                                            </select>
                                                            <select name="cardPointName" class="border rounded-lg px-3 py-2">
                                                                <option value="temperature">温度</option>
                                                                <option value="humidity">湿度</option>
                                                            </select>
                                                            <select name="cardCompareOperator" class="border rounded-lg px-3 py-2">
                                                                <option value="gt">大于</option>
                                                                <option value="lt">小于</option>
                                                                <option value="gte">大于等于</option>
                                                                <option value="lte">小于等于</option>
                                                                <option value="between">在...之间</option>
                                                                <option value="notBetween">不在...之间</option>
                                                            </select>
                                                            <input type="number" name="cardThresholdValue" class="border rounded-lg px-3 py-2" placeholder="数值">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="mt-2">
                                                    <button type="button" onclick="addThresholdItem(this)" 
                                                        class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                                                        <i class="ri-add-line mr-1"></i> 添加阈值
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <!-- 延时执行 -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-600 mb-1">延时执行</label>
                                                <div class="flex items-center space-x-2">
                                                    <input type="number" name="cardDelaySeconds" class="border rounded-lg px-3 py-2 w-full" min="0" placeholder="0">
                                                    <span class="whitespace-nowrap">秒</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="button" onclick="addConditionCard()" 
                                    class="mt-4 bg-blue-50 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-100 w-full flex items-center justify-center">
                                    <i class="ri-add-line mr-2"></i> 添加条件卡片
                                </button>
                            </div>
                        </div>

                        <!-- 定时任务特有设置 -->
                        <div id="scheduledTaskSettings" class="space-y-6 hidden">
                            <!-- 执行方式 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行方式</label>
                                <div class="space-y-4">
                                    <!-- 一次 -->
                                    <label class="flex items-center">
                                        <input type="radio" name="scheduleType" value="once" class="text-blue-500 focus:ring-blue-500" onchange="handleScheduleTypeChange('once')">
                                        <span class="ml-2">一次</span>
                                    </label>
                                    <div id="onceTimeSettings" class="ml-6 hidden">
                                        <input type="time" name="onceTime" class="border rounded-lg px-3 py-2">
                                    </div>

                                    <!-- 每日定时 -->
                                    <label class="flex items-center">
                                        <input type="radio" name="scheduleType" value="daily" class="text-blue-500 focus:ring-blue-500" onchange="handleScheduleTypeChange('daily')">
                                        <span class="ml-2">每日定时</span>
                                    </label>
                                    <div id="dailySettings" class="ml-6 space-y-4 hidden">
                                        <!-- 重复方式 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">重复方式</label>
                                            <div class="space-y-2">
                                                <label class="flex items-center">
                                                    <input type="radio" name="repeatType" value="everyday" class="text-blue-500 focus:ring-blue-500" onchange="handleRepeatTypeChange('everyday')">
                                                    <span class="ml-2">每天</span>
                                                </label>
                                                <div id="everydaySettings" class="ml-6 hidden">
                                                    <input type="time" name="everydayTime" class="border rounded-lg px-3 py-2">
                                                </div>

                                                <label class="flex items-center">
                                                    <input type="radio" name="repeatType" value="custom" class="text-blue-500 focus:ring-blue-500" onchange="handleRepeatTypeChange('custom')">
                                                    <span class="ml-2">自定义</span>
                                                </label>
                                                <div id="customSettings" class="ml-6 hidden">
                                                    <div class="grid grid-cols-4 gap-2">
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="1" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周一</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="2" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周二</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="3" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周三</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="4" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周四</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="5" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周五</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="6" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周六</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" name="weekdays" value="0" class="text-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2">周日</span>
                                                        </label>
                                                    </div>
                                                    <div class="mt-2">
                                                        <input type="time" name="customTime" class="border rounded-lg px-3 py-2">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 间隔时间重复 -->
                                    <label class="flex items-center">
                                        <input type="radio" name="scheduleType" value="interval" class="text-blue-500 focus:ring-blue-500" onchange="handleScheduleTypeChange('interval')">
                                        <span class="ml-2">间隔时间重复</span>
                                    </label>
                                    <div id="intervalSettings" class="ml-6 hidden">
                                        <div class="flex items-center space-x-2">
                                            <input type="number" name="intervalValue" class="border rounded-lg px-3 py-2 w-32" min="1">
                                            <select name="intervalUnit" class="border rounded-lg px-3 py-2">
                                                <option value="minute">分钟</option>
                                                <option value="hour">小时</option>
                                                <option value="day">天</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                            <label class="block text-sm font-medium text-gray-700 mb-2">执行设置</label>
                            <div class="space-y-4" id="executeCardList">
                                <!-- 执行卡片 -->
                                <div class="execute-card bg-white border border-green-200 rounded-lg p-4 shadow-sm">
                                    <div class="flex justify-between items-center mb-3">
                                        <h4 class="font-medium text-gray-800">执行卡片</h4>
                                        <button type="button" onclick="removeExecuteCard(this)" class="text-red-500 hover:text-red-700">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                    <div class="space-y-4">
                                        <!-- 设备源选择 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">设备源</label>
                                            <select name="executeDeviceSource" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                                                <option value="">请选择设备</option>
                                                <option value="device1">温度传感器-01</option>
                                                <option value="device2">温度传感器-02</option>
                                                <option value="device3">湿度传感器-01</option>
                                                <option value="device4">湿度传感器-02</option>
                                            </select>
                                        </div>
                                        
                                        <!-- 点位操作设置 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">点位设置</label>
                                            <div class="grid grid-cols-2 gap-2 mb-2">
                                                <select name="executePointName" class="border rounded-lg px-3 py-2" onchange="handlePointTypeChange(this)">
                                                    <option value="temperature">温度</option>
                                                    <option value="humidity">湿度</option>
                                                    <option value="switch">开关</option>
                                                    <option value="text">文本</option>
                                                </select>
                                                
                                                <!-- 布尔值选项(开关) -->
                                                <select name="executeBoolValue" class="border rounded-lg px-3 py-2 point-value boolean">
                                                    <option value="true">开</option>
                                                    <option value="false">关</option>
                                                </select>
                                                
                                                <!-- 数值输入 -->
                                                <input type="number" name="executeNumberValue" class="border rounded-lg px-3 py-2 point-value number hidden" placeholder="数值">
                                                
                                                <!-- 文本输入 -->
                                                <input type="text" name="executeTextValue" class="border rounded-lg px-3 py-2 point-value text hidden" placeholder="文本">
                                            </div>
                                        </div>
                                        
                                        <!-- 延时执行 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">延时执行</label>
                                            <div class="flex items-center space-x-2">
                                                <input type="number" name="executeDelaySeconds" class="border rounded-lg px-3 py-2 w-full" min="0" placeholder="0">
                                                <span class="whitespace-nowrap">秒</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="button" onclick="addExecuteCard()" 
                                class="mt-4 bg-green-50 text-green-600 px-4 py-2 rounded-lg hover:bg-green-100 w-full flex items-center justify-center">
                                <i class="ri-add-line mr-2"></i> 添加执行卡片
                            </button>
                        </div>

                        <!-- 任务描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">任务描述</label>
                            <textarea name="description" rows="4" class="w-full border rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleAddTaskDrawer()" class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitAddTask()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加任务详情抽屉 -->
    <div id="taskDetailDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleTaskDetailDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-2xl w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">温度控制任务</h3>
                    <button onclick="toggleTaskDetailDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">基本信息</h4>
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                <div class="flex">
                                    <span class="text-gray-500 w-24">任务名称：</span>
                                    <span class="text-gray-900">温度控制任务</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">目标类型：</span>
                                    <span class="text-gray-900">设备</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">任务类型：</span>
                                    <span class="text-gray-900">属性下发</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">定时设置：</span>
                                    <span class="text-gray-900">每日定时</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">运行时间：</span>
                                    <span class="text-gray-900">08:00:00</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">状态：</span>
                                    <span class="text-green-600">启用</span>
                                </div>
                            </div>
                        </div>

                        <!-- 命令内容 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">命令内容</h4>
                            <div class="bg-gray-50 rounded-lg overflow-hidden">
                                <div class="bg-gray-100 px-4 py-2 text-sm font-mono border-b">JSON</div>
                                <pre class="p-4 text-sm font-mono overflow-x-auto text-gray-900">{
    "method": "set_temperature",
    "params": {
        "temperature": 25,
        "mode": "auto"
    },
    "id": 1000
}</pre>
                            </div>
                        </div>

                        <!-- 任务描述 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">任务描述</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-gray-900">每日定时设置温度为25度，自动控制模式。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 初始化导航和事件监听
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            document.getElementById('cardViewBtn').classList.add('bg-gray-100', 'text-blue-500');

            // 添加卡片点击事件
            const cardView = document.querySelector('#cardView');
            if (cardView) {
                const cards = cardView.querySelectorAll('.bg-white.rounded-xl');
                cards.forEach(card => {
                    card.addEventListener('click', function(e) {
                        if (!e.target.closest('button')) {
                            toggleTaskDetailDrawer();
                        }
                    });
                });
            }

            // 添加列表行点击事件
            const listView = document.querySelector('#listView');
            if (listView) {
                const rows = listView.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('click', function(e) {
                        if (!e.target.closest('button')) {
                            toggleTaskDetailDrawer();
                        }
                    });
                });
            }
            
            // 初始化阈值项的逻辑操作符
            initializeThresholdItems();
            
            // 默认显示条件任务设置
            handleTaskTypeChange('condition');
            
            // 手动触发一次初始化，确保页面加载后逻辑操作符正确显示
            setTimeout(initializeThresholdItems, 500);
        });

        // 切换视图
        function toggleView(type) {
            const cardView = document.getElementById('cardView');
            const listView = document.getElementById('listView');
            const cardViewBtn = document.getElementById('cardViewBtn');
            const listViewBtn = document.getElementById('listViewBtn');
            
            if (type === 'card') {
                cardView.classList.remove('hidden');
                listView.classList.add('hidden');
                cardViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.remove('bg-white');
                listViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.add('bg-white');
            } else {
                cardView.classList.add('hidden');
                listView.classList.remove('hidden');
                listViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.remove('bg-white');
                cardViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.add('bg-white');
            }
        }

        // 初始化第一个阈值项的逻辑操作符
        function initializeThresholdItems() {
            console.log("初始化阈值项...");
            const thresholdLists = document.querySelectorAll('.thresholdItemList');
            thresholdLists.forEach(list => {
                console.log("找到阈值列表，子项数量:", list.children.length);
                if (list.children.length > 0) {
                    // 隐藏第一个项的逻辑操作符
                    const firstItem = list.children[0];
                    const logicalOperator = firstItem.querySelector('select[name="cardLogicalOperator"]');
                    if (logicalOperator) {
                        console.log("隐藏第一个项的逻辑操作符");
                        logicalOperator.style.visibility = 'hidden';
                    }
                    
                    // 确保其他项的逻辑操作符是可见的
                    for (let i = 1; i < list.children.length; i++) {
                        const item = list.children[i];
                        const logicalOp = item.querySelector('select[name="cardLogicalOperator"]');
                        if (logicalOp) {
                            console.log("显示第", i+1, "个项的逻辑操作符");
                            logicalOp.style.visibility = 'visible';
                        }
                    }
                }
            });
        }

        // 切换添加任务抽屉显示
        function toggleAddTaskDrawer() {
            const drawer = document.getElementById('addTaskDrawer');
            const drawerContent = drawer.querySelector('.max-w-2xl');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 10);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 提交新增任务表单
        function submitAddTask() {
            const form = document.getElementById('addTaskForm');
            const formData = new FormData(form);
            
            // 这里添加表单提交逻辑
            console.log('提交任务表单:', Object.fromEntries(formData));
            
            // 关闭抽屉并重置表单
            toggleAddTaskDrawer();
            form.reset();
        }

        // 切换任务详情抽屉
        function toggleTaskDetailDrawer() {
            const drawer = document.getElementById('taskDetailDrawer');
            const drawerContent = drawer.querySelector('.max-w-2xl');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 处理任务类型变化
        function handleTaskTypeChange(type) {
            const conditionSettings = document.getElementById('conditionTaskSettings');
            const scheduledSettings = document.getElementById('scheduledTaskSettings');
            
            if (type === 'condition') {
                conditionSettings.classList.remove('hidden');
                scheduledSettings.classList.add('hidden');
            } else if (type === 'scheduled') {
                conditionSettings.classList.add('hidden');
                scheduledSettings.classList.remove('hidden');
            }
        }

        // 处理定时任务执行方式变化
        function handleScheduleTypeChange(type) {
            const onceSettings = document.getElementById('onceTimeSettings');
            const dailySettings = document.getElementById('dailySettings');
            const intervalSettings = document.getElementById('intervalSettings');
            
            onceSettings.classList.add('hidden');
            dailySettings.classList.add('hidden');
            intervalSettings.classList.add('hidden');
            
            if (type === 'once') {
                onceSettings.classList.remove('hidden');
            } else if (type === 'daily') {
                dailySettings.classList.remove('hidden');
            } else if (type === 'interval') {
                intervalSettings.classList.remove('hidden');
            }
        }

        // 处理每日定时重复方式变化
        function handleRepeatTypeChange(type) {
            const everydaySettings = document.getElementById('everydaySettings');
            const customSettings = document.getElementById('customSettings');
            
            everydaySettings.classList.add('hidden');
            customSettings.classList.add('hidden');
            
            if (type === 'everyday') {
                everydaySettings.classList.remove('hidden');
            } else if (type === 'custom') {
                customSettings.classList.remove('hidden');
            }
        }

        // 添加条件卡片
        function addConditionCard() {
            const template = document.querySelector('.condition-card').cloneNode(true);
            document.getElementById('conditionCardList').appendChild(template);
        }

        // 删除条件卡片
        function removeConditionCard(button) {
            const cardList = document.getElementById('conditionCardList');
            if (cardList.children.length > 1) {
                button.closest('.condition-card').remove();
            }
        }

        // 添加阈值项
        function addThresholdItem(button) {
            const card = button.closest('.condition-card');
            const thresholdList = card.querySelector('.thresholdItemList');
            const template = thresholdList.querySelector('.threshold-item').cloneNode(true);
            
            // 显示新添加项的逻辑操作符
            const newLogicalOperator = template.querySelector('select[name="cardLogicalOperator"]');
            if (newLogicalOperator) {
                console.log("设置新阈值项的逻辑操作符为可见");
                newLogicalOperator.style.visibility = 'visible';
            }
            
            thresholdList.appendChild(template);
            
            // 确保第一个条件项的逻辑操作符隐藏，其他项显示
            initializeThresholdItems();
        }

        // 添加执行卡片
        function addExecuteCard() {
            const template = document.querySelector('.execute-card').cloneNode(true);
            document.getElementById('executeCardList').appendChild(template);
            
            // 确保新添加的卡片也能正确响应点位类型变化
            const select = template.querySelector('select[name="executePointName"]');
            handlePointTypeChange(select);
        }

        // 删除执行卡片
        function removeExecuteCard(button) {
            const cardList = document.getElementById('executeCardList');
            if (cardList.children.length > 1) {
                button.closest('.execute-card').remove();
            }
        }

        // 处理点位类型变化
        function handlePointTypeChange(select) {
            const card = select.closest('.execute-card');
            const numberInput = card.querySelector('.point-value.number');
            const textInput = card.querySelector('.point-value.text');
            const booleanInput = card.querySelector('.point-value.boolean');
            
            // 隐藏所有输入
            numberInput.classList.add('hidden');
            textInput.classList.add('hidden');
            booleanInput.classList.add('hidden');
            
            // 根据选择显示相应的输入
            if (select.value === 'switch') {
                booleanInput.classList.remove('hidden');
            } else if (select.value === 'text') {
                textInput.classList.remove('hidden');
            } else {
                // 温度、湿度等数值类型
                numberInput.classList.remove('hidden');
            }
        }
    </script>
</body>
</html> 