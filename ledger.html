<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台账管理 - 智慧物联网平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .drawer {
            transition: transform 0.3s ease-in-out;
        }

        /* 表格行悬停效果 */
        .table-row:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        /* 按钮悬停效果 */
        .btn-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        /* 搜索框聚焦效果 */
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        /* 状态标签动画 */
        .status-badge {
            transition: all 0.2s ease;
        }

        .status-badge:hover {
            transform: scale(1.05);
        }

        /* 加载动画 */
        .loading {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 表单验证样式 */
        .form-input:invalid {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input:valid {
            border-color: #10b981;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 台账管理 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-3-line mr-2"></i>
                                <span>台账管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="ledger.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 台账记录
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="asset-type.html" class="flex items-center w-full">
                                    <i class="ri-price-tag-3-line mr-2"></i> 资产类型管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="operation-log.html" class="flex items-center w-full">
                                    <i class="ri-file-text-line mr-2"></i> 操作日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 个人配置 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">台账管理</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">台账记录</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6 space-y-6">
                <!-- 操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">台账管理</h2>
                        <div class="flex space-x-3">
                            <button onclick="importLedger()"
                                class="bg-orange-500 text-white h-10 px-4 rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2">
                                <i class="ri-upload-line text-lg"></i>
                                <span>导入Excel</span>
                            </button>
                            <button onclick="exportLedger()"
                                class="bg-green-500 text-white h-10 px-4 rounded-lg hover:bg-green-600 transition-colors flex items-center space-x-2">
                                <i class="ri-download-line text-lg"></i>
                                <span>导出Excel</span>
                            </button>
                            <button onclick="toggleAddLedgerDrawer()"
                                class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-add-line text-lg"></i>
                                <span>新增台账</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索区域 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="relative">
                            <input id="searchKeyword" type="text" placeholder="搜索名称或编号"
                                class="search-input w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all">
                            <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="正常">正常</option>
                                <option value="维修">维修</option>
                                <option value="报废">报废</option>
                            </select>
                        </div>
                        <div>
                            <input id="startDate" type="date" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex space-x-2">
                            <input id="endDate" type="date" class="flex-1 border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button onclick="searchLedger()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                <i class="ri-search-line"></i>
                            </button>
                            <button onclick="resetSearch()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                                <i class="ri-refresh-line"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 台账列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资产编号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资产名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资产类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属部门</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">到期日期</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="ledgerTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-3 flex items-center justify-between border-t">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                上一页
                            </button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下一页
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示第 <span id="pageStart" class="font-medium">1</span> 到 <span id="pageEnd" class="font-medium">10</span> 条，
                                    共 <span id="totalCount" class="font-medium">50</span> 条记录
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                                    <!-- 分页按钮将通过JavaScript动态生成 -->
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 导入文件输入框（隐藏） -->
    <input type="file" id="importFileInput" accept=".csv,.xlsx,.xls" style="display: none;">

    <!-- 台账详情查看抽屉 -->
    <div id="viewLedgerDrawer" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-[700px] bg-white transform translate-x-full drawer">
            <div class="flex flex-col h-full">
                <!-- 抽屉头部 -->
                <div class="p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="ri-file-list-3-line text-2xl text-blue-600 mr-3"></i>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">台账详情</h3>
                                <p class="text-sm text-gray-500" id="viewAssetCode">资产编号</p>
                            </div>
                        </div>
                        <button onclick="toggleViewLedgerDrawer()" class="text-gray-400 hover:text-gray-600">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div id="viewLedgerContent" class="space-y-6">
                        <!-- 基本信息卡片 -->
                        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <h4 class="text-md font-semibold text-gray-900 flex items-center">
                                    <i class="ri-information-line text-blue-600 mr-2"></i>
                                    基本信息
                                </h4>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">资产编号</label>
                                            <div id="viewDetailAssetCode" class="text-sm text-gray-900 bg-blue-50 p-3 rounded-lg border border-blue-200 font-mono"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">资产类型</label>
                                            <div id="viewDetailAssetType" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">关联设备</label>
                                            <div id="viewDetailRelatedDevice" class="text-sm text-gray-900 bg-indigo-50 p-3 rounded-lg border border-indigo-200 flex items-center">
                                                <i class="ri-device-line text-indigo-600 mr-2"></i>
                                                <span id="viewDetailRelatedDeviceText">无</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">资产名称</label>
                                            <div id="viewDetailAssetName" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border font-medium"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                            <div id="viewDetailStatus" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 归属信息卡片 -->
                        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <h4 class="text-md font-semibold text-gray-900 flex items-center">
                                    <i class="ri-building-line text-green-600 mr-2"></i>
                                    归属信息
                                </h4>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
                                            <div id="viewDetailDepartment" class="text-sm text-gray-900 bg-green-50 p-3 rounded-lg border border-green-200"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">责任人</label>
                                            <div id="viewDetailResponsible" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">存放位置</label>
                                            <div id="viewDetailLocation" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border flex items-center">
                                                <i class="ri-map-pin-line text-gray-400 mr-2"></i>
                                                <span id="viewDetailLocationText"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 采购信息卡片 -->
                        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <h4 class="text-md font-semibold text-gray-900 flex items-center">
                                    <i class="ri-shopping-cart-line text-purple-600 mr-2"></i>
                                    采购信息
                                </h4>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">采购日期</label>
                                            <div id="viewDetailPurchaseDate" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border flex items-center">
                                                <i class="ri-calendar-line text-gray-400 mr-2"></i>
                                                <span id="viewDetailPurchaseDateText"></span>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">供应商</label>
                                            <div id="viewDetailSupplier" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">采购价格</label>
                                            <div id="viewDetailPurchasePrice" class="text-sm text-gray-900 bg-purple-50 p-3 rounded-lg border border-purple-200 flex items-center font-medium">
                                                <i class="ri-money-cny-circle-line text-purple-600 mr-2"></i>
                                                <span id="viewDetailPurchasePriceText"></span>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">到期时间</label>
                                            <div id="viewDetailExpiryDate" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border flex items-center">
                                                <i class="ri-calendar-check-line text-gray-400 mr-2"></i>
                                                <span id="viewDetailExpiryDateText"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 到期提前通知 -->
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">到期提前通知</label>
                                    <div id="viewDetailNotifyDays" class="text-sm text-gray-900 bg-yellow-50 p-3 rounded-lg border border-yellow-200 flex items-center">
                                        <i class="ri-notification-3-line text-yellow-600 mr-2"></i>
                                        <span id="viewDetailNotifyDaysText"></span>
                                    </div>
                                </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 时间信息卡片 -->
                        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <h4 class="text-md font-semibold text-gray-900 flex items-center">
                                    <i class="ri-time-line text-orange-600 mr-2"></i>
                                    时间信息
                                </h4>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                                        <div id="viewDetailCreateTime" class="text-sm text-gray-900 bg-orange-50 p-3 rounded-lg border border-orange-200 flex items-center">
                                            <i class="ri-add-circle-line text-orange-600 mr-2"></i>
                                            <span id="viewDetailCreateTimeText"></span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">更新时间</label>
                                        <div id="viewDetailUpdateTime" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border flex items-center">
                                            <i class="ri-edit-circle-line text-gray-400 mr-2"></i>
                                            <span id="viewDetailUpdateTimeText"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注信息卡片 -->
                        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <h4 class="text-md font-semibold text-gray-900 flex items-center">
                                    <i class="ri-sticky-note-line text-yellow-600 mr-2"></i>
                                    备注信息
                                </h4>
                            </div>
                            <div class="p-6">
                                <div id="viewDetailRemark" class="text-sm text-gray-900 bg-yellow-50 p-4 rounded-lg border border-yellow-200 min-h-[80px] whitespace-pre-wrap"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t bg-gray-50">
                    <div class="flex justify-between">
                        <div class="flex space-x-3">
                            <button onclick="editLedgerFromView()"
                                class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center transition-colors">
                                <i class="ri-edit-line mr-2"></i>
                                编辑台账
                            </button>
                            <button onclick="printLedgerDetail()"
                                class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center transition-colors">
                                <i class="ri-printer-line mr-2"></i>
                                打印详情
                            </button>
                        </div>
                        <button onclick="toggleViewLedgerDrawer()"
                            class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑台账抽屉 -->
    <div id="addLedgerDrawer" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-[600px] bg-white transform translate-x-full drawer">
            <div class="flex flex-col h-full">
                <!-- 抽屉头部 -->
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 id="drawerTitle" class="text-lg font-medium">新增台账</h3>
                        <button onclick="toggleAddLedgerDrawer()" class="text-gray-400 hover:text-gray-600">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="ledgerForm" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>

                            <!-- 资产编号 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    资产编号 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="assetCode" id="assetCode"
                                    class="form-input w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                                    placeholder="请输入资产编号" required>
                            </div>

                            <!-- 资产名称 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    资产名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="assetName" id="assetName"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入资产名称" required>
                            </div>

                            <!-- 资产类型 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    资产类型 <span class="text-red-500">*</span>
                                </label>
                                <select name="assetType" id="assetType"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    <option value="">请选择资产类型</option>
                                    <option value="服务器设备">服务器设备</option>
                                    <option value="网络设备">网络设备</option>
                                    <option value="办公设备">办公设备</option>
                                    <option value="安防设备">安防设备</option>
                                    <option value="其他设备">其他设备</option>
                                </select>
                            </div>

                            <!-- 关联设备 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    关联设备
                                </label>
                                <select name="relatedDevice" id="relatedDevice"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择关联设备</option>
                                    <!-- 设备选项将通过JavaScript动态填充 -->
                                </select>
                            </div>

                            <!-- 状态 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    状态 <span class="text-red-500">*</span>
                                </label>
                                <select name="status" id="status"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    <option value="正常">正常</option>
                                    <option value="维修">维修</option>
                                    <option value="报废">报废</option>
                                </select>
                            </div>
                        </div>

                        <!-- 归属信息 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">归属信息</h4>

                            <!-- 所属部门 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    所属部门 <span class="text-red-500">*</span>
                                </label>
                                <select name="department" id="department"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    <option value="">请选择部门</option>
                                    <option value="信息技术部">信息技术部</option>
                                    <option value="财务部">财务部</option>
                                    <option value="行政部">行政部</option>
                                    <option value="安保部">安保部</option>
                                    <option value="人事部">人事部</option>
                                </select>
                            </div>

                            <!-- 存放位置 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">存放位置</label>
                                <input type="text" name="location" id="location"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入存放位置">
                            </div>

                            <!-- 责任人 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">责任人</label>
                                <input type="text" name="responsible" id="responsible"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入责任人">
                            </div>
                        </div>

                        <!-- 采购信息 -->
                        <div class="border-b pb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-4">采购信息</h4>

                            <!-- 采购日期 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">采购日期</label>
                                <input type="date" name="purchaseDate" id="purchaseDate"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- 采购价格 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">采购价格（元）</label>
                                <input type="number" name="purchasePrice" id="purchasePrice"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入采购价格" min="0" step="0.01">
                            </div>

                            <!-- 供应商 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">供应商</label>
                                <input type="text" name="supplier" id="supplier"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入供应商">
                            </div>

                            <!-- 到期时间 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">到期时间</label>
                                <input type="date" name="expiryDate" id="expiryDate"
                                    class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- 到期提前通知 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    到期提前通知
                                    <span class="text-xs text-gray-500">(1-30天)</span>
                                </label>
                                <div class="flex items-center space-x-2">
                                    <input type="number" name="notifyDays" id="notifyDays"
                                        class="flex-1 border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="请输入天数" min="1" max="30">
                                    <span class="text-sm text-gray-500">天</span>
                                </div>
                                <div id="notifyDaysError" class="text-red-500 text-xs mt-1 hidden">请输入1-30之间的数字</div>
                            </div>
                        </div>

                        <!-- 备注 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                            <textarea name="remark" id="remark" rows="3"
                                class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                placeholder="请输入备注信息"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <button onclick="submitLedgerForm()"
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                        <button onclick="toggleAddLedgerDrawer()"
                            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // ==================== 台账管理数据和逻辑 ====================

        // 模拟台账数据
        let ledgerData = [
            {
                id: 'LD001',
                assetCode: '*********',
                assetName: '服务器-Dell R740',
                assetType: '服务器设备',
                relatedDevice: 'DEVICE_001',
                status: '正常',
                department: '信息技术部',
                location: '机房A-01',
                purchaseDate: '2024-01-15',
                purchasePrice: 25000,
                supplier: '戴尔科技',
                expiryDate: '2027-01-15',
                notifyDays: 7,
                responsible: '张三',
                createTime: '2024-01-15 10:30:00',
                updateTime: '2024-01-15 10:30:00',
                remark: '主要用于数据库服务'
            },
            {
                id: 'LD002',
                assetCode: 'AS2024002',
                assetName: '网络交换机-Cisco 2960',
                assetType: '网络设备',
                relatedDevice: 'DEVICE_002',
                status: '正常',
                department: '信息技术部',
                location: '机房A-02',
                purchaseDate: '2024-01-20',
                purchasePrice: 8000,
                supplier: '思科系统',
                expiryDate: '2026-01-20',
                notifyDays: 15,
                responsible: '李四',
                createTime: '2024-01-20 14:20:00',
                updateTime: '2024-01-20 14:20:00',
                remark: '核心网络设备'
            },
            {
                id: 'LD003',
                assetCode: 'AS2024003',
                assetName: '办公电脑-联想ThinkPad',
                assetType: '办公设备',
                relatedDevice: '',
                status: '正常',
                department: '财务部',
                location: '办公楼3F-305',
                purchaseDate: '2024-02-01',
                purchasePrice: 6500,
                supplier: '联想集团',
                expiryDate: '2025-02-01',
                notifyDays: 30,
                responsible: '王五',
                createTime: '2024-02-01 09:15:00',
                updateTime: '2024-02-01 09:15:00',
                remark: '财务专用办公电脑'
            },
            {
                id: 'LD004',
                assetCode: 'AS2024004',
                assetName: '打印机-HP LaserJet',
                assetType: '办公设备',
                relatedDevice: '',
                status: '维修',
                department: '行政部',
                location: '办公楼2F-201',
                purchaseDate: '2023-12-10',
                purchasePrice: 3200,
                supplier: '惠普公司',
                expiryDate: '2024-12-10',
                notifyDays: 10,
                responsible: '赵六',
                createTime: '2023-12-10 16:45:00',
                updateTime: '2024-03-01 11:20:00',
                remark: '需要更换硒鼓'
            },
            {
                id: 'LD005',
                assetCode: 'AS2024005',
                assetName: '监控摄像头-海康威视',
                assetType: '安防设备',
                relatedDevice: 'DEV001',
                status: '正常',
                department: '安保部',
                location: '大门入口',
                purchaseDate: '2024-01-05',
                purchasePrice: 1200,
                supplier: '海康威视',
                expiryDate: '2026-01-05',
                notifyDays: 5,
                responsible: '孙七',
                createTime: '2024-01-05 13:30:00',
                updateTime: '2024-01-05 13:30:00',
                remark: '主要出入口监控'
            }
        ];

        // 模拟设备数据
        let deviceData = [
            {
                id: 'DEVICE_001',
                name: '温度传感器-01',
                type: '温度传感器',
                group: '温控设备组',
                status: '在线'
            },
            {
                id: 'DEVICE_002',
                name: '湿度传感器-01',
                type: '湿度传感器',
                group: '温控设备组',
                status: '在线'
            },
            {
                id: 'DEVICE_003',
                name: '光照传感器-01',
                type: '光照传感器',
                group: '监控设备组',
                status: '在线'
            },
            {
                id: 'DEVICE_004',
                name: '电力监测器-01',
                type: '电力监测器',
                group: '监控设备组',
                status: '在线'
            },
            {
                id: 'DEVICE_005',
                name: '压力传感器-01',
                type: '压力传感器',
                group: '监控设备组',
                status: '离线'
            },
            {
                id: 'DEV001',
                name: '人脸机-01',
                type: '人脸机',
                group: '门禁设备组',
                status: '在线'
            },
            {
                id: 'DEV002',
                name: '刷卡机-01',
                type: '刷卡机',
                group: '门禁设备组',
                status: '在线'
            },
            {
                id: 'DEV003',
                name: '二维码设备-01',
                type: '二维码设备',
                group: '门禁设备组',
                status: '在线'
            }
        ];

        // 获取设备列表数据
        function getDeviceList() {
            return deviceData.filter(device => device.status === '在线');
        }

        // 分页配置
        let currentPage = 1;
        let pageSize = 10;
        let filteredData = [...ledgerData];

        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置台账管理菜单为展开状态
            const ledgerItem = document.querySelector('.nav-item.active');
            if (ledgerItem) {
                ledgerItem.classList.add('active');
            }

            // 初始化台账列表
            initLedgerList();

            // 初始化关联设备下拉列表
            initRelatedDeviceSelect();

            // 绑定搜索事件
            bindSearchEvents();
        });

        // 初始化台账列表
        function initLedgerList() {
            filteredData = [...ledgerData];
            renderLedgerTable();
            renderPagination();

            // 绑定表单验证事件
            bindFormValidation();
        }

        // 初始化关联设备下拉列表
        function initRelatedDeviceSelect() {
            const relatedDeviceSelect = document.getElementById('relatedDevice');
            if (relatedDeviceSelect) {
                // 清空现有选项（保留默认选项）
                relatedDeviceSelect.innerHTML = '<option value="">请选择关联设备</option>';

                // 获取在线设备列表
                const devices = getDeviceList();

                // 添加设备选项
                devices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.id;
                    option.textContent = `${device.name} (${device.type})`;
                    relatedDeviceSelect.appendChild(option);
                });
            }
        }

        // 绑定表单验证事件
        function bindFormValidation() {
            const notifyDaysInput = document.getElementById('notifyDays');
            if (notifyDaysInput) {
                notifyDaysInput.addEventListener('input', validateNotifyDays);
                notifyDaysInput.addEventListener('blur', validateNotifyDays);
            }
        }

        // 验证到期提前通知天数
        function validateNotifyDays() {
            const input = document.getElementById('notifyDays');
            const errorDiv = document.getElementById('notifyDaysError');
            const value = parseInt(input.value);

            if (input.value && (isNaN(value) || value < 1 || value > 30)) {
                errorDiv.classList.remove('hidden');
                input.classList.add('border-red-500');
                input.classList.remove('border-gray-300');
                return false;
            } else {
                errorDiv.classList.add('hidden');
                input.classList.remove('border-red-500');
                input.classList.add('border-gray-300');
                return true;
            }
        }

        // 渲染台账表格
        function renderLedgerTable() {
            const tbody = document.getElementById('ledgerTableBody');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);

            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <div>暂无台账记录</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = pageData.map((item, index) => `
                <tr class="table-row cursor-pointer">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${startIndex + index + 1}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">${item.assetCode}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.assetName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.assetType}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge px-2 py-1 text-xs font-medium rounded-full ${getStatusClass(item.status)}">
                            ${item.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.department}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm ${getExpiryDateClass(item.expiryDate)}">${formatExpiryDate(item.expiryDate)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                        <button onclick="viewLedger('${item.id}')"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors">
                            <i class="ri-eye-line mr-1"></i>查看
                        </button>
                        <button onclick="editLedger('${item.id}')"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 bg-green-50 rounded hover:bg-green-100 transition-colors">
                            <i class="ri-edit-line mr-1"></i>编辑
                        </button>
                        <button onclick="deleteLedger('${item.id}')"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 transition-colors">
                            <i class="ri-delete-bin-line mr-1"></i>删除
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '正常':
                    return 'bg-green-100 text-green-800';
                case '维修':
                    return 'bg-yellow-100 text-yellow-800';
                case '报废':
                    return 'bg-red-100 text-red-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // 格式化到期日期显示
        function formatExpiryDate(expiryDate) {
            if (!expiryDate) {
                return '<span class="text-gray-400">未设置</span>';
            }

            const today = new Date();
            const expiry = new Date(expiryDate);
            const diffTime = expiry - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays < 0) {
                return `<div>${expiryDate}<br><span class="text-xs text-red-600">已过期 ${Math.abs(diffDays)} 天</span></div>`;
            } else if (diffDays === 0) {
                return `<div>${expiryDate}<br><span class="text-xs text-red-600">今天到期</span></div>`;
            } else if (diffDays <= 7) {
                return `<div>${expiryDate}<br><span class="text-xs text-orange-600">${diffDays} 天后到期</span></div>`;
            } else if (diffDays <= 30) {
                return `<div>${expiryDate}<br><span class="text-xs text-yellow-600">${diffDays} 天后到期</span></div>`;
            } else {
                return `<div>${expiryDate}<br><span class="text-xs text-gray-500">${diffDays} 天后到期</span></div>`;
            }
        }

        // 获取到期日期样式类
        function getExpiryDateClass(expiryDate) {
            if (!expiryDate) {
                return 'text-gray-400';
            }

            const today = new Date();
            const expiry = new Date(expiryDate);
            const diffTime = expiry - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays < 0) {
                return 'text-red-700 font-medium'; // 已过期
            } else if (diffDays === 0) {
                return 'text-red-700 font-medium'; // 今天到期
            } else if (diffDays <= 7) {
                return 'text-orange-700 font-medium'; // 7天内到期
            } else if (diffDays <= 30) {
                return 'text-yellow-700'; // 30天内到期
            } else {
                return 'text-gray-700'; // 正常
            }
        }

        // 渲染分页
        function renderPagination() {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredData.length);

            // 更新统计信息
            document.getElementById('pageStart').textContent = filteredData.length > 0 ? startIndex : 0;
            document.getElementById('pageEnd').textContent = endIndex;
            document.getElementById('totalCount').textContent = filteredData.length;

            // 生成分页按钮
            const pagination = document.getElementById('pagination');
            let paginationHTML = '';

            // 上一页按钮
            paginationHTML += `
                <button onclick="changePage(${currentPage - 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}"
                    ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="ri-arrow-left-s-line"></i>
                </button>
            `;

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button onclick="changePage(${i})"
                            class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                i === currentPage
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += `
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                    `;
                }
            }

            // 下一页按钮
            paginationHTML += `
                <button onclick="changePage(${currentPage + 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}"
                    ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="ri-arrow-right-s-line"></i>
                </button>
            `;

            pagination.innerHTML = paginationHTML;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderLedgerTable();
                renderPagination();
            }
        }

        // 绑定搜索事件
        function bindSearchEvents() {
            // 实时搜索
            document.getElementById('searchKeyword').addEventListener('input', debounce(searchLedger, 300));
            document.getElementById('statusFilter').addEventListener('change', searchLedger);
            document.getElementById('startDate').addEventListener('change', searchLedger);
            document.getElementById('endDate').addEventListener('change', searchLedger);
        }

        // 搜索台账
        function searchLedger() {
            const keyword = document.getElementById('searchKeyword').value.toLowerCase();
            const status = document.getElementById('statusFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            filteredData = ledgerData.filter(item => {
                // 关键词搜索
                const matchKeyword = !keyword ||
                    item.assetName.toLowerCase().includes(keyword) ||
                    item.assetCode.toLowerCase().includes(keyword) ||
                    item.department.toLowerCase().includes(keyword) ||
                    item.responsible.toLowerCase().includes(keyword);

                // 状态筛选
                const matchStatus = !status || item.status === status;

                // 日期筛选
                const itemDate = item.createTime.split(' ')[0];
                const matchStartDate = !startDate || itemDate >= startDate;
                const matchEndDate = !endDate || itemDate <= endDate;

                return matchKeyword && matchStatus && matchStartDate && matchEndDate;
            });

            currentPage = 1;
            renderLedgerTable();
            renderPagination();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';

            filteredData = [...ledgerData];
            currentPage = 1;
            renderLedgerTable();
            renderPagination();
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 当前查看的台账ID
        let currentViewId = null;

        // 查看台账详情
        function viewLedger(id) {
            const item = ledgerData.find(l => l.id === id);
            if (!item) return;

            currentViewId = id;

            // 填充详情信息
            fillLedgerDetailView(item);

            // 打开详情抽屉
            toggleViewLedgerDrawer();
        }

        // 填充台账详情视图
        function fillLedgerDetailView(item) {
            // 头部信息
            document.getElementById('viewAssetCode').textContent = item.assetCode;

            // 基本信息
            document.getElementById('viewDetailAssetCode').textContent = item.assetCode;
            document.getElementById('viewDetailAssetName').textContent = item.assetName;
            document.getElementById('viewDetailAssetType').textContent = item.assetType;

            // 关联设备信息
            const relatedDeviceText = document.getElementById('viewDetailRelatedDeviceText');
            if (item.relatedDevice) {
                const device = deviceData.find(d => d.id === item.relatedDevice);
                if (device) {
                    relatedDeviceText.textContent = `${device.name} (${device.type})`;
                } else {
                    relatedDeviceText.textContent = item.relatedDevice;
                }
            } else {
                relatedDeviceText.textContent = '无';
            }

            // 状态显示
            const statusElement = document.getElementById('viewDetailStatus');
            statusElement.textContent = item.status;
            statusElement.className = `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusClass(item.status)}`;

            // 归属信息
            document.getElementById('viewDetailDepartment').textContent = item.department;
            document.getElementById('viewDetailLocationText').textContent = item.location || '未设置';
            document.getElementById('viewDetailResponsible').textContent = item.responsible || '未指定';

            // 采购信息
            document.getElementById('viewDetailPurchaseDateText').textContent = item.purchaseDate || '未设置';
            document.getElementById('viewDetailPurchasePriceText').textContent = item.purchasePrice ? `¥${item.purchasePrice.toLocaleString()}` : '未设置';
            document.getElementById('viewDetailSupplier').textContent = item.supplier || '未设置';
            document.getElementById('viewDetailExpiryDateText').textContent = item.expiryDate || '未设置';
            document.getElementById('viewDetailNotifyDaysText').textContent = item.notifyDays ? `提前 ${item.notifyDays} 天通知` : '未设置';

            // 时间信息
            document.getElementById('viewDetailCreateTimeText').textContent = item.createTime;
            document.getElementById('viewDetailUpdateTimeText').textContent = item.updateTime;

            // 备注信息
            document.getElementById('viewDetailRemark').textContent = item.remark || '无备注信息';
        }

        // 切换台账详情查看抽屉
        function toggleViewLedgerDrawer() {
            const drawer = document.getElementById('viewLedgerDrawer');
            const drawerContent = drawer.querySelector('.drawer');

            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                    currentViewId = null;
                }, 300);
            }
        }

        // 从查看页面进入编辑模式
        function editLedgerFromView() {
            if (currentViewId) {
                // 关闭查看抽屉
                toggleViewLedgerDrawer();

                // 等待关闭动画完成后打开编辑抽屉
                setTimeout(() => {
                    editLedger(currentViewId);
                }, 300);
            }
        }

        // 打印台账详情
        function printLedgerDetail() {
            if (!currentViewId) return;

            const item = ledgerData.find(l => l.id === currentViewId);
            if (!item) return;

            // 创建打印内容
            const printContent = generatePrintContent(item);

            // 创建新窗口进行打印
            const printWindow = window.open('', '_blank');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
            printWindow.close();
        }

        // 生成打印内容
        function generatePrintContent(item) {
            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>台账详情 - ${item.assetName}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
                        .section { margin-bottom: 20px; }
                        .section-title { font-weight: bold; font-size: 16px; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; margin-bottom: 10px; }
                        .field { margin-bottom: 8px; }
                        .field-label { font-weight: bold; display: inline-block; width: 120px; }
                        .field-value { display: inline-block; }
                        .status { padding: 2px 8px; border-radius: 4px; font-size: 12px; }
                        .status-normal { background-color: #d4edda; color: #155724; }
                        .status-repair { background-color: #fff3cd; color: #856404; }
                        .status-scrap { background-color: #f8d7da; color: #721c24; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>台账详情</h1>
                        <p>资产编号: ${item.assetCode}</p>
                        <p>打印时间: ${new Date().toLocaleString('zh-CN')}</p>
                    </div>

                    <div class="section">
                        <div class="section-title">基本信息</div>
                        <div class="field"><span class="field-label">资产编号:</span><span class="field-value">${item.assetCode}</span></div>
                        <div class="field"><span class="field-label">资产名称:</span><span class="field-value">${item.assetName}</span></div>
                        <div class="field"><span class="field-label">资产类型:</span><span class="field-value">${item.assetType}</span></div>
                        <div class="field"><span class="field-label">状态:</span><span class="field-value status ${getStatusPrintClass(item.status)}">${item.status}</span></div>
                    </div>

                    <div class="section">
                        <div class="section-title">归属信息</div>
                        <div class="field"><span class="field-label">所属部门:</span><span class="field-value">${item.department}</span></div>
                        <div class="field"><span class="field-label">存放位置:</span><span class="field-value">${item.location || '未设置'}</span></div>
                        <div class="field"><span class="field-label">责任人:</span><span class="field-value">${item.responsible || '未指定'}</span></div>
                    </div>

                    <div class="section">
                        <div class="section-title">采购信息</div>
                        <div class="field"><span class="field-label">采购日期:</span><span class="field-value">${item.purchaseDate || '未设置'}</span></div>
                        <div class="field"><span class="field-label">采购价格:</span><span class="field-value">${item.purchasePrice ? '¥' + item.purchasePrice.toLocaleString() : '未设置'}</span></div>
                        <div class="field"><span class="field-label">供应商:</span><span class="field-value">${item.supplier || '未设置'}</span></div>
                        <div class="field"><span class="field-label">到期时间:</span><span class="field-value">${item.expiryDate || '未设置'}</span></div>
                        <div class="field"><span class="field-label">到期提前通知:</span><span class="field-value">${item.notifyDays ? '提前' + item.notifyDays + '天通知' : '未设置'}</span></div>
                    </div>

                    <div class="section">
                        <div class="section-title">时间信息</div>
                        <div class="field"><span class="field-label">创建时间:</span><span class="field-value">${item.createTime}</span></div>
                        <div class="field"><span class="field-label">更新时间:</span><span class="field-value">${item.updateTime}</span></div>
                    </div>

                    <div class="section">
                        <div class="section-title">备注信息</div>
                        <div class="field-value">${item.remark || '无备注信息'}</div>
                    </div>
                </body>
                </html>
            `;
        }

        // 获取状态打印样式类
        function getStatusPrintClass(status) {
            switch (status) {
                case '正常':
                    return 'status-normal';
                case '维修':
                    return 'status-repair';
                case '报废':
                    return 'status-scrap';
                default:
                    return '';
            }
        }

        // 编辑台账
        function editLedger(id) {
            // 这个函数将在后续任务中实现
            console.log('编辑台账:', id);
        }

        // 删除台账
        function deleteLedger(id) {
            const item = ledgerData.find(l => l.id === id);
            if (item && confirm(`确定要删除台账"${item.assetName}"吗？`)) {
                // 逻辑删除
                const index = ledgerData.findIndex(l => l.id === id);
                if (index > -1) {
                    ledgerData.splice(index, 1);
                    searchLedger(); // 重新渲染列表
                    alert('删除成功');
                }
            }
        }

        // 导出Excel
        function exportLedger() {
            if (filteredData.length === 0) {
                alert('没有数据可以导出');
                return;
            }

            // 生成CSV内容
            const csvContent = generateCSV(filteredData);

            // 下载文件
            downloadFile(csvContent, `台账数据_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv;charset=utf-8;');

            alert(`成功导出 ${filteredData.length} 条台账记录`);
        }

        // 生成CSV内容
        function generateCSV(data) {
            // CSV表头
            const headers = [
                '序号', '资产编号', '资产名称', '资产类型', '状态',
                '所属部门', '存放位置', '责任人', '采购日期', '采购价格',
                '供应商', '到期日期', '到期提前通知(天)', '创建时间', '更新时间', '备注'
            ];

            // 构建CSV内容
            let csvContent = '\uFEFF'; // 添加BOM以支持中文
            csvContent += headers.join(',') + '\n';

            data.forEach((item, index) => {
                const row = [
                    index + 1,
                    `"${item.assetCode}"`,
                    `"${item.assetName}"`,
                    `"${item.assetType}"`,
                    `"${item.status}"`,
                    `"${item.department}"`,
                    `"${item.location}"`,
                    `"${item.responsible}"`,
                    `"${item.purchaseDate}"`,
                    item.purchasePrice,
                    `"${item.supplier}"`,
                    `"${item.expiryDate || ''}"`,
                    item.notifyDays || '',
                    `"${item.createTime}"`,
                    `"${item.updateTime}"`,
                    `"${item.remark}"`
                ];
                csvContent += row.join(',') + '\n';
            });

            return csvContent;
        }

        // 下载文件
        function downloadFile(content, fileName, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            window.URL.revokeObjectURL(url);
        }

        // 当前编辑的台账ID
        let currentEditId = null;

        // 新增台账抽屉
        function toggleAddLedgerDrawer() {
            const drawer = document.getElementById('addLedgerDrawer');
            const drawerContent = drawer.querySelector('.drawer');

            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                    resetLedgerForm();
                }, 300);
            }
        }

        // 重置表单
        function resetLedgerForm() {
            document.getElementById('ledgerForm').reset();
            document.getElementById('drawerTitle').textContent = '新增台账';
            currentEditId = null;
        }

        // 提交台账表单
        function submitLedgerForm() {
            const form = document.getElementById('ledgerForm');
            const formData = new FormData(form);

            // 表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 验证到期提前通知天数
            if (!validateNotifyDays()) {
                return;
            }

            // 构建台账数据
            const ledgerItem = {
                id: currentEditId || generateLedgerId(),
                assetCode: formData.get('assetCode'),
                assetName: formData.get('assetName'),
                assetType: formData.get('assetType'),
                relatedDevice: formData.get('relatedDevice') || '',
                status: formData.get('status'),
                department: formData.get('department'),
                location: formData.get('location') || '',
                responsible: formData.get('responsible') || '',
                purchaseDate: formData.get('purchaseDate') || '',
                purchasePrice: parseFloat(formData.get('purchasePrice')) || 0,
                supplier: formData.get('supplier') || '',
                expiryDate: formData.get('expiryDate') || '',
                notifyDays: parseInt(formData.get('notifyDays')) || 0,
                remark: formData.get('remark') || '',
                createTime: currentEditId ?
                    ledgerData.find(l => l.id === currentEditId).createTime :
                    new Date().toLocaleString('zh-CN'),
                updateTime: new Date().toLocaleString('zh-CN')
            };

            // 检查资产编号是否重复
            const existingItem = ledgerData.find(l => l.assetCode === ledgerItem.assetCode && l.id !== currentEditId);
            if (existingItem) {
                alert('资产编号已存在，请使用其他编号');
                return;
            }

            if (currentEditId) {
                // 编辑模式
                const index = ledgerData.findIndex(l => l.id === currentEditId);
                if (index > -1) {
                    ledgerData[index] = ledgerItem;
                    alert('台账更新成功');
                }
            } else {
                // 新增模式
                ledgerData.unshift(ledgerItem);
                alert('台账添加成功');
            }

            // 关闭抽屉并刷新列表
            toggleAddLedgerDrawer();
            searchLedger(); // 重新渲染列表
        }

        // 生成台账ID
        function generateLedgerId() {
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000);
            return `LD${timestamp}${random}`.slice(0, 10);
        }

        // 编辑台账（更新之前的函数）
        function editLedger(id) {
            const item = ledgerData.find(l => l.id === id);
            if (!item) return;

            currentEditId = id;

            // 填充表单
            document.getElementById('assetCode').value = item.assetCode;
            document.getElementById('assetName').value = item.assetName;
            document.getElementById('assetType').value = item.assetType;
            document.getElementById('relatedDevice').value = item.relatedDevice || '';
            document.getElementById('status').value = item.status;
            document.getElementById('department').value = item.department;
            document.getElementById('location').value = item.location;
            document.getElementById('responsible').value = item.responsible;
            document.getElementById('purchaseDate').value = item.purchaseDate;
            document.getElementById('purchasePrice').value = item.purchasePrice;
            document.getElementById('supplier').value = item.supplier;
            document.getElementById('expiryDate').value = item.expiryDate;
            document.getElementById('notifyDays').value = item.notifyDays;
            document.getElementById('remark').value = item.remark;

            // 更新标题
            document.getElementById('drawerTitle').textContent = '编辑台账';

            // 打开抽屉
            toggleAddLedgerDrawer();
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N 新增台账
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                toggleAddLedgerDrawer();
            }

            // Escape 关闭抽屉
            if (e.key === 'Escape') {
                const addDrawer = document.getElementById('addLedgerDrawer');
                const viewDrawer = document.getElementById('viewLedgerDrawer');

                if (!addDrawer.classList.contains('hidden')) {
                    toggleAddLedgerDrawer();
                } else if (!viewDrawer.classList.contains('hidden')) {
                    toggleViewLedgerDrawer();
                }
            }
        });

        // 添加表格行点击事件
        document.addEventListener('click', function(e) {
            const row = e.target.closest('.table-row');
            if (row && !e.target.closest('button')) {
                // 点击表格行时高亮显示
                document.querySelectorAll('.table-row').forEach(r => r.classList.remove('bg-blue-50'));
                row.classList.add('bg-blue-50');
            }
        });

        // 添加加载状态
        function showLoading(message = '加载中...') {
            const tbody = document.getElementById('ledgerTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                        <i class="ri-loader-4-line text-4xl mb-2 loading"></i>
                        <div>${message}</div>
                    </td>
                </tr>
            `;
        }

        // 添加成功提示
        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform';
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="ri-check-line mr-2"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 更新提交函数以使用新的提示
        const originalSubmitLedgerForm = submitLedgerForm;
        submitLedgerForm = function() {
            const form = document.getElementById('ledgerForm');
            const formData = new FormData(form);

            // 表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 显示加载状态
            const submitBtn = document.querySelector('#addLedgerDrawer button[onclick="submitLedgerForm()"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="ri-loader-4-line loading mr-2"></i>保存中...';
            submitBtn.disabled = true;

            // 模拟异步操作
            setTimeout(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // 执行原始逻辑
                originalSubmitLedgerForm();

                // 显示成功提示
                const isEdit = currentEditId !== null;
                showSuccessMessage(isEdit ? '台账更新成功' : '台账添加成功');
            }, 500);
        };

        // ==================== 导入功能 ====================

        // 页面加载完成后绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定文件导入事件
            const fileInput = document.getElementById('importFileInput');
            fileInput.addEventListener('change', handleFileImport);
        });

        // 导入台账数据
        function importLedger() {
            const fileInput = document.getElementById('importFileInput');
            fileInput.click();
        }

        // 处理文件导入
        function handleFileImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 检查文件类型
            const allowedTypes = ['.csv', '.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

            if (!allowedTypes.includes(fileExtension)) {
                showErrorMessage('请选择CSV或Excel文件');
                return;
            }

            // 检查文件大小（限制为5MB）
            if (file.size > 5 * 1024 * 1024) {
                showErrorMessage('文件大小不能超过5MB');
                return;
            }

            // 显示加载状态
            showLoading('正在解析文件...');

            // 模拟文件解析过程
            setTimeout(() => {
                try {
                    // 这里应该是真实的文件解析逻辑
                    // 目前使用模拟数据演示
                    const importedData = simulateFileImport(file);

                    if (importedData.length > 0) {
                        // 显示导入预览
                        showImportPreview(importedData);
                    } else {
                        renderLedgerTable();
                        showErrorMessage('文件中没有有效的台账数据');
                    }
                } catch (error) {
                    renderLedgerTable();
                    showErrorMessage('文件解析失败，请检查文件格式');
                }
            }, 1500);

            // 清空文件输入
            event.target.value = '';
        }

        // 模拟文件导入
        function simulateFileImport(file) {
            // 模拟解析的数据
            return [
                {
                    assetCode: 'AS2024006',
                    assetName: '激光打印机-Canon',
                    assetType: '办公设备',
                    status: '正常',
                    department: '市场部',
                    location: '办公楼3F-310',
                    responsible: '李明',
                    purchaseDate: '2024-03-01',
                    purchasePrice: 4500,
                    supplier: '佳能公司',
                    expiryDate: '2025-03-01',
                    notifyDays: 14,
                    remark: '市场部专用打印机'
                },
                {
                    assetCode: 'AS2024007',
                    assetName: '投影仪-Epson',
                    assetType: '办公设备',
                    status: '正常',
                    department: '会议室',
                    location: '办公楼4F-会议室A',
                    responsible: '张华',
                    purchaseDate: '2024-02-15',
                    purchasePrice: 8900,
                    supplier: '爱普生公司',
                    expiryDate: '2026-02-15',
                    notifyDays: 20,
                    remark: '会议室专用投影设备'
                }
            ];
        }

        // 显示导入预览
        function showImportPreview(importedData) {
            const message = `
                <div class="text-left">
                    <h4 class="font-medium mb-2">导入预览</h4>
                    <p class="text-sm text-gray-600 mb-3">检测到 ${importedData.length} 条台账记录：</p>
                    <ul class="text-sm space-y-1 mb-4">
                        ${importedData.map(item => `<li>• ${item.assetCode} - ${item.assetName}</li>`).join('')}
                    </ul>
                    <div class="flex space-x-3">
                        <button onclick="confirmImport()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                            确认导入
                        </button>
                        <button onclick="cancelImport()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                            取消
                        </button>
                    </div>
                </div>
            `;

            // 存储待导入数据
            window.pendingImportData = importedData;

            // 恢复表格显示
            renderLedgerTable();

            // 显示预览对话框
            showCustomMessage(message);
        }

        // 确认导入
        function confirmImport() {
            if (window.pendingImportData) {
                const importedCount = window.pendingImportData.length;

                // 添加导入的数据到台账列表
                window.pendingImportData.forEach(item => {
                    const ledgerItem = {
                        id: generateLedgerId(),
                        ...item,
                        createTime: new Date().toLocaleString('zh-CN'),
                        updateTime: new Date().toLocaleString('zh-CN')
                    };
                    ledgerData.unshift(ledgerItem);
                });

                // 清空待导入数据
                window.pendingImportData = null;

                // 刷新列表
                searchLedger();

                // 显示成功提示
                showSuccessMessage(`成功导入 ${importedCount} 条台账记录`);

                // 关闭预览对话框
                closeCustomMessage();
            }
        }

        // 取消导入
        function cancelImport() {
            window.pendingImportData = null;
            closeCustomMessage();
        }

        // 显示自定义消息
        function showCustomMessage(htmlContent) {
            const overlay = document.createElement('div');
            overlay.id = 'customMessageOverlay';
            overlay.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center';
            overlay.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    ${htmlContent}
                </div>
            `;
            document.body.appendChild(overlay);
        }

        // 关闭自定义消息
        function closeCustomMessage() {
            const overlay = document.getElementById('customMessageOverlay');
            if (overlay) {
                document.body.removeChild(overlay);
            }
        }
    </script>
</body>
</html>
