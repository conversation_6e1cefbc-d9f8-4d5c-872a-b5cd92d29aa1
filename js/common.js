// 导航菜单交互
function initNavigation() {
    document.querySelectorAll('.nav-item').forEach(item => {
        item.querySelector('div').addEventListener('click', () => {
            // 关闭其他打开的菜单
            document.querySelectorAll('.nav-item').forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
            // 切换当前菜单
            item.classList.toggle('active');
        });
    });
}

// 加载侧边栏
async function loadSidebar() {
    const sidebarContainer = document.getElementById('sidebar-container');
    try {
        // 添加加载提示
        sidebarContainer.innerHTML = '<div class="p-4 text-gray-500">加载中...</div>';
        
        const response = await fetch('./components/sidebar.html');
        if (!response.ok) {
            throw new Error('Failed to load sidebar');
        }
        const html = await response.text();
        sidebarContainer.innerHTML = html;
        initNavigation();
        
        // 根据当前页面设置活动菜单
        const currentPath = window.location.pathname;
        setActiveMenuItem(currentPath);
    } catch (error) {
        console.error('Error loading sidebar:', error);
        sidebarContainer.innerHTML = '<div class="p-4 text-red-500">加载失败，请刷新页面重试</div>';
    }
}

// 设置当前活动菜单项
function setActiveMenuItem(path) {
    const menuItems = document.querySelectorAll('.nav-item');
    menuItems.forEach(item => {
        const menuText = item.querySelector('span').textContent;
        
        // 根据路径判断当前页面
        if (path.includes('workorder')) {
            if (menuText.includes('工单配置')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                const submenuItem = item.querySelector('a[href="workorder.html"]').parentElement;
                submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
            }
        } else if (path.includes('rule-log')) {
            if (menuText.includes('规则管理')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                const submenuItem = item.querySelector('a[href="rule-log.html"]').parentElement;
                submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
            }
        } else if (path.includes('alarm-rule')) {
            if (menuText.includes('报警配置')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                const submenuItem = item.querySelector('a[href="alarm-rule.html"]').parentElement;
                submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
            }
        } else if (path.includes('alarm-record')) {
            if (menuText.includes('报警配置')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                const submenuItem = item.querySelector('a[href="alarm-record.html"]').parentElement;
                submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
            }
        } else if (path.includes('notification-group')) {
            if (menuText.includes('报警配置')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                const submenuItem = item.querySelector('a[href="notification-group.html"]').parentElement;
                submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
            }
        } else if (path.includes('rule.html')) {
            if (menuText.includes('规则管理')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                const submenuItem = item.querySelector('a[href="rule.html"]').parentElement;
                submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
            }
        } else if (path.includes('video-monitor')) {
            if (menuText.includes('视频监控')) {
                item.classList.add('active');
                item.querySelector('div').classList.add('bg-slate-700');
                // 根据具体的视频监控页面设置活动状态
                if (path.includes('index.html') || path.endsWith('video-monitor/')) {
                    const submenuItem = item.querySelector('a[href="video-monitor/index.html"]').parentElement;
                    submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
                } else if (path.includes('playback.html')) {
                    const submenuItem = item.querySelector('a[href="video-monitor/playback.html"]').parentElement;
                    submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
                } else if (path.includes('ai-analysis.html')) {
                    const submenuItem = item.querySelector('a[href="video-monitor/ai-analysis.html"]').parentElement;
                    submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
                } else if (path.includes('alerts.html')) {
                    const submenuItem = item.querySelector('a[href="video-monitor/alerts.html"]').parentElement;
                    submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
                } else if (path.includes('storage.html')) {
                    const submenuItem = item.querySelector('a[href="video-monitor/storage.html"]').parentElement;
                    submenuItem.classList.add('bg-blue-600', 'text-white', 'rounded-md', 'px-2');
                }
            }
        }
        // 可以添加其他页面的判断...
    });
} 