<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警记录管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space-type.html" class="flex items-center w-full">
                                    <i class="ri-layout-line mr-2"></i> 空间类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-building-2-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-git-commit-line mr-2"></i> 规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-3-line mr-2"></i> 规则日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-calendar-todo-line mr-2"></i>
                                <span>任务配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 任务管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="schedule.html" class="flex items-center w-full">
                                    <i class="ri-time-line mr-2"></i> 定时任务
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">报警记录</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">报警记录管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6 space-y-6">
                <!-- 搜索区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6 flex justify-between items-center">
                    <div class="flex space-x-4">
                        <input type="text" placeholder="搜索设备名称" class="border rounded-lg px-3 py-2 w-64">
                        <select class="border rounded-lg px-3 py-2">
                            <option>选择报警等级</option>
                            <option>普通报警</option>
                            <option>重要报警</option>
                            <option>紧急报警</option>
                        </select>
                        <input type="date" class="border rounded-lg px-3 py-2">
                        <span>至</span>
                        <input type="date" class="border rounded-lg px-3 py-2">
                    </div>
                    <div class="flex space-x-4">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">搜索</button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">重置</button>
                    </div>
                </div>

                <!-- 报警记录列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警等级</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警规则</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警内容</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报警时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">重要报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度过高报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">温度传感器-01 超过阈值</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器-01</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 10:30</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800" onclick="confirmGenerateWorkOrder()">生成工单</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">普通报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">湿度异常报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">湿度传感器-02 低于阈值</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">湿度传感器-02</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">湿度</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 09:45</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800" onclick="confirmGenerateWorkOrder()">生成工单</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">紧急报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">设备离线报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">光照传感器-03 设备离线</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">光照传感器-03</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">连接状态</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-14 18:20</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800" onclick="confirmGenerateWorkOrder()">生成工单</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 确认生成工单的弹出框 -->
    <div id="confirmWorkOrderModal" class="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 hidden">
        <div class="bg-white rounded-lg p-6 w-1/3">
            <h3 class="text-lg font-semibold mb-4">确认生成工单</h3>
            <p>您确定要为此报警记录生成工单吗？</p>
            <div class="flex justify-end mt-4">
                <button onclick="generateWorkOrder()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">确认</button>
                <button onclick="closeModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 ml-2">取消</button>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置报警配置菜单为展开状态
            const alarmConfigItem = document.querySelector('.nav-item:nth-child(5)');
            alarmConfigItem.classList.add('active');
        });

        // 确认生成工单的函数
        function confirmGenerateWorkOrder() {
            const modal = document.getElementById('confirmWorkOrderModal');
            modal.classList.remove('hidden');
        }

        // 关闭弹出框
        function closeModal() {
            const modal = document.getElementById('confirmWorkOrderModal');
            modal.classList.add('hidden');
        }

        // 生成工单的逻辑
        function generateWorkOrder() {
            // 这里添加生成工单的逻辑
            console.log('工单已生成');
            closeModal();
        }
    </script>
</body>
</html> 