<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警规则管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .drawer {
            transition: transform 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600">报警配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">报警规则管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6 space-y-6">
                <!-- 操作按钮区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6 flex justify-between items-center">
                    <div class="flex space-x-4">
                        <button onclick="toggleAddRuleDrawer()" 
                            class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                            <i class="ri-add-line text-lg"></i>
                            <span>新增规则</span>
                        </button>
                        <button class="border border-red-500 text-red-500 h-10 px-4 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2">
                            <i class="ri-delete-bin-line text-lg"></i>
                            <span>批量删除</span>
                        </button>
                    </div>
                </div>

                <!-- 规则列表 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备源类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备源</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度过高报警</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">设备</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器-01</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-3 py-1 text-green-600 bg-green-50 rounded-full text-sm font-medium">
                                        启用
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-3">
                                    <button class="text-blue-600 hover:text-blue-800">编辑</button>
                                    <button class="text-red-600 hover:text-red-800">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增规则抽屉 -->
    <div id="addRuleDrawer" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="absolute right-0 top-0 h-full w-[600px] bg-white transform translate-x-full drawer">
            <div class="flex flex-col h-full">
                <!-- 抽屉头部 -->
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium">新增报警规则</h3>
                        <button onclick="toggleAddRuleDrawer()" class="text-gray-400 hover:text-gray-600">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addRuleForm" class="space-y-6">
                        <!-- 规则名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则名称 <span class="text-red-500">*</span></label>
                            <input type="text" name="ruleName" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>

                        <!-- 设备源类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备源类型 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="sourceType" value="device" class="form-radio text-blue-500" required checked>
                                    <span class="ml-2">设备</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="sourceType" value="deviceType" class="form-radio text-blue-500">
                                    <span class="ml-2">设备类型</span>
                                </label>
                            </div>
                        </div>

                        <!-- 触发类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">触发类型 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="triggerType" value="pointChange" class="form-radio text-blue-500" checked onchange="handleTriggerTypeChange('pointChange')" required>
                                    <span class="ml-2">设备点位变化</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="triggerType" value="deviceActive" class="form-radio text-blue-500" onchange="handleTriggerTypeChange('deviceActive')">
                                    <span class="ml-2">设备活跃</span>
                                </label>
                            </div>
                        </div>

                        <!-- 设备源 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备源 <span class="text-red-500">*</span></label>
                            <select name="deviceSource" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择设备源</option>
                                <option value="1">温度传感器-01</option>
                                <option value="2">温度传感器-02</option>
                            </select>
                        </div>

                        <!-- 点位阈值设置 -->
                        <div id="thresholdSection">
                            <label class="block text-sm font-medium text-gray-700 mb-1">点位阈值设置 <span class="text-red-500">*</span></label>
                            <div class="space-y-4" id="thresholdList">
                                <div class="flex items-center space-x-2">
                                    <select name="logicOperator" class="border rounded-lg px-3 py-2 w-24" required>
                                        <option value="AND">AND</option>
                                        <option value="OR">OR</option>
                                    </select>
                                    <select name="pointName" class="border rounded-lg px-3 py-2 w-32" required>
                                        <option value="">请选择</option>
                                        <option value="temperature">温度</option>
                                        <option value="humidity">湿度</option>
                                    </select>
                                    <select name="compareOperator" class="border rounded-lg px-3 py-2 w-40" required>
                                        <option value="">请选择</option>
                                        <option value="gt">大于</option>
                                        <option value="lt">小于</option>
                                        <option value="gte">大于等于</option>
                                        <option value="lte">小于等于</option>
                                        <option value="between">在...之间</option>
                                        <option value="notBetween">不在...之间</option>
                                    </select>
                                    <input type="number" name="thresholdValue" class="border rounded-lg px-3 py-2 w-24" placeholder="数值" required>
                                    <button type="button" onclick="removeThreshold(this)" 
                                        class="text-red-500 hover:text-red-700">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" onclick="addThreshold()" 
                                class="mt-2 text-blue-500 hover:text-blue-700 flex items-center">
                                <i class="ri-add-line mr-1"></i> 添加条件
                            </button>
                        </div>

                        <!-- 重复次数 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                重复次数 <span class="text-red-500">*</span>
                                <button type="button" class="ml-1 text-gray-400 hover:text-gray-600" onclick="showTooltip('repeatCountTooltip')" onmouseleave="hideTooltip('repeatCountTooltip')">
                                    <i class="ri-question-line text-sm"></i>
                                </button>
                                <!-- 提示框 -->
                                <div id="repeatCountTooltip" class="absolute z-10 hidden bg-gray-800 text-white text-xs rounded py-2 px-3 mt-1 max-w-xs">
                                    在有效的时间段内。例如：设置为3次，则只报警3次。
                                    <div class="absolute top-0 left-4 transform -translate-y-1 w-2 h-2 bg-gray-800 rotate-45"></div>
                                </div>
                            </label>
                            <select name="repeatCount" class="w-full border rounded-lg px-3 py-2" required>
                                <option value="1">1次</option>
                                <option value="2">2次</option>
                                <option value="3">3次</option>
                                <option value="4">4次</option>
                                <option value="5">5次</option>
                            </select>
                        </div>

                        <!-- 报警频次 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                报警频次 <span class="text-red-500">*</span>
                                <button type="button" class="ml-1 text-gray-400 hover:text-gray-600" onclick="showTooltip('alarmFrequencyTooltip')" onmouseleave="hideTooltip('alarmFrequencyTooltip')">
                                    <i class="ri-question-line text-sm"></i>
                                </button>
                                <!-- 提示框 -->
                                <div id="alarmFrequencyTooltip" class="absolute z-10 hidden bg-gray-800 text-white text-xs rounded py-2 px-3 mt-1 max-w-xs">
                                    报警触发后的重复通知间隔时间。例如：设置为30分钟，则每30分钟重复发送一次报警通知
                                    <div class="absolute top-0 left-4 transform -translate-y-1 w-2 h-2 bg-gray-800 rotate-45"></div>
                                </div>
                            </label>
                            <div class="flex items-center space-x-2">
                                <input type="number" name="alarmFrequency" class="border rounded-lg px-3 py-2 w-24" min="1" required>
                                <span>分钟</span>
                            </div>
                        </div>

                        <!-- 通知组 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">通知组 <span class="text-red-500">*</span></label>
                            <select name="notificationGroup" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择通知组</option>
                                <option value="1">运维组</option>
                                <option value="2">管理组</option>
                            </select>
                        </div>

                        <!-- 是否转工单 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">是否转工单 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="createWorkOrder" value="yes" class="form-radio text-blue-500" required>
                                    <span class="ml-2">是</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="createWorkOrder" value="no" class="form-radio text-blue-500">
                                    <span class="ml-2">否</span>
                                </label>
                            </div>
                        </div>

                        <!-- 有效时间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">有效时间</label>
                            <div class="flex items-center space-x-2">
                                <label class="flex items-center">
                                    <input type="checkbox" id="enableTimeRange" class="form-checkbox text-blue-500">
                                    <span class="ml-2">启用时间范围</span>
                                </label>
                            </div>
                            <div id="timeRangeSelector" class="mt-2 hidden">
                                <div class="grid grid-cols-24 gap-2">
                                    <!-- 生成24小时选择器 -->
                                    <label class="flex items-center">
                                        <span class="w-16">00:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">01:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">02:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">03:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">04:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">05:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">06:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">07:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">08:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">09:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">10:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">11:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">12:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">13:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">14:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">15:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">16:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">17:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">18:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">19:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">20:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">21:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">22:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                    <label class="flex items-center">
                                        <span class="w-16">23:00</span>
                                        <input type="checkbox" class="form-checkbox text-blue-500">
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 报警上限次数 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                通知上限次数 <span class="text-red-500">*</span>
                                <button type="button" class="ml-1 text-gray-400 hover:text-gray-600" onclick="showTooltip('alarmLimitTooltip')" onmouseleave="hideTooltip('alarmLimitTooltip')">
                                    <i class="ri-question-line text-sm"></i>
                                </button>
                                <!-- 提示框 -->
                                <div id="alarmLimitTooltip" class="absolute z-10 hidden bg-gray-800 text-white text-xs rounded py-2 px-3 mt-1 max-w-xs">
                                    单次报警事件的最大通知次数。例如：设置为5次，则同一个报警事件最多发送5次通知后停止
                                    <div class="absolute top-0 left-4 transform -translate-y-1 w-2 h-2 bg-gray-800 rotate-45"></div>
                                </div>
                            </label>
                            <input type="number" name="alarmLimit" class="w-full border rounded-lg px-3 py-2" min="1" required>
                        </div>

                        <!-- 报警等级 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">报警等级 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="alarmLevel" value="normal" class="form-radio text-blue-500" required checked>
                                    <span class="ml-2">普通</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="alarmLevel" value="important" class="form-radio text-blue-500">
                                    <span class="ml-2">重要</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="alarmLevel" value="urgent" class="form-radio text-blue-500">
                                    <span class="ml-2">紧急</span>
                                </label>
                            </div>
                        </div>

                        <!-- 规则描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则描述</label>
                            <textarea name="description" class="w-full border rounded-lg px-3 py-2 h-24 resize-none"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 抽屉底部 -->
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <button onclick="submitAddRule()" 
                            class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                        <button onclick="toggleAddRuleDrawer()" 
                            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置报警配置菜单为展开状态
            const alarmConfigItem = document.querySelector('.nav-item:nth-child(5)');
            alarmConfigItem.classList.add('active');
            
            // 初始化时间选择器
            initTimeRangeSelector();
        });

        // 切换抽屉
        function toggleAddRuleDrawer() {
            const drawer = document.getElementById('addRuleDrawer');
            const drawerContent = drawer.querySelector('.drawer');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 添加阈值条件
        function addThreshold() {
            const template = document.querySelector('#thresholdList > div').cloneNode(true);
            document.getElementById('thresholdList').appendChild(template);
        }

        // 删除阈值条件
        function removeThreshold(button) {
            const thresholdList = document.getElementById('thresholdList');
            if (thresholdList.children.length > 1) {
                button.closest('div').remove();
            }
        }

        // 初始化时间选择器
        function initTimeRangeSelector() {
            const timeRangeSelector = document.getElementById('timeRangeSelector');
            const enableTimeRange = document.getElementById('enableTimeRange');
            
            enableTimeRange.addEventListener('change', function() {
                timeRangeSelector.classList.toggle('hidden');
                if (!timeRangeSelector.hasChildNodes()) {
                    // 生成24小时选择器
                    const hours = Array.from({length: 24}, (_, i) => i);
                    const hoursHtml = hours.map(hour => {
                        const formattedHour = hour.toString().padStart(2, '0');
                        return `
                            <div class="flex items-center space-x-2">
                                <span class="w-16">${formattedHour}:00</span>
                                <input type="checkbox" class="form-checkbox text-blue-500">
                            </div>
                        `;
                    }).join('');
                    
                    timeRangeSelector.innerHTML = `
                        <div class="grid grid-cols-6 gap-4">
                            ${hoursHtml}
                        </div>
                    `;
                }
            });
        }

        // 处理触发类型变化
        function handleTriggerTypeChange(type) {
            const thresholdSection = document.getElementById('thresholdSection');
            
            if (type === 'deviceActive') {
                thresholdSection.style.display = 'none';
                
                // 当隐藏点位阈值设置时，移除其必填属性
                const requiredInputs = thresholdSection.querySelectorAll('[required]');
                requiredInputs.forEach(input => {
                    input.removeAttribute('required');
                    input.dataset.wasRequired = 'true'; // 记录原来是必填的
                });
            } else {
                thresholdSection.style.display = '';
                
                // 恢复必填属性
                const previouslyRequiredInputs = thresholdSection.querySelectorAll('[data-was-required="true"]');
                previouslyRequiredInputs.forEach(input => {
                    input.setAttribute('required', '');
                });
            }
        }

        // 提交表单前验证
        function validateForm() {
            const form = document.getElementById('addRuleForm');
            const triggerType = form.querySelector('input[name="triggerType"]:checked').value;
            
            // 如果是设备活跃类型，跳过点位阈值验证
            if (triggerType === 'deviceActive') {
                return true;
            }
            
            // 其他验证逻辑...
            return form.checkValidity();
        }

        // 提交表单
        function submitAddRule() {
            if (!validateForm()) {
                alert('请填写所有必填字段');
                return;
            }
            
            const form = document.getElementById('addRuleForm');
            const formData = new FormData(form);
            
            // 这里添加表单提交逻辑
            console.log('提交规则表单:', Object.fromEntries(formData));
            
            // 关闭抽屉
            toggleAddRuleDrawer();
            form.reset();
        }

        // 显示提示框
        function showTooltip(tooltipId) {
            // 隐藏所有其他提示框
            document.querySelectorAll('[id$="Tooltip"]').forEach(tooltip => {
                tooltip.classList.add('hidden');
            });

            // 显示指定的提示框
            const tooltip = document.getElementById(tooltipId);
            if (tooltip) {
                tooltip.classList.remove('hidden');
            }
        }

        // 隐藏提示框
        function hideTooltip(tooltipId) {
            const tooltip = document.getElementById(tooltipId);
            if (tooltip) {
                // 延迟隐藏，给用户时间阅读
                setTimeout(() => {
                    tooltip.classList.add('hidden');
                }, 200);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 确保默认选中设备点位变化
            const pointChangeRadio = document.querySelector('input[name="triggerType"][value="pointChange"]');
            if (pointChangeRadio) {
                pointChangeRadio.checked = true;
            }

            // 点击页面其他地方时隐藏所有提示框
            document.addEventListener('click', function(event) {
                // 如果点击的不是问号按钮或提示框，则隐藏所有提示框
                if (!event.target.closest('[onclick*="showTooltip"]') && !event.target.closest('[id$="Tooltip"]')) {
                    document.querySelectorAll('[id$="Tooltip"]').forEach(tooltip => {
                        tooltip.classList.add('hidden');
                    });
                }
            });
        });
    </script>
</body>
</html> 