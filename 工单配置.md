# 工单配置操作手册

## 目录

### 第一章 工单配置概述
- 1.1 工单配置介绍
- 1.2 功能说明
- 1.3 工单状态

### 第二章 工单列表管理
- 2.1 工单列表查看
- 2.2 工单搜索筛选
- 2.3 工单统计信息

### 第三章 工单创建
- 3.1 新增工单
- 3.2 工单信息填写
- 3.3 工单提交

### 第四章 工单处理
- 4.1 工单处理操作
- 4.2 工单分配
- 4.3 工单状态变更

### 第五章 工单统计
- 5.1 统计数据查看
- 5.2 工单分析
- 5.3 报表功能

### 第六章 常见问题和注意事项
- 6.1 常见问题
- 6.2 注意事项
- 6.3 最佳实践

---

## 第一章 工单配置概述

### 1.1 工单配置介绍

工单配置是物联网平台的重要业务管理模块，用于管理设备维修、故障处理、巡检任务等各类工作任务。通过工单系统，可以规范化管理各类维护工作，提高工作效率和服务质量。

#### 主要功能
- **工单创建**：创建各类维修和维护工单
- **工单分配**：将工单分配给相应的处理人员
- **工单处理**：跟踪和管理工单处理过程
- **工单统计**：统计分析工单处理情况

#### 工单类型
- **设备维修**：设备故障维修工单
- **设备巡检**：定期设备巡检工单
- **环境维护**：环境设施维护工单
- **其他服务**：其他类型服务工单

### 1.2 功能说明

#### 工单管理
- **工单创建**：支持创建各类工单
- **工单编辑**：修改工单基本信息
- **工单删除**：删除不需要的工单
- **工单查询**：按条件查询工单

#### 工单流程
- **工单提交**：提交工单等待处理
- **工单分配**：分配工单给处理人员
- **工单处理**：执行工单任务
- **工单完成**：完成工单并记录结果

### 1.3 工单状态

#### 状态类型
- **🟡 待处理**：新创建的工单，等待分配处理
- **🔵 处理中**：已分配给处理人员，正在处理
- **🟢 已完成**：工单已完成处理
- **🔴 已取消**：工单被取消，不再处理

#### 状态流转
```
待处理 → 处理中 → 已完成
   ↓
已取消
```

#### 状态说明
- **待处理**：工单刚创建，需要分配处理人员
- **处理中**：工单已分配，处理人员正在执行
- **已完成**：工单处理完成，问题已解决
- **已取消**：工单因各种原因被取消

---

## 第二章 工单列表管理

### 2.1 工单列表查看

#### 访问路径
📋 操作步骤：
1. 登录系统后，点击左侧导航栏"工单配置"
2. 进入工单管理页面

#### 列表信息
工单列表显示以下信息：
- **工单编号**：工单的唯一标识
- **报修人**：提交工单的人员
- **报修人电话**：报修人的联系电话
- **所属空间**：工单关联的空间位置
- **处理人**：负责处理工单的人员
- **处理人电话**：处理人的联系电话
- **创建时间**：工单的创建时间
- **状态**：工单的当前状态
- **操作**：处理、分配、详情、删除等操作

#### 状态显示
- **🟡 待处理**：黄色标识，新创建等待处理的工单
- **🔵 处理中**：蓝色标识，正在处理的工单
- **🟢 已完成**：绿色标识，已完成的工单
- **🔴 已取消**：红色标识，已取消的工单

#### 操作功能
- **处理**：开始处理工单
- **分配**：分配工单给其他处理人员
- **详情**：查看工单详细信息
- **删除**：删除工单记录

### 2.2 工单搜索筛选

#### 搜索条件
- **工单编号**：按工单编号精确搜索
- **报修人**：按报修人姓名搜索
- **处理人**：按处理人姓名搜索
- **所属空间**：按空间位置筛选
- **创建时间**：按时间范围筛选
- **工单状态**：按状态筛选工单

#### 操作步骤
📋 操作步骤：
1. 在搜索框中输入搜索条件
2. 选择筛选条件（状态、时间等）
3. 点击"搜索"按钮执行搜索
4. 点击"重置"按钮清空搜索条件

#### 高级筛选
- **时间范围**：支持日期范围选择
- **多条件组合**：支持多个条件同时筛选
- **快速筛选**：提供常用筛选条件快捷按钮

### 2.3 工单统计信息

#### 统计卡片
页面顶部显示工单统计信息：
- **待处理工单**：显示待处理工单数量和变化趋势
- **处理中工单**：显示正在处理的工单数量
- **已完成工单**：显示已完成的工单数量
- **已取消工单**：显示已取消的工单数量

#### 统计数据
- **数量统计**：显示各状态工单的具体数量
- **趋势对比**：显示较昨日的变化情况
- **实时更新**：统计数据实时更新

#### 视图切换
- **卡片视图**：以卡片形式展示工单信息
- **列表视图**：以表格形式展示工单详细信息
- **视图切换**：可在两种视图间自由切换

---

## 第三章 工单创建

### 3.1 新增工单

#### 操作步骤
📋 操作步骤：
1. 在工单管理页面，点击"新增工单"按钮
2. 系统打开新增工单的抽屉式表单
3. 填写工单的基本信息
4. 点击"提交"按钮保存工单

#### 表单界面
- **抽屉式设计**：从右侧滑出的表单界面
- **分步填写**：按步骤引导填写工单信息
- **实时验证**：输入时实时验证数据格式

### 3.2 工单信息填写

#### 必填字段
- **工单标题** *：工单的标题描述
- **报修人** *：提交工单的人员姓名
- **报修人电话** *：报修人的联系电话
- **所属空间** *：选择工单关联的空间
- **问题描述** *：详细描述遇到的问题

#### 可选字段
- **处理人**：指定处理工单的人员
- **优先级**：设置工单的优先级
- **预计完成时间**：预估工单完成时间
- **备注信息**：其他相关说明

#### 附件上传
- **图片上传**：支持上传问题相关图片
- **文件限制**：最多上传5张图片
- **格式支持**：支持JPG、PNG、GIF格式
- **大小限制**：单张图片不超过2MB

#### 验证规则
- **电话格式**：验证手机号码格式
- **必填检查**：确保必填字段不为空
- **字符限制**：描述字段有字符数限制

### 3.3 工单提交

#### 提交流程
📋 操作步骤：
1. 完成所有必填字段的填写
2. 检查信息的准确性
3. 点击"提交"按钮
4. 系统生成工单编号
5. 工单进入待处理状态

#### 提交验证
- **数据完整性**：检查必填字段是否完整
- **格式验证**：验证电话、邮箱等格式
- **重复检查**：避免重复提交相同工单

#### 提交结果
- **成功提示**：显示工单创建成功信息
- **工单编号**：自动生成唯一工单编号
- **状态设置**：工单状态设为"待处理"
- **通知发送**：向相关人员发送通知

⚠️ 注意事项
- 工单编号自动生成，不可修改
- 提交后的工单信息可以编辑
- 重要信息建议上传图片说明
- 及时填写准确的联系方式

---

## 第四章 工单处理

### 4.1 工单处理操作

#### 开始处理
📋 操作步骤：
1. 在工单列表中找到要处理的工单
2. 点击"处理"按钮
3. 工单状态变更为"处理中"
4. 开始执行工单任务

#### 处理记录
- **处理开始时间**：记录开始处理的时间
- **处理人员**：记录实际处理人员
- **处理过程**：记录处理的详细过程
- **处理结果**：记录处理的最终结果

#### 处理状态更新
- **实时更新**：处理过程中实时更新状态
- **进度跟踪**：跟踪工单处理进度
- **时间记录**：记录各阶段的时间节点

### 4.2 工单分配

#### 分配操作
📋 操作步骤：
1. 选择要分配的工单
2. 点击"分配"按钮
3. 选择目标处理人员
4. 填写分配说明
5. 确认分配操作

#### 分配条件
- **人员权限**：只能分配给有权限的人员
- **工作负荷**：考虑处理人员的工作负荷
- **专业技能**：根据问题类型选择合适人员
- **地理位置**：考虑处理人员的地理位置

#### 分配通知
- **即时通知**：分配后立即通知处理人员
- **通知方式**：系统通知、邮件、短信等
- **确认机制**：处理人员需要确认接收工单

### 4.3 工单状态变更

#### 状态流转
工单状态按以下流程变更：
```
待处理 → 处理中 → 已完成
   ↓
已取消
```

#### 状态变更操作
📋 操作步骤：
1. 选择要变更状态的工单
2. 根据实际情况选择目标状态
3. 填写状态变更原因
4. 确认状态变更

#### 完成工单
📋 操作步骤：
1. 完成工单处理任务
2. 点击"完成"按钮
3. 填写处理结果和总结
4. 上传相关图片或文档
5. 确认完成操作

#### 取消工单
📋 操作步骤：
1. 选择要取消的工单
2. 点击"取消"按钮
3. 填写取消原因
4. 确认取消操作

#### 状态变更记录
- **变更时间**：记录状态变更的时间
- **变更人员**：记录执行变更的人员
- **变更原因**：记录状态变更的原因
- **变更历史**：保留完整的变更历史

💡 工单处理最佳实践
- 及时响应和处理工单
- 详细记录处理过程和结果
- 合理分配工单给合适的人员
- 定期跟进工单处理进度

⚠️ 注意事项
- 工单状态变更不可逆，请谨慎操作
- 完成工单前确保问题已彻底解决
- 取消工单需要充分的理由
- 及时更新工单处理状态

---

## 第五章 工单统计

### 5.1 统计数据查看

#### 统计概览
工单管理页面提供实时统计数据：
- **待处理工单数量**：显示当前待处理的工单数量
- **处理中工单数量**：显示正在处理的工单数量
- **已完成工单数量**：显示已完成的工单数量
- **已取消工单数量**：显示已取消的工单数量

#### 趋势分析
- **日环比**：显示较昨日的数量变化
- **增减趋势**：用颜色和图标显示增减趋势
- **实时更新**：数据实时更新，反映最新状态

### 5.2 工单分析

#### 处理效率分析
- **平均处理时间**：统计工单的平均处理时间
- **处理人员效率**：分析各处理人员的工作效率
- **完成率统计**：统计工单的完成率
- **响应时间**：统计工单的平均响应时间

#### 问题类型分析
- **问题分类统计**：按问题类型统计工单数量
- **高频问题**：识别出现频率高的问题
- **解决方案**：总结常见问题的解决方案
- **预防措施**：制定问题预防措施

#### 空间分布分析
- **空间工单统计**：按空间统计工单分布
- **问题热点**：识别问题频发的空间
- **维护重点**：确定需要重点维护的区域

### 5.3 报表功能

#### 报表类型
- **工单汇总报表**：工单的整体统计报表
- **处理人员报表**：按处理人员统计的报表
- **空间分布报表**：按空间分布统计的报表
- **时间趋势报表**：按时间维度的趋势报表

#### 报表生成
📋 操作步骤：
1. 选择报表类型
2. 设置报表时间范围
3. 选择统计维度
4. 点击"生成报表"
5. 查看或导出报表

#### 报表导出
- **Excel格式**：支持导出Excel格式报表
- **PDF格式**：支持导出PDF格式报表
- **图表导出**：支持导出统计图表
- **数据筛选**：支持按条件筛选导出数据

💡 统计分析技巧
- 定期查看统计数据了解工单处理情况
- 分析趋势数据制定改进措施
- 利用报表功能进行深度分析
- 关注异常数据及时处理问题

---

## 第六章 常见问题和注意事项

### 6.1 常见问题

#### 工单创建相关问题

**Q1：为什么无法提交工单？**
A：可能的原因：
- 必填字段未完整填写
- 电话号码格式不正确
- 网络连接问题
- 解决方法：检查必填字段，确认网络连接

**Q2：工单编号是如何生成的？**
A：工单编号由系统自动生成，格式为"WO+年月日+序号"，如WO2024031501。

**Q3：可以上传哪些格式的图片？**
A：支持JPG、PNG、GIF格式，单张图片不超过2MB，最多上传5张。

#### 工单处理相关问题

**Q4：如何修改工单的处理人？**
A：使用工单分配功能，选择新的处理人员并填写分配说明。

**Q5：工单状态可以回退吗？**
A：工单状态变更是单向的，不支持回退。如需修改请联系管理员。

**Q6：如何批量处理工单？**
A：目前系统不支持批量处理，需要逐个处理工单。

#### 工单查询相关问题

**Q7：如何快速查找某个工单？**
A：可以使用工单编号进行精确搜索，或使用报修人、处理人等条件筛选。

**Q8：历史工单数据保存多长时间？**
A：系统会永久保存工单历史数据，可随时查询。

### 6.2 注意事项

#### 工单创建注意事项
- **信息准确性**：确保填写的联系方式准确有效
- **问题描述**：详细描述问题，便于处理人员理解
- **图片上传**：上传清晰的问题图片有助于快速定位
- **空间选择**：准确选择问题所在的空间位置

#### 工单处理注意事项
- **及时响应**：收到工单分配后及时响应
- **状态更新**：及时更新工单处理状态
- **详细记录**：详细记录处理过程和结果
- **完成确认**：确保问题彻底解决后再完成工单

#### 工单管理注意事项
- **权限控制**：确保用户有相应的操作权限
- **数据备份**：重要工单数据建议定期备份
- **流程规范**：建立标准的工单处理流程
- **培训指导**：定期培训用户正确使用系统

#### 系统使用注意事项
- **浏览器兼容**：建议使用Chrome、Firefox等现代浏览器
- **网络稳定**：确保网络连接稳定，避免数据丢失
- **定期清理**：定期清理浏览器缓存
- **密码安全**：定期修改登录密码，确保账户安全

### 6.3 最佳实践

#### 工单创建最佳实践
- **标准化描述**：建立标准的问题描述模板
- **分类管理**：按问题类型进行分类管理
- **优先级设置**：合理设置工单优先级
- **及时提交**：发现问题及时提交工单

#### 工单处理最佳实践
- **快速响应**：建立快速响应机制
- **专业分工**：根据专业技能分配工单
- **过程跟踪**：全程跟踪工单处理过程
- **质量控制**：建立工单处理质量控制机制

#### 工单管理最佳实践
- **流程标准化**：建立标准化的工单流程
- **绩效考核**：建立工单处理绩效考核体系
- **持续改进**：根据统计数据持续改进流程
- **知识积累**：积累常见问题的解决方案

#### 系统维护最佳实践
- **定期检查**：定期检查系统运行状态
- **数据分析**：定期分析工单数据，发现问题趋势
- **用户培训**：定期培训用户提高使用效率
- **系统优化**：根据使用情况优化系统功能

💡 使用技巧
- 使用工单编号可以快速定位工单
- 合理使用筛选条件提高查询效率
- 定期查看统计数据了解工作情况
- 建立工单处理的标准操作流程

⚠️ 重要提醒
- 工单信息涉及隐私，注意信息安全
- 重要工单建议电话确认
- 工单状态变更要谨慎操作
- 定期备份重要工单数据

---

*本手册详细介绍了工单配置模块的各项功能和操作方法，帮助用户更好地使用工单管理功能。*
