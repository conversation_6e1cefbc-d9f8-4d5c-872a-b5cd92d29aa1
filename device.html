<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 text-white bg-blue-600 rounded-md flex items-center cursor-pointer px-2">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-line mr-2"></i> 空间类型管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-building-2-line mr-2"></i> 空间管理
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-team-line mr-2"></i> 通知组管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-alert-line mr-2"></i> 报警规则管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-history-line mr-2"></i> 报警记录管理
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-user-settings-line mr-2"></i> 用户管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-organization-chart-line mr-2"></i> 部门管理
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <i class="ri-shield-user-line mr-2"></i> 角色管理
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">设备配置</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">设备管理</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 搜索和操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <!-- 搜索区域 -->
                    <div class="flex items-center space-x-4 mb-4">
                        <!-- 设备名称搜索 -->
                        <div class="relative">
                            <input type="text" placeholder="设备名称" 
                                class="pl-4 pr-4 py-2 border rounded-lg w-48 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                        </div>
                        <!-- 设备类型选择 -->
                        <select class="pl-4 pr-8 py-2 border rounded-lg w-48 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                            <option value="">设备类型</option>
                            <option value="1">温度传感器</option>
                            <option value="2">湿度传感器</option>
                        </select>
                        <!-- 设备组选择 -->
                        <select class="pl-4 pr-8 py-2 border rounded-lg w-48 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none">
                            <option value="">设备组</option>
                            <option value="1">温控设备组</option>
                            <option value="2">监控设备组</option>
                        </select>
                        <button class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                            <i class="ri-search-line"></i>
                            <span>搜索</span>
                        </button>
                        <button class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                            <i class="ri-refresh-line"></i>
                            <span>重置</span>
                        </button>
                    </div>

                    <!-- 分隔线 -->
                    <div class="border-b mb-4"></div>

                    <!-- 操作按钮区域 -->
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-3 items-center">
                            <!-- 新增设备按钮 -->
                            <button onclick="toggleAddDeviceDrawer()" 
                                class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-add-line text-lg"></i>
                                <span>新增设备</span>
                            </button>

                            <!-- 批量删除按钮 -->
                            <button class="border border-red-500 text-red-500 h-10 px-4 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2">
                                <i class="ri-delete-bin-line text-lg"></i>
                                <span>批量删除</span>
                            </button>
                        </div>

                        <!-- 切换视图按钮组 -->
                        <div class="flex rounded-lg border overflow-hidden">
                            <button onclick="toggleView('card')" 
                                class="h-10 px-4 flex items-center space-x-1 bg-white hover:bg-gray-50 transition-colors border-r" 
                                id="cardViewBtn">
                                <i class="ri-layout-grid-line text-lg"></i>
                                <span class="text-sm">卡片视图</span>
                            </button>
                            <button onclick="toggleView('list')" 
                                class="h-10 px-4 flex items-center space-x-1 bg-white hover:bg-gray-50 transition-colors" 
                                id="listViewBtn">
                                <i class="ri-list-check-2 text-lg"></i>
                                <span class="text-sm">列表视图</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" class="grid grid-cols-3 gap-6">
                    <!-- 设备卡片 -->
                    <div onclick="location.href='device-detail.html'" class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-device-line text-3xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">温度传感器-01</h3>
                                        <p class="text-gray-500 text-sm">ID: DEVICE_001</p>
                                    </div>
                                    <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">在线</span>
                                </div>
                                <p class="text-gray-600 mt-2 text-sm">温控设备组</p>
                                <div class="flex space-x-3 mt-4">
                                    <button onclick="event.stopPropagation(); location.href='device-detail.html'" class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                        <i class="ri-eye-line"></i>
                                        <span>查看</span>
                                    </button>
                                    <button onclick="event.stopPropagation()" class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button onclick="event.stopPropagation()" class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 更多设备卡片... -->
                </div>

                <!-- 列表视图 -->
                <div id="listView" class="hidden">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备编码</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属设备组</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="location.href='device-detail.html'">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">DEVICE_001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温控设备组</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">在线</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                        <button onclick="event.stopPropagation(); location.href='device-detail.html'" class="text-blue-600 hover:text-blue-800">查看</button>
                                        <button onclick="event.stopPropagation()" class="text-green-600 hover:text-green-800">编辑</button>
                                        <button onclick="event.stopPropagation()" class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                                <!-- 更多设备行... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增设备抽屉 -->
    <div id="addDeviceDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddDeviceDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 抽屉头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">新增设备</h3>
                    <button onclick="toggleAddDeviceDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 抽屉内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addDeviceForm" class="space-y-6">
                        <!-- 设备名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备名称 <span class="text-red-500">*</span></label>
                            <input type="text" name="deviceName" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>

                        <!-- 设备编码 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备编码</label>
                            <input type="text" name="deviceCode" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="选填">
                        </div>

                        <!-- 设备类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备类型 <span class="text-red-500">*</span></label>
                            <select name="deviceType" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择设备类型</option>
                                <option value="1">温度传感器</option>
                                <option value="2">湿度传感器</option>
                                <option value="3">光照传感器</option>
                            </select>
                        </div>

                        <!-- 设备组 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备组</label>
                            <select name="deviceGroup" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择设备组</option>
                                <option value="1">温控设备组</option>
                                <option value="2">监控设备组</option>
                            </select>
                        </div>

                        <!-- 所属网关 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属网关 <span class="text-red-500">*</span></label>
                            <select name="gateway" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择网关</option>
                                <option value="1">网关-001</option>
                                <option value="2">网关-002</option>
                            </select>
                        </div>

                        <!-- 所属空间 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属空间</label>
                            <select name="space" class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择空间</option>
                                <option value="1">A栋1层</option>
                                <option value="2">A栋2层</option>
                                <option value="3">B栋1层</option>
                            </select>
                        </div>

                        <!-- 设备状态 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备状态 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="enabled" checked
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">启用</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="disabled"
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">停用</span>
                                </label>
                            </div>
                        </div>

                        <!-- 设备描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备描述</label>
                            <textarea name="description" rows="4"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </form>
                </div>

                <!-- 底部按钮 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleAddDeviceDrawer()" 
                            class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitAddDevice()" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        // 初始化导航
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            // 设置设备配置菜单为展开状态
            const deviceConfigItem = document.querySelector('.nav-item:nth-child(3)');
            deviceConfigItem.classList.add('active');
        });

        // 切换视图
        function toggleView(type) {
            const cardView = document.getElementById('cardView');
            const listView = document.getElementById('listView');
            const cardViewBtn = document.getElementById('cardViewBtn');
            const listViewBtn = document.getElementById('listViewBtn');
            
            if (type === 'card') {
                // 切换到卡片视图
                cardView.classList.remove('hidden');
                listView.classList.add('hidden');
                // 更新按钮状态
                cardViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.remove('bg-white');
                listViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.add('bg-white');
            } else {
                // 切换到列表视图
                cardView.classList.add('hidden');
                listView.classList.remove('hidden');
                // 更新按钮状态
                listViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.remove('bg-white');
                cardViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.add('bg-white');
            }
        }

        // 切换抽屉
        function toggleAddDeviceDrawer() {
            const drawer = document.getElementById('addDeviceDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 页面加载时初始化视图状态
        document.addEventListener('DOMContentLoaded', () => {
            // 默认选中卡片视图
            document.getElementById('cardViewBtn').classList.add('bg-gray-100', 'text-blue-500');
        });

        // 添加提交表单的 JavaScript 函数
        function submitAddDevice() {
            const form = document.getElementById('addDeviceForm');
            const formData = new FormData(form);
            
            // 这里添加表单提交逻辑
            console.log('提交设备表单:', Object.fromEntries(formData));
            
            // 关闭抽屉并重置表单
            toggleAddDeviceDrawer();
            form.reset();
        }
    </script>
</body>
</html> 