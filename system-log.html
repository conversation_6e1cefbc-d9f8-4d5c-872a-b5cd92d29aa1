<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧物联网平台 - 系统日志</title>
    <!-- Tailwind CSS 国内CDN -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Remixicon 国内CDN -->
    <link href="https://cdn.staticfile.org/remixicon/3.5.0/remixicon.min.css" rel="stylesheet">
    <style>
        body { background: #f4f6fa; min-height: 100vh; }
        .sidebar { background: #232946; color: #fff; min-height: 100vh; position: fixed; width: 16rem; }
        .nav-item .nav-submenu { display: none; opacity: 0; transition: all 0.3s; }
        .nav-item.active .nav-submenu { display: block; opacity: 1; }
        .nav-item .nav-arrow { transition: transform 0.3s; }
        .nav-item.active .nav-arrow { transform: rotate(90deg); }
        .nav-submenu { padding-left: 2rem; margin-top: 0.25rem; }
        .nav-submenu li { padding: 0.375rem 0; opacity: 0.7; transition: all 0.2s; }
        .nav-submenu li:hover { opacity: 1; }
        .sidebar a, .sidebar .menu-item { transition: all .2s; padding: 0.75rem 1rem; border-radius: 0.5rem; display: flex; align-items: center; cursor: pointer; }
        .sidebar a:hover, .sidebar .menu-item:hover { background: rgba(59, 130, 246, 0.08); color: #3b82f6; }
        .sidebar a.active, .sidebar a[aria-current="page"] {
            background: #3b82f6;
            color: #fff;
        }
        .main-content { margin-left: 16rem; min-height: 100vh; }
        .card { background: #fff; border-radius: 1rem; box-shadow: 0 4px 24px 0 rgba(0,0,0,0.05); margin-bottom: 1.5rem; transition: all 0.3s ease; }
        .card:hover { box-shadow: 0 6px 30px 0 rgba(0,0,0,0.08); }
        .card-header { border-bottom: 1px solid #f0f0f0; padding: 1.25rem 1.5rem; font-size: 1.1rem; font-weight: 600; display: flex; align-items: center; color: #1f2937; }
        .card-header i { color: #3b82f6; font-size: 1.25rem; margin-right: 0.75rem; background: rgba(59, 130, 246, 0.1); padding: 0.5rem; border-radius: 0.5rem; }
        .card-body { padding: 1.5rem; }
        .top-bar { background: #fff; border-bottom: 1px solid #e5e7eb; padding: 1rem 2rem; position: sticky; top: 0; z-index: 10; }
        .user-info { display: flex; align-items: center; gap: 1rem; }
        .user-name { font-size: 1.1rem; font-weight: 600; color: #1f2937; }
        .user-role { font-size: 0.875rem; color: #6b7280; }
        .table th, .table td { padding: 0.75rem 1rem; text-align: left; }
        .table th { background: #f9fafb; color: #374151; font-weight: 600; }
        .table tbody tr { background: #fff; border-bottom: 1px solid #f3f4f6; transition: background 0.2s; }
        .table tbody tr:hover { background: #f1f5f9; }
        .filter-bar { display: flex; flex-wrap: wrap; gap: 1rem; margin-bottom: 1.5rem; align-items: center; }
        .filter-input, .filter-select { border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 0.5rem 1rem; background: #f9fafb; color: #1f2937; }
        .filter-input:focus, .filter-select:focus { border-color: #3b82f6; outline: none; background: #fff; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
        .btn-main { background: #3b82f6; color: #fff; border-radius: 0.5rem; padding: 0.5rem 1.5rem; font-weight: 500; display: inline-flex; align-items: center; transition: all 0.2s; border: 2px solid transparent; }
        .btn-main:hover { background: #2563eb; transform: translateY(-1px); }
        .btn-main:active { transform: translateY(0); }
        /* 分组展开时不变色，仅箭头旋转和子菜单显示 */
        .nav-item.active > .py-2 {
            background: none;
            color: inherit;
        }
        /* 取消原有的 .sidebar .active 粉色底色 */
        .sidebar .active:not(a) {
            background: none;
            color: inherit;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <aside class="sidebar">
        <div class="p-4">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li>
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li>
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-layout-line mr-2"></i> 空间类型管理
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-building-2-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li>
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li>
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 规则管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-git-branch-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li>
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li>
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                            <li>
                                <a href="system-log.html" class="flex items-center w-full active">
                                    <i class="ri-file-search-line mr-2"></i> 系统日志
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu">
                            <li>
                                <a href="#" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- 个人配置 -->
                    <li class="py-2 px-3 bg-slate-700 rounded-md flex items-center cursor-pointer mt-8">
                        <a href="personal.html" class="flex items-center w-full">
                            <i class="ri-user-settings-line mr-2"></i> 个人配置
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </aside>
    <!-- 主体内容 -->
    <main class="main-content">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <div class="flex justify-between items-center">
                <div class="user-info">
                    <img src="https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=96&q=80" class="avatar" alt="用户头像">
                    <div>
                        <div class="user-name">张三</div>
                        <div class="user-role">管理员</div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 hover:bg-gray-100 rounded-full transition-colors">
                        <i class="ri-notification-3-line text-xl text-gray-500"></i>
                    </button>
                    <button class="p-2 hover:bg-gray-100 rounded-full transition-colors">
                        <i class="ri-settings-3-line text-xl text-gray-500"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- 内容区 -->
        <div class="max-w-6xl mx-auto py-8 px-6">
            <div class="card">
                <div class="card-header">
                    <i class="ri-file-search-line"></i>
                    <span>系统日志</span>
                </div>
                <div class="card-body">
                    <!-- 筛选栏 -->
                    <div class="filter-bar">
                        <input type="text" class="filter-input" placeholder="操作人">
                        <select class="filter-select">
                            <option value="">操作类型</option>
                            <option value="新增">新增</option>
                            <option value="修改">修改</option>
                            <option value="删除">删除</option>
                            <option value="导入">导入</option>
                            <option value="导出">导出</option>
                        </select>
                        <input type="date" class="filter-input" placeholder="开始日期">
                        <span>至</span>
                        <input type="date" class="filter-input" placeholder="结束日期">
                        <button class="btn-main"><i class="ri-search-line mr-2"></i>查询</button>
                    </div>
                    <!-- 日志表格 -->
                    <div class="overflow-x-auto">
                        <table class="table w-full text-sm">
                            <thead>
                                <tr>
                                    <th>操作人</th>
                                    <th>操作类型</th>
                                    <th>操作对象</th>
                                    <th>操作时间</th>
                                    <th>操作详情</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>张三</td>
                                    <td><span class="text-green-600">新增</span></td>
                                    <td>新增</td>
                                    <td>2024-06-01 10:23:45</td>
                                    <td>新增用户：李四</td>
                                </tr>
                                <tr>
                                    <td>李四</td>
                                    <td><span class="text-yellow-600">修改</span></td>
                                    <td>修改</td>
                                    <td>2024-06-01 11:05:12</td>
                                    <td>修改设备名称：温湿度传感器</td>
                                </tr>
                                <tr>
                                    <td>王五</td>
                                    <td><span class="text-red-600">删除</span></td>
                                    <td>删除</td>
                                    <td>2024-06-01 12:30:01</td>
                                    <td>删除空间：会议室A</td>
                                </tr>
                                <tr>
                                    <td>赵六</td>
                                    <td><span class="text-blue-600">导入</span></td>
                                    <td>导入</td>
                                    <td>2024-06-01 13:15:22</td>
                                    <td>导入台账数据：50条记录</td>
                                </tr>
                                <tr>
                                    <td>孙七</td>
                                    <td><span class="text-purple-600">导出</span></td>
                                    <td>导出</td>
                                    <td>2024-06-01 14:20:15</td>
                                    <td>导出设备列表：100条记录</td>
                                </tr>
                                <!-- 可根据实际数据动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script>
        // 侧边栏菜单交互
        document.addEventListener('DOMContentLoaded', function() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                const toggle = item.querySelector('.py-2');
                if (toggle) {
                    toggle.addEventListener('click', () => {
                        item.classList.toggle('active');
                    });
                }
            });
        });
    </script>
</body>
</html> 