<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息规则</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="./styles/common.css" rel="stylesheet">
    <style>
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .nav-item.active .nav-submenu {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }
        .nav-arrow {
            transition: transform 0.3s ease;
        }
        .nav-item.active .nav-arrow {
            transform: rotate(90deg);
        }
        .hover-scale {
            transition: transform 0.2s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-slate-800 text-white p-4 flex flex-col overflow-y-auto">
            <div class="flex items-center space-x-2 mb-6">
                <i class="ri-cloud-line text-2xl text-blue-400"></i>
                <h2 class="text-xl font-bold">智慧物联网平台</h2>
            </div>
            <nav class="flex-1">
                <ul class="space-y-1">
                    <!-- 首页 -->
                    <li class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center cursor-pointer">
                        <a href="index.html" class="flex items-center w-full">
                            <i class="ri-home-line mr-2"></i> 首页
                        </a>
                    </li>
                    
                    <!-- 网关配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-gateway-line mr-2"></i>
                                <span>网关配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="gateway.html" class="flex items-center w-full">
                                    <i class="ri-settings-line mr-2"></i> 网关管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 设备配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-device-line mr-2"></i>
                                <span>设备配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-type.html" class="flex items-center w-full">
                                    <i class="ri-dashboard-line mr-2"></i> 设备类型管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device-group.html" class="flex items-center w-full">
                                    <i class="ri-folder-line mr-2"></i> 设备分组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="device.html" class="flex items-center w-full">
                                    <i class="ri-computer-line mr-2"></i> 设备管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 空间配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-building-line mr-2"></i>
                                <span>空间配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="space.html" class="flex items-center w-full">
                                    <i class="ri-space-line mr-2"></i> 空间管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 工单配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-task-line mr-2"></i>
                                <span>工单配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="workorder.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 工单管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 规则管理 -->
                    <li class="nav-item mt-2 active">
                        <div class="py-2 px-3 bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-file-list-line mr-2"></i>
                                <span>规则管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="rule-log.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 规则日志
                                </a>
                            </li>
                            <li class="py-1.5 bg-blue-600 text-white rounded-md px-2 flex items-center cursor-pointer">
                                <a href="rule.html" class="flex items-center w-full">
                                    <i class="ri-message-2-line mr-2"></i> 消息规则
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 报警配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-alarm-warning-line mr-2"></i>
                                <span>报警配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="notification-group.html" class="flex items-center w-full">
                                    <i class="ri-team-line mr-2"></i> 通知组管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-rule.html" class="flex items-center w-full">
                                    <i class="ri-alert-line mr-2"></i> 报警规则管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="alarm-record.html" class="flex items-center w-full">
                                    <i class="ri-history-line mr-2"></i> 报警记录管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 任务管理 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-timer-line mr-2"></i>
                                <span>任务管理</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task.html" class="flex items-center w-full">
                                    <i class="ri-calendar-todo-line mr-2"></i> 定时任务
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="task-log.html" class="flex items-center w-full">
                                    <i class="ri-file-list-line mr-2"></i> 任务日志
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 可视化大屏配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-dashboard-3-line mr-2"></i>
                                <span>可视化大屏配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="visual.html" class="flex items-center w-full">
                                    <i class="ri-layout-masonry-line mr-2"></i> 组态管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统配置 -->
                    <li class="nav-item mt-2">
                        <div class="py-2 px-3 hover:bg-slate-700 rounded-md flex items-center justify-between cursor-pointer">
                            <div class="flex items-center">
                                <i class="ri-settings-3-line mr-2"></i>
                                <span>系统配置</span>
                            </div>
                            <i class="ri-arrow-right-s-line nav-arrow"></i>
                        </div>
                        <ul class="nav-submenu pl-8 mt-1">
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="user.html" class="flex items-center w-full">
                                    <i class="ri-user-settings-line mr-2"></i> 用户管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="department.html" class="flex items-center w-full">
                                    <i class="ri-organization-chart-line mr-2"></i> 部门管理
                                </a>
                            </li>
                            <li class="py-1.5 hover:text-white flex items-center cursor-pointer">
                                <a href="role.html" class="flex items-center w-full">
                                    <i class="ri-shield-user-line mr-2"></i> 角色管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主体内容 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm sticky top-0 z-10">
                <div class="flex justify-between items-center px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <i class="ri-menu-fold-line text-xl text-gray-500 cursor-pointer hover:text-blue-600 transition-colors"></i>
                        <div class="flex items-center text-gray-600">
                            <span class="text-blue-500 hover:text-blue-600 cursor-pointer">规则管理</span>
                            <i class="ri-arrow-right-s-line mx-2"></i>
                            <span class="font-medium">消息规则</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 搜索和操作区域 -->
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <!-- 搜索区域 -->
                    <div class="flex items-center space-x-4 mb-4">
                        <!-- 规则名称搜索 -->
                        <div class="relative">
                            <input type="text" placeholder="规则名称" 
                                class="pl-4 pr-4 py-2 border rounded-lg w-64 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all">
                        </div>
                        <button class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                            <i class="ri-search-line"></i>
                            <span>搜索</span>
                        </button>
                        <button class="bg-gray-100 text-gray-600 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2">
                            <i class="ri-refresh-line"></i>
                            <span>重置</span>
                        </button>
                    </div>

                    <!-- 分隔线 -->
                    <div class="border-b mb-4"></div>

                    <!-- 操作按钮区域 -->
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-3 items-center">
                            <!-- 新增规则按钮 -->
                            <button onclick="toggleAddRuleDrawer()" 
                                class="bg-blue-500 text-white h-10 px-4 rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                                <i class="ri-add-line text-lg"></i>
                                <span>新增规则</span>
                            </button>

                            <!-- 批量删除按钮 -->
                            <button class="border border-red-500 text-red-500 h-10 px-4 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2">
                                <i class="ri-delete-bin-line text-lg"></i>
                                <span>批量删除</span>
                            </button>
                        </div>

                        <!-- 切换视图按钮组 -->
                        <div class="flex rounded-lg border overflow-hidden">
                            <button onclick="toggleView('card')" 
                                class="h-10 px-4 flex items-center space-x-1 bg-white hover:bg-gray-50 transition-colors border-r" 
                                id="cardViewBtn">
                                <i class="ri-layout-grid-line text-lg"></i>
                                <span class="text-sm">卡片视图</span>
                            </button>
                            <button onclick="toggleView('list')" 
                                class="h-10 px-4 flex items-center space-x-1 bg-white hover:bg-gray-50 transition-colors" 
                                id="listViewBtn">
                                <i class="ri-list-check-2 text-lg"></i>
                                <span class="text-sm">列表视图</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 在操作按钮区域后添加卡片视图部分 -->
                <div id="cardView" class="grid grid-cols-3 gap-6 mt-6">
                    <!-- 规则卡片 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 hover-scale cursor-pointer">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center flex-shrink-0">
                                <i class="ri-git-branch-line text-2xl text-blue-500"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-800">温度超限告警规则</h3>
                                        <p class="text-gray-500 text-sm mt-1">点位上报预处理</p>
                                    </div>
                                    <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">启用</span>
                                </div>
                                <p class="text-gray-600 mt-2 text-sm">设备：温度传感器-01</p>
                                <div class="flex space-x-3 mt-4">
                                    <button onclick="event.stopPropagation(); toggleRuleDetailDrawer()" class="text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                                        <i class="ri-eye-line"></i>
                                        <span>查看</span>
                                    </button>
                                    <button onclick="event.stopPropagation()" class="text-green-600 hover:text-green-800 flex items-center space-x-1">
                                        <i class="ri-edit-line"></i>
                                        <span>编辑</span>
                                    </button>
                                    <button onclick="event.stopPropagation()" class="text-red-600 hover:text-red-800 flex items-center space-x-1">
                                        <i class="ri-delete-bin-line"></i>
                                        <span>删除</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 可以添加更多规则卡片 -->
                </div>

                <!-- 列表视图 -->
                <div id="listView" class="hidden">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="location.href='rule-detail.html'">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度超限告警规则</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温度传感器-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-green-600 bg-green-50 rounded-full text-sm">启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                        <button onclick="event.stopPropagation(); location.href='rule-detail.html'" class="text-blue-600 hover:text-blue-800">查看</button>
                                        <button onclick="event.stopPropagation()" class="text-green-600 hover:text-green-800">编辑</button>
                                        <button onclick="event.stopPropagation()" class="text-red-600 hover:text-red-800">删除</button>
                                    </td>
                                </tr>
                                <!-- 更多规则行... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增规则抽屉 -->
    <div id="addRuleDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleAddRuleDrawer()"></div>
        
        <!-- 抽屉内容 -->
        <div class="absolute inset-y-0 right-0 max-w-lg w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-medium">新增规则</h3>
                        <button onclick="toggleAddRuleDrawer()" class="text-gray-400 hover:text-gray-500">
                            <i class="ri-close-line text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 表单内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <form id="addRuleForm" class="space-y-6">
                        <!-- 规则名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规则名称</label>
                            <input type="text" name="ruleName" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- 规则类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规则类型</label>
                            <select name="ruleType" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择规则类型</option>
                                <option value="report">点位上报预处理</option>
                                <option value="send">点位下发预处理</option>
                            </select>
                        </div>

                        <!-- 设备源类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备源类型</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="sourceType" value="device" checked
                                        onchange="handleSourceTypeChange(this.value)"
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">设备</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="sourceType" value="deviceType"
                                        onchange="handleSourceTypeChange(this.value)"
                                        class="w-4 h-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2">设备类型</span>
                                </label>
                            </div>
                        </div>

                        <!-- 选择来源 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择来源</label>
                            <select name="source" required
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择来源</option>
                            </select>
                        </div>

                        <!-- 操作代码 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">操作</h4>
                            <div class="bg-gray-50 rounded-lg overflow-hidden">
                                <div class="bg-gray-100 px-4 py-2 text-sm font-mono border-b">JavaScript</div>
                                <pre class="p-4 text-sm font-mono overflow-x-auto text-gray-900">
                                    <code id="functionCode" contenteditable="true">module.exports = function(report_attributes) {
    if (report_attributes.temperature > 35 && report_attributes.humidity < 20) {
        // 触发报警逻辑
        return {
            ...report_attributes,
            alarm: true,
            alarmType: 'temperature_humidity_warning'
        };
    }
    return report_attributes;
}</code>
                                </pre>
                            </div>
                        </div>

                        <!-- 测试云函数 -->
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-lg font-medium">测试云函数</h4>
                                <button type="button" onclick="testFunction()" 
                                    class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                    运行测试
                                </button>
                            </div>
                            <div class="space-y-4">
                                <!-- 测试输入 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">测试输入 (JSON)</label>
                                    <div class="relative">
                                        <pre class="p-4 bg-gray-50 rounded-lg text-sm font-mono overflow-x-auto border">
                                            <code id="testInput" contenteditable="true">{
    "temperature": 36,
    "humidity": 18
}</code>
                                        </pre>
                                    </div>
                                </div>
                                
                                <!-- 测试结果 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">测试结果</label>
                                    <pre id="testResult" class="p-4 bg-gray-50 rounded-lg text-sm font-mono overflow-x-auto border min-h-[100px]">
// 测试结果将显示在这里
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 底部按钮 -->
                <div class="px-6 py-4 border-t bg-gray-50">
                    <div class="flex justify-end space-x-4">
                        <button onclick="toggleAddRuleDrawer()" 
                            class="px-4 py-2 border rounded-lg hover:bg-gray-100">
                            取消
                        </button>
                        <button onclick="submitAddRule()" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改规则详情抽屉 - 改为从右边滑出 -->
    <div id="ruleDetailDrawer" class="fixed inset-0 overflow-hidden z-50 hidden">
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="toggleRuleDetailDrawer()"></div>
        
        <!-- 抽屉内容 - 从右边滑出 -->
        <div class="absolute inset-y-0 right-0 max-w-2xl w-full bg-white shadow-xl transform translate-x-full transition-transform duration-300">
            <div class="h-full flex flex-col">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-medium">温度超限告警规则</h3>
                    <button onclick="toggleRuleDetailDrawer()" class="text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-2xl"></i>
                    </button>
                </div>

                <!-- 规则详情内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">基本信息</h4>
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                <div class="flex">
                                    <span class="text-gray-500 w-24">规则名称：</span>
                                    <span class="text-gray-900">温度超限告警规则</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">规则类型：</span>
                                    <span class="text-gray-900">点位上报预处理</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">设备源类型：</span>
                                    <span class="text-gray-900">设备</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">来源：</span>
                                    <span class="text-gray-900">温度传感器-01</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">状态：</span>
                                    <span class="text-green-600">启用</span>
                                </div>
                            </div>
                        </div>

                        <!-- 操作代码 -->
                        <div>
                            <h4 class="text-lg font-medium mb-4">操作</h4>
                            <div class="bg-gray-50 rounded-lg overflow-hidden">
                                <div class="bg-gray-100 px-4 py-2 text-sm font-mono border-b">JavaScript</div>
                                <pre class="p-4 text-sm font-mono overflow-x-auto text-gray-900">module.exports = function(report_attributes) {
    if (report_attributes.temperature > 35 && report_attributes.humidity < 20) {
        // 触发报警逻辑
        return {
            ...report_attributes,
            alarm: true,
            alarmType: 'temperature_humidity_warning'
        };
    }
    return report_attributes;
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            initNavigation();
            setActiveMenuItem(window.location.pathname);
            
            // 默认选中卡片视图
            document.getElementById('cardViewBtn').classList.add('bg-gray-100', 'text-blue-500');

            // 添加卡片点击事件监听
            const cardView = document.querySelector('#cardView');
            if (cardView) {
                const cards = cardView.querySelectorAll('.bg-white.rounded-xl');
                cards.forEach(card => {
                    card.addEventListener('click', function(e) {
                        if (!e.target.closest('button')) {
                            toggleRuleDetailDrawer();
                        }
                    });
                });
            }

            // 添加列表点击事件监听
            const listView = document.querySelector('#listView');
            if (listView) {
                listView.addEventListener('click', function(e) {
                    const row = e.target.closest('tr');
                    if (row && !e.target.closest('button')) {
                        e.preventDefault();
                        toggleRuleDetailDrawer();
                    }
                });
            }
        });

        // 切换视图
        function toggleView(type) {
            const cardView = document.getElementById('cardView');
            const listView = document.getElementById('listView');
            const cardViewBtn = document.getElementById('cardViewBtn');
            const listViewBtn = document.getElementById('listViewBtn');
            
            if (type === 'card') {
                cardView.classList.remove('hidden');
                listView.classList.add('hidden');
                cardViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.remove('bg-white');
                listViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.add('bg-white');
            } else {
                cardView.classList.add('hidden');
                listView.classList.remove('hidden');
                listViewBtn.classList.add('bg-gray-100', 'text-blue-500');
                listViewBtn.classList.remove('bg-white');
                cardViewBtn.classList.remove('bg-gray-100', 'text-blue-500');
                cardViewBtn.classList.add('bg-white');
            }
        }

        // 切换抽屉
        function toggleAddRuleDrawer() {
            const drawer = document.getElementById('addRuleDrawer');
            const drawerContent = drawer.querySelector('.max-w-lg');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 处理设备源类型变化
        function handleSourceTypeChange(type) {
            const sourceSelect = document.querySelector('select[name="source"]');
            sourceSelect.innerHTML = '<option value="">请选择来源</option>';
            
            if (type === 'device') {
                // 添加设备选项
                const devices = [
                    { id: 1, name: '温度传感器-01' },
                    { id: 2, name: '温度传感器-02' }
                ];
                devices.forEach(device => {
                    sourceSelect.add(new Option(device.name, device.id));
                });
            } else {
                // 添加设备类型选项
                const deviceTypes = [
                    { id: 1, name: '温度传感器' },
                    { id: 2, name: '湿度传感器' }
                ];
                deviceTypes.forEach(type => {
                    sourceSelect.add(new Option(type.name, type.id));
                });
            }
        }

        // 提交表单
        function submitAddRule() {
            const form = document.getElementById('addRuleForm');
            const formData = new FormData(form);
            
            // 这里添加表单提交逻辑
            console.log('提交规则表单:', Object.fromEntries(formData));
            
            // 关闭抽屉并重置表单
            toggleAddRuleDrawer();
            form.reset();
        }

        // 切换规则详情抽屉
        function toggleRuleDetailDrawer() {
            const drawer = document.getElementById('ruleDetailDrawer');
            const drawerContent = drawer.querySelector('.max-w-2xl');
            
            if (drawer.classList.contains('hidden')) {
                drawer.classList.remove('hidden');
                setTimeout(() => {
                    drawerContent.classList.remove('translate-x-full');
                }, 0);
            } else {
                drawerContent.classList.add('translate-x-full');
                setTimeout(() => {
                    drawer.classList.add('hidden');
                }, 300);
            }
        }

        // 测试云函数
        function testFunction() {
            try {
                // 获取函数代码和测试输入
                const functionCode = document.getElementById('functionCode').textContent;
                const testInput = JSON.parse(document.getElementById('testInput').textContent);
                
                // 创建函数
                const AsyncFunction = Object.getPrototypeOf(async function(){}).constructor;
                const moduleExports = {};
                const fn = new AsyncFunction('module', 'exports', functionCode);
                fn(moduleExports, moduleExports);
                
                // 执行函数
                const result = moduleExports.exports(testInput);
                
                // 显示结果
                document.getElementById('testResult').innerHTML = 
                    `// 执行成功\n${JSON.stringify(result, null, 2)}`;
                document.getElementById('testResult').classList.remove('text-red-500');
                document.getElementById('testResult').classList.add('text-green-600');
            } catch (error) {
                // 显示错误
                document.getElementById('testResult').innerHTML = 
                    `// 执行错误\n${error.message}`;
                document.getElementById('testResult').classList.remove('text-green-600');
                document.getElementById('testResult').classList.add('text-red-500');
            }
        }

        // 让代码框支持tab键
        document.querySelectorAll('pre code[contenteditable="true"]').forEach(element => {
            element.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    e.preventDefault();
                    document.execCommand('insertText', false, '    ');
                }
            });
        });
    </script>
</body>
</html> 